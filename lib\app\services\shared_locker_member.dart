import 'package:get/get.dart';
import 'package:ironlocker/app/constants/endpoints.dart';
import 'package:ironlocker/app/helpers/device_info.dart';
import 'package:ironlocker/app/helpers/storage.dart';
import 'package:ironlocker/app/stores/user.dart';

class SharedLockerMemberService extends GetConnect {
  final endPoints = ApiEndpoints();

  @override
  void onInit() {
    httpClient.timeout = const Duration(seconds: 30);
    httpClient.addRequestModifier<dynamic>((request) async {
      var device = await DeviceInfo.device();
      request.headers['User-Agent'] = device;

      request.headers['Authorization'] =
          "Bearer ${UserStore.to.accessToken.value}";
      return request;
    });

    httpClient.addAuthenticator<dynamic>((request) async {
      final storage = Storage();

      final response = await post("${endPoints.auth}/refresh-token", {
        "refreshToken": UserStore.to.refreshToken.value,
      });

      final accessToken = response.body['accessToken'];
      final refreshToken = response.body['refreshToken'];

      await storage.setItem(key: "accessToken", value: accessToken);
      await storage.setItem(key: "refreshToken", value: refreshToken);

      UserStore.to.accessToken(accessToken);
      UserStore.to.refreshToken(refreshToken);

      // Set the header
      request.headers['Authorization'] = "Bearer $accessToken";
      return request;
    });

    httpClient.maxAuthRetries = 3;

    super.onInit();
  }

  Future<Response> getMembers(String sharedLockerId) {
    return get("${endPoints.sharedLockers}/shared-locker/$sharedLockerId");
  }

  Future<Response> removeMember(String membershipId) {
    return delete("${endPoints.sharedLockerMembers}/$membershipId");
  }

  Future<Response> addMember({
    required String email,
    required String sharedLockerId,
    required String permission,
    required String key,
  }) {
    return post(endPoints.sharedLockerMembers, {
      "email": email,
      "permission": permission,
      "sharedLockerId": sharedLockerId,
      "key": key,
    });
  }

  Future<Response> updatePermission({
    required String membershipId,
    required String permission,
  }) {
    return patch("${endPoints.sharedLockerMembers}/$membershipId/permission", {
      "permission": permission,
    });
  }
}
