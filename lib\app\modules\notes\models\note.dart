class Note {
  final String id;
  final String name;
  final String? folder;
  final String itemType;
  final String? content;
  final bool favourite;

  Note({
    required this.id,
    required this.name,
    required this.favourite,
    required this.itemType,
    this.content,
    this.folder,
  });

  static Note fromJSON(data) {
    return Note(
      id: data['_id'],
      name: data['name'],
      folder: data['folder'],
      itemType: data['itemType'],
      content: data['content'],
      favourite: data['favourite'],
    );
  }
}
