import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/ad_free_widget.dart';
import 'package:ironlocker/app/components/banner_add.dart';
import 'package:ironlocker/app/components/upgrade_button.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/modules/settings/controller/index.dart';
import 'package:ironlocker/app/routes/app_pages.dart';

import '../../../components/locker_app_bar.dart';
import '../../../themes/app_bar.dart';

class SettingsPage extends GetView<SettingsController> {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        Get.toNamed(Routes.home);
      },
      child: Scaffold(
        appBar: LockerAppBar(
          title: "Settings",
          page: "settings",
          showSearchIcon: false,
          showAddLockerItemButton: false,
          leading: AppbarTheme.buildStyledBackButton(
            onPressed: () {
              Get.toNamed(Routes.home);
            },
            tooltip: 'Back to Home',
          ),
        ),
        body: Column(
          children: [
            Expanded(
              child: Container(
                constraints: const BoxConstraints(maxWidth: 480),
                child: ListView(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  children: [
                    // Premium Upgrade Section (Top Priority)
                    const UpgradeButton(),

                    // Account Section
                    _buildSectionHeader(context, "Account"),
                    _buildSettingsCard(
                      context,
                      children: [
                        _buildSettingsTile(
                          context,
                          icon: Icons.person_outline,
                          title: "Profile",
                          subtitle: "Name, email, and personal info",
                          onTap: () => Get.toNamed(Routes.PROFILE),
                        ),
                        _buildDivider(),
                        _buildSettingsTile(
                          context,
                          icon: Icons.lock_outline,
                          title: "Security",
                          subtitle: "Password, 2FA, and security settings",
                          onTap: () => Get.toNamed(Routes.SECURITY),
                        ),
                        _buildDivider(),
                        _buildSettingsTile(
                          context,
                          icon: Icons.access_time_outlined,
                          title: "Active Sessions",
                          subtitle: "Manage your active login sessions",
                          onTap: () => Get.toNamed(Routes.SESSIONS),
                        ),
                      ],
                    ),

                    // Preferences Section
                    _buildSectionHeader(context, "Preferences"),
                    _buildSettingsCard(
                      context,
                      children: [
                        _buildSettingsTile(
                          context,
                          icon: Icons.palette_outlined,
                          title: "Theme",
                          subtitle: "Light, dark, or system theme",
                          onTap: () => DialogHelper.selectTheme(),
                          showArrow: false,
                        ),
                        Obx(() {
                          if (!controller.biometricAvailable.value) {
                            return const SizedBox.shrink();
                          }
                          return Column(
                            children: [
                              _buildDivider(),
                              _buildBiometricTile(context),
                            ],
                          );
                        }),
                      ],
                    ),

                    // Ad-Free Experience Card
                    const AdFreeCard(),

                    // Add some bottom padding
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
            // AdMob Banner Ad at the bottom
            Container(
              color: Get.isDarkMode ? Colors.grey[900] : Colors.grey[50],
              child: SafeArea(
                top: false,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: BannerAdWidget(
                    useTestAds: kDebugMode, // Use test ads in debug mode
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 24, 24, 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  Widget _buildSettingsCard(
    BuildContext context, {
    required List<Widget> children,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(children: children),
    );
  }

  Widget _buildSettingsTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool showArrow = true,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color:
                      Get.isDarkMode
                          ? Colors.grey[700]?.withValues(alpha: 0.5)
                          : Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 20,
                  color: Get.isDarkMode ? Colors.grey[300] : Colors.grey[700],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Get.isDarkMode ? Colors.white : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 13,
                        color:
                            Get.isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              if (showArrow)
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Get.isDarkMode ? Colors.grey[500] : Colors.grey[400],
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBiometricTile(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          controller.toggleBiometric(!controller.biometricEnabled.value);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color:
                      Get.isDarkMode
                          ? Colors.grey[700]?.withValues(alpha: 0.5)
                          : Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.fingerprint,
                  size: 20,
                  color: Get.isDarkMode ? Colors.grey[300] : Colors.grey[700],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Biometric Authentication",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Get.isDarkMode ? Colors.white : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      "Use fingerprint or face unlock",
                      style: TextStyle(
                        fontSize: 13,
                        color:
                            Get.isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Obx(
                () => AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  width: 50,
                  height: 30,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    color:
                        controller.biometricEnabled.value
                            ? Colors.blue
                            : (Get.isDarkMode
                                ? Colors.grey[700]
                                : Colors.grey[300]),
                  ),
                  child: AnimatedAlign(
                    duration: const Duration(milliseconds: 200),
                    alignment:
                        controller.biometricEnabled.value
                            ? Alignment.centerRight
                            : Alignment.centerLeft,
                    child: Container(
                      width: 26,
                      height: 26,
                      margin: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      height: 1,
      color: Get.isDarkMode ? Colors.grey[700] : Colors.grey[200],
    );
  }
}
