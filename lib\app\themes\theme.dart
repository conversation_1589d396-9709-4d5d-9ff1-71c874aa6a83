import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:ironlocker/app/constants/colors.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/themes/app_bar.dart';
import 'package:ironlocker/app/themes/text.dart';

class AppTheme {
  static final lightTheme = ThemeData(
    brightness: Brightness.light,
    useMaterial3: true,
    fontFamily: GoogleFonts.jura().fontFamily,
    appBarTheme: AppbarTheme.lightTheme,
    textTheme: AppTextTheme.lightTheme,
    scaffoldBackgroundColor: IronLockerColors.backgroundLight,
    primaryColor: IronLockerColors.blue02,
    colorScheme: ColorScheme.light(
      primary: IronLockerColors.blue02,
      secondary: IronLockerColors.yellow,
      surface: IronLockerColors.white01,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: IronLockerColors.foreground,
    ),
    radioTheme: RadioThemeData(
      fillColor: WidgetStateProperty.resolveWith<Color>((
        Set<WidgetState> states,
      ) {
        if (states.contains(WidgetState.disabled)) {
          return IronLockerColors.blue02.withValues(alpha: 0.32);
        }
        if (states.contains(WidgetState.selected)) {
          return IronLockerColors.blue02;
        }
        return IronLockerColors.blueGray;
      }),
    ),
    textSelectionTheme: TextSelectionThemeData(
      selectionHandleColor: IronLockerColors.blue02,
      selectionColor: IronLockerColors.blue02.withValues(alpha: 0.3),
      cursorColor: IronLockerColors.blue02,
    ),
    cardTheme: CardThemeData(
      color: IronLockerColors.white01,
      elevation: 0.5,
      shadowColor: IronLockerColors.blue01.withValues(alpha: 0.1),
    ),
    bottomSheetTheme: BottomSheetThemeData(
      backgroundColor: IronLockerColors.backgroundLight,
    ),
    drawerTheme: DrawerThemeData(backgroundColor: IronLockerColors.white01),
    popupMenuTheme: PopupMenuThemeData(color: IronLockerColors.backgroundLight),
  );

  static final darkTheme = ThemeData(
    brightness: Brightness.dark,
    useMaterial3: true,
    fontFamily: GoogleFonts.jura().fontFamily,
    appBarTheme: AppbarTheme.darkTheme,
    textTheme: AppTextTheme.darkTheme,
    scaffoldBackgroundColor: IronLockerColors.backgroundDark,
    primaryColor: IronLockerColors.blue02,
    colorScheme: ColorScheme.dark(
      primary: IronLockerColors.blue02,
      secondary: IronLockerColors.yellow,
      surface: IronLockerColors.cardDark,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: Colors.white,
    ),
    cardTheme: CardThemeData(
      color: IronLockerColors.cardDark,
      shadowColor: IronLockerColors.blue01.withValues(alpha: 0.2),
    ),
    drawerTheme: DrawerThemeData(
      backgroundColor: IronLockerColors.backgroundDark,
    ),
    popupMenuTheme: PopupMenuThemeData(color: IronLockerColors.backgroundDark),
    textSelectionTheme: TextSelectionThemeData(
      selectionHandleColor: IronLockerColors.blue02,
      selectionColor: IronLockerColors.blue02.withValues(alpha: 0.3),
      cursorColor: IronLockerColors.blue02,
    ),
    radioTheme: RadioThemeData(
      fillColor: WidgetStateProperty.resolveWith<Color>((
        Set<WidgetState> states,
      ) {
        if (states.contains(WidgetState.disabled)) {
          return IronLockerColors.blue02.withValues(alpha: 0.32);
        }
        if (states.contains(WidgetState.selected)) {
          return IronLockerColors.blue02;
        }
        return Colors.white70;
      }),
    ),
    dialogTheme: DialogThemeData(
      backgroundColor: IronLockerColors.backgroundDark,
    ),
    bottomSheetTheme: BottomSheetThemeData(
      backgroundColor: IronLockerColors.backgroundDark,
    ),
  );
}
