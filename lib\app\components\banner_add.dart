import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:ironlocker/app/controllers/ad_free_controller.dart';
import 'package:ironlocker/app/helpers/ad_helper.dart';
import 'package:ironlocker/app/helpers/ad_manager.dart';
import 'package:ironlocker/app/stores/user.dart';

class BannerAdWidget extends StatefulWidget {
  final AdSize adSize;
  final bool useTestAds;

  // Constructor now uses AdHelper for production ads
  // Set useTestAds to true for development/testing
  const BannerAdWidget({
    super.key,
    this.adSize = AdSize.banner,
    this.useTestAds = false, // Set to true for testing
  });

  String get adUnitId =>
      useTestAds ? AdHelper.testBannerAdUnitId : AdHelper.bannerAdUnitId;

  @override
  State<BannerAdWidget> createState() => _BannerAdWidgetState();
}

class _BannerAdWidgetState extends State<BannerAdWidget> {
  /// The banner ad to show. This is `null` until the ad is actually loaded.
  BannerAd? _bannerAd;
  bool _isLoading = true;
  bool _hasError = false;
  int _retryCount = 0;
  static const int _maxRetries = 5; // Increased retry attempts
  String? _errorMessage;
  bool _isDisposed = false;

  @override
  Widget build(BuildContext context) {
    // Use GetX Obx for real-time ad-free status updates
    return Obx(() {
      // Hide completely when user has premium or lifetime plan
      if (UserStore.to.plan["name"] == "premium" ||
          UserStore.to.plan["name"] == "lifetime") {
        return const SizedBox.shrink();
      }

      // Hide completely when ad-free is active - no loading, no error states, nothing
      if (AdFreeController.to.isAdFree) {
        return const SizedBox.shrink();
      }

      return _buildAdContent();
    });
  }

  Widget _buildAdContent() {
    if (_hasError) {
      // Show a subtle placeholder instead of nothing when ads fail
      return Container(
        width: widget.adSize.width.toDouble(),
        height: widget.adSize.height.toDouble(),
        margin: const EdgeInsets.symmetric(vertical: 8.0),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.grey[300]!, width: 0.5),
        ),
        child: Tooltip(
          message: _errorMessage ?? 'Ad failed to load',
          child: InkWell(
            onTap: _retryLoadAd,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.refresh, color: Colors.grey[600], size: 16),
                  const SizedBox(height: 4),
                  Text(
                    'Tap to retry',
                    style: TextStyle(color: Colors.grey[600], fontSize: 10),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }

    return SafeArea(
      child: SizedBox(
        width: widget.adSize.width.toDouble(),
        height: widget.adSize.height.toDouble(),
        child:
            _bannerAd == null
                ? (_isLoading
                    ? const Center(
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                    : const SizedBox.shrink())
                : AdWidget(ad: _bannerAd!),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _checkAndLoadAd();
    // No need for periodic checking - GetX handles real-time updates
  }

  void _checkAndLoadAd() async {
    // Check if user has premium or lifetime plan
    if (UserStore.to.plan["name"] == "premium" ||
        UserStore.to.plan["name"] == "lifetime") {
      debugPrint(
        '🚫 Banner ad completely hidden - user has premium/lifetime plan',
      );
      // No need to set state - GetX Obx handles the UI updates
      return;
    }

    // Check if user has ad-free experience active using GetX controller
    if (AdFreeController.to.isAdFree) {
      debugPrint(
        '🚫 Banner ad completely hidden - user has ad-free experience',
      );
      // No need to set state - GetX Obx handles the UI updates
      return;
    }

    // Check if device is known to have WebView issues
    if (_isProblematicDevice()) {
      debugPrint(
        'Skipping ads on problematic device: ${Platform.operatingSystem}',
      );
      setState(() {
        _hasError = true;
        _errorMessage = 'Device compatibility issue';
      });
      return;
    }
    _loadAd();
  }

  bool _isProblematicDevice() {
    // Add device-specific checks here
    // This is a placeholder - you could check device model, Android version, etc.
    return false; // For now, don't skip any devices
  }

  @override
  void dispose() {
    _isDisposed = true;
    _bannerAd?.dispose();
    super.dispose();
  }

  /// Manual retry method for user-initiated ad refresh
  void _retryLoadAd() {
    if (_isDisposed) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _retryCount = 0; // Reset retry count for manual retry
      _errorMessage = null;
    });

    _loadAd();
  }

  /// Loads a banner ad with proper error handling for JavaScript engine issues.
  void _loadAd() async {
    if (_isDisposed) return; // Don't load if widget is disposed

    // Check if AdManager is initialized
    if (!await AdManager.instance.waitForInitialization()) {
      debugPrint('AdManager initialization failed, cannot load banner ad');
      if (mounted && !_isDisposed) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage =
              'AdMob initialization failed: ${AdManager.instance.initializationError}';
        });
      }
      return;
    }

    if (_retryCount >= _maxRetries) {
      debugPrint('Max retries reached for banner ad loading');
      if (mounted && !_isDisposed) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = 'Failed to load ad after $_maxRetries attempts';
        });
      }
      return;
    }

    try {
      debugPrint('Loading banner ad (attempt ${_retryCount + 1}/$_maxRetries)');

      final bannerAd = BannerAd(
        size: widget.adSize,
        adUnitId: widget.adUnitId,
        request: AdManager.instance.getOptimizedAdRequest(),
        listener: BannerAdListener(
          // Called when an ad is successfully received.
          onAdLoaded: (ad) {
            if (!mounted || _isDisposed) {
              ad.dispose();
              return;
            }
            debugPrint(
              'BannerAd loaded successfully on attempt ${_retryCount + 1}',
            );
            setState(() {
              _bannerAd = ad as BannerAd;
              _isLoading = false;
              _hasError = false;
              _errorMessage = null;
              _retryCount = 0; // Reset retry count on success
            });
          },
          // Called when an ad request failed.
          onAdFailedToLoad: (ad, error) {
            debugPrint(
              'BannerAd failed to load (attempt ${_retryCount + 1}): $error',
            );
            ad.dispose();

            if (!mounted || _isDisposed) return;

            _retryCount++;
            _errorMessage = error.message;

            // Report error to Crashlytics with filtering
            AdManager.reportAdError(
              adType: 'banner',
              errorMessage: error.message,
              errorCode: error.code,
              retryAttempt: _retryCount,
              additionalData: {
                'ad_unit_id': widget.adUnitId,
                'ad_size': '${widget.adSize.width}x${widget.adSize.height}',
                'use_test_ads': widget.useTestAds,
              },
            );

            // Enhanced retry logic for common production issues
            bool shouldRetry =
                _retryCount < _maxRetries &&
                (error.message.contains('JavascriptEngine') ||
                    error.message.contains('No ad to show') ||
                    error.message.contains('Network error') ||
                    error.message.contains('Internal error') ||
                    error.code == 0 || // No fill
                    error.code == 1 || // Invalid request
                    error.code == 2 || // Network error
                    error.code ==
                        3 // No fill
                        );

            if (shouldRetry) {
              // Progressive delay: 2s, 4s, 6s, 8s, 10s
              int delaySeconds = _retryCount * 2;
              debugPrint(
                'Ad load error detected, retrying in ${delaySeconds}s... (Error: ${error.message})',
              );
              Future.delayed(Duration(seconds: delaySeconds), () {
                if (mounted && !_isDisposed) {
                  _loadAd(); // Retry loading
                }
              });
            } else {
              // Either not a recoverable error or max retries reached
              setState(() {
                _isLoading = false;
                _hasError = true;
              });
            }
          },
          // Called when an ad opens an overlay that covers the screen.
          onAdOpened: (ad) {
            debugPrint('BannerAd opened.');
          },
          // Called when an ad removes an overlay that covers the screen.
          onAdClosed: (ad) {
            debugPrint('BannerAd closed.');
          },
        ),
      );

      // Start loading.
      bannerAd.load();
    } catch (e) {
      // Handle JavaScript engine or other initialization errors
      debugPrint(
        'Error initializing banner ad (attempt ${_retryCount + 1}): $e',
      );

      if (!mounted || _isDisposed) return;

      _retryCount++;
      _errorMessage = e.toString();

      // Report initialization error to Crashlytics
      AdManager.reportAdError(
        adType: 'banner_init',
        errorMessage: e.toString(),
        errorCode: -1, // Custom code for initialization errors
        retryAttempt: _retryCount,
        additionalData: {
          'ad_unit_id': widget.adUnitId,
          'error_type': 'initialization',
          'use_test_ads': widget.useTestAds,
        },
      );

      // Enhanced retry logic for initialization errors
      bool shouldRetryInit =
          _retryCount < _maxRetries &&
          (e.toString().contains('JavascriptEngine') ||
              e.toString().contains('WebView') ||
              e.toString().contains('chromium') ||
              e.toString().contains('MobileAds') ||
              e.toString().contains('AdMob') ||
              e.toString().contains('PlatformException'));

      if (shouldRetryInit) {
        // Progressive delay for initialization errors
        int delaySeconds = _retryCount * 3; // 3s, 6s, 9s, 12s, 15s
        debugPrint(
          'Initialization error detected, retrying in ${delaySeconds}s...',
        );
        Future.delayed(Duration(seconds: delaySeconds), () {
          if (mounted && !_isDisposed) {
            _loadAd(); // Retry loading
          }
        });
        return;
      }

      // Max retries reached or non-recoverable error
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
    }
  }
}
