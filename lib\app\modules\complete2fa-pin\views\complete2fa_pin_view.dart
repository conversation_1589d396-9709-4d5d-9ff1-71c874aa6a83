import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/button.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:otp_text_field/otp_field.dart';
import 'package:otp_text_field/style.dart';

import '../controllers/complete2fa_pin_controller.dart';

class Complete2faPinView extends GetView<Complete2faPinController> {
  const Complete2faPinView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Get.isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              IronLockerColors.blue01.withValues(alpha: 0.1),
              IronLockerColors.getThemeBackground(Get.isDarkMode),
            ],
            stops: const [0.0, 0.3],
          ),
        ),
        child: Safe<PERSON><PERSON>(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                const SizedBox(height: 40),

                // Header Section
                _buildHeader(),
                const SizedBox(height: 60),

                // Main Content Card
                _buildMainCard(),
                const SizedBox(height: 32),

                // Recovery Code Link
                _buildRecoveryCodeLink(),
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // PIN Icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: IronLockerColors.blue02.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(40),
            border: Border.all(
              color: IronLockerColors.blue02.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Icon(Icons.pin, size: 40, color: IronLockerColors.blue02),
        ),
        const SizedBox(height: 24),

        // Title
        Text(
          "Enter PIN Code",
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.w700,
            fontFamily: 'Jura',
            color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),

        // Subtitle
        Text(
          "Enter your 4-digit PIN to continue",
          style: TextStyle(
            fontSize: 16,
            fontFamily: 'Jura',
            color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMainCard() {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(maxWidth: 420),
      decoration: BoxDecoration(
        color: IronLockerColors.getThemeCard(Get.isDarkMode),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: IronLockerColors.getThemeBorder(Get.isDarkMode),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            // Lock Icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: IronLockerColors.blue02.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(30),
              ),
              child: Icon(
                Icons.lock_outline,
                size: 30,
                color: IronLockerColors.blue02,
              ),
            ),
            const SizedBox(height: 24),

            // Instructions
            Text(
              "Enter your 4-digit PIN code",
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),

            // Helper text
            Text(
              "This PIN was set up when you enabled 2FA",
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),

            // PIN Input
            LayoutBuilder(
              builder: (context, constraints) {
                // Calculate responsive field width and spacing
                double availableWidth = constraints.maxWidth;
                double totalSpacing = 3 * 8; // 3 gaps of 8px each
                double fieldWidth = (availableWidth - totalSpacing) / 4;

                // Ensure minimum and maximum field sizes
                fieldWidth = fieldWidth.clamp(45.0, 70.0);

                return OTPTextField(
                  length: 4,
                  width: availableWidth,
                  fieldWidth: fieldWidth,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  ),
                  spaceBetween: 8,
                  contentPadding: const EdgeInsets.symmetric(vertical: 12),
                  fieldStyle: FieldStyle.box,
                  obscureText: true,
                  onChanged: (pin) {
                    controller.pinController.text = pin;
                  },
                );
              },
            ),
            const SizedBox(height: 32),

            // Continue Button
            SizedBox(
              width: double.infinity,
              child: Obx(
                () => Button(
                  text: controller.loading.value ? "Verifying..." : "Continue",
                  loading: controller.loading.value,
                  disabled:
                      controller.loading.value ||
                      controller.pinCompleted.isFalse,
                  onPress: controller.complete2fa,
                  bgColor: IronLockerColors.blue02,
                  fgColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecoveryCodeLink() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.help_outline, size: 20, color: Colors.orange),
              const SizedBox(width: 8),
              Text(
                "Can't remember your pin?",
                style: TextStyle(
                  fontSize: 14,
                  fontFamily: 'Jura',
                  fontWeight: FontWeight.w600,
                  color: Colors.orange[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          TextButton(
            onPressed: () {
              Get.toNamed(
                Routes.COMPLETE2FA_RECOVERY_CODE,
                parameters: {
                  "complete2faToken": Get.parameters['complete2faToken']!,
                },
              );
            },
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            child: Text(
              "Use recovery code instead",
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Jura',
                fontWeight: FontWeight.w600,
                color: Colors.orange[700],
                decoration: TextDecoration.underline,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
