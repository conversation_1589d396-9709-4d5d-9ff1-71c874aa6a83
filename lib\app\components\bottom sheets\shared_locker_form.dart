import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/button.dart';
import 'package:ironlocker/app/components/input_field.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/helpers.dart';
import 'package:ironlocker/app/helpers/interstitial_ad_manager.dart';
import 'package:ironlocker/app/helpers/rsa_key_helper.dart';
import 'package:ironlocker/app/helpers/secure_encryption.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/modules/shared-lockers/models/shared_locker.dart';
import 'package:ironlocker/app/services/shared_locker.dart';
import 'package:ironlocker/app/stores/secure_storage.dart';
import 'package:ironlocker/app/stores/shared_lockers.dart';
import 'package:pointycastle/export.dart' as encrypter;

void sharedLockerFormSheet({
  required BuildContext context,
  SharedLockerData? updateSharedLocker,
}) {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  final nameController = TextEditingController(
    text: updateSharedLocker?.name ?? "",
  );
  ScrollController scrollController = ScrollController();
  var loading = false.obs;
  var sharedLockerKey = "".obs;

  final sharedLockersStore = Get.find<SharedLockersStore>();
  final sharedLockerService = Get.find<SharedLockerService>();
  var secureStorage = Get.find<SecureStorageService>();

  handleSubmit() async {
    if (formKey.currentState!.validate()) {
      DialogHelper.loading();

      if (updateSharedLocker == null) {
        var publicKeyBase64 = await secureStorage.read("publicKey");

        encrypter.RSAPublicKey publicKey = RSAKeyHelper.publicKeyFromBase64(
          publicKeyBase64!,
        );

        var randomKey = Helpers.generateSecureKey(32);

        final Uint8List salt = await SecureEncryptionHelper.generateSalt();
        final Uint8List key = Uint8List.fromList(base64.decode(randomKey));

        final Uint8List combinedData = Uint8List.fromList(salt + key);

        sharedLockerKey.value = RSAKeyHelper.encryptWithPublicKey(
          base64.encode(combinedData),
          publicKey,
        );
      }

      final resp =
          updateSharedLocker == null
              ? await sharedLockerService.createSharedLocker(
                name: nameController.text,
                key: sharedLockerKey.value,
              )
              : await sharedLockerService.updateSharedLockerName(
                name: nameController.text,
                sharedLockerId: updateSharedLocker.id,
              );

      if (resp.hasError) {
        Get.back();
        ToastHelper.error("Something went wrong");
        return;
      }
      await sharedLockersStore.fetchSharedLockers();

      if (updateSharedLocker != null) {
        Get.back();
        Get.back();
        ToastHelper.success("Updated");
        return;
      }

      nameController.clear();

      Get.back();
      Get.back();
      ToastHelper.success("Created");

      // Show interstitial ad after successful shared locker creation
      InterstitialAdManager.instance.showItemCreationAd(useTestAds: kDebugMode);
    }
  }

  showModalBottomSheet(
    context: context,
    elevation: 0,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder:
        (context) => Container(
          decoration: BoxDecoration(
            color: IronLockerColors.getThemeCard(Get.isDarkMode),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(28),
              topRight: Radius.circular(28),
            ),
            border: Border.all(
              color: IronLockerColors.getThemeBorder(Get.isDarkMode),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(
                  alpha: Get.isDarkMode ? 0.4 : 0.15,
                ),
                blurRadius: 30,
                offset: const Offset(0, -10),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: IronLockerColors.blue02.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, -5),
                spreadRadius: -5,
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: SingleChildScrollView(
              controller: scrollController,
              child: SafeArea(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle bar
                    Container(
                      margin: const EdgeInsets.only(top: 16),
                      width: 48,
                      height: 5,
                      decoration: BoxDecoration(
                        color: IronLockerColors.getThemeBorder(Get.isDarkMode),
                        borderRadius: BorderRadius.circular(3),
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Header
                    _buildSharedLockerHeader(updateSharedLocker != null),

                    // Content
                    Padding(
                      padding: const EdgeInsets.fromLTRB(24, 8, 24, 32),
                      child: Form(
                        key: formKey,
                        child: Column(
                          children: [
                            // Name Input Section
                            _buildNameInputSection(nameController),

                            const SizedBox(height: 32),

                            // Submit Button
                            Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: IronLockerColors.blue02.withValues(
                                      alpha: 0.3,
                                    ),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Obx(
                                () => Button(
                                  text:
                                      updateSharedLocker != null
                                          ? 'Update Shared Locker'
                                          : 'Create Shared Locker',
                                  loading: loading.value,
                                  onPress: handleSubmit,
                                  bgColor: IronLockerColors.blue02,
                                  height: 50,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
  );
}

Widget _buildSharedLockerHeader(bool isUpdate) {
  return Container(
    margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    padding: const EdgeInsets.all(24),
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [
          IronLockerColors.blue02.withValues(alpha: 0.1),
          IronLockerColors.blue02.withValues(alpha: 0.05),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(20),
      border: Border.all(
        color: IronLockerColors.blue02.withValues(alpha: 0.2),
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: IronLockerColors.blue02.withValues(alpha: 0.1),
          blurRadius: 12,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: Row(
      children: [
        // Enhanced icon container
        Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                IronLockerColors.blue02,
                IronLockerColors.blue02.withValues(alpha: 0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(18),
            boxShadow: [
              BoxShadow(
                color: IronLockerColors.blue02.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: const Icon(
            Icons.people_rounded,
            color: Colors.white,
            size: 28,
          ),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "${isUpdate ? 'Update' : 'Create'} Shared Locker",
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w700,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  letterSpacing: 0.5,
                ),
              ),
              const SizedBox(height: 6),
              Text(
                isUpdate
                    ? "Update your shared locker name"
                    : "Create a secure shared space for collaboration",
                style: TextStyle(
                  fontSize: 15,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                  height: 1.3,
                ),
              ),
            ],
          ),
        ),
        // Decorative element
        Container(
          width: 4,
          height: 40,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                IronLockerColors.blue02,
                IronLockerColors.blue02.withValues(alpha: 0.3),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ],
    ),
  );
}

Widget _buildNameInputSection(TextEditingController nameController) {
  return Container(
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color:
          Get.isDarkMode
              ? Colors.grey[850]?.withValues(alpha: 0.5)
              : Colors.grey[50],
      borderRadius: BorderRadius.circular(16),
      border: Border.all(
        color: IronLockerColors.getThemeBorder(Get.isDarkMode),
        width: 1,
      ),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: IronLockerColors.blue02.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.edit_rounded,
                color: IronLockerColors.blue02,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              "Locker Information",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        InputField(
          labelText: "Shared Locker Name",
          controller: nameController,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter a name for your shared locker';
            }
            if (value.length < 3) {
              return 'Name must be at least 3 characters long';
            }
            return null;
          },
        ),
        const SizedBox(height: 8),
        Text(
          "Choose a descriptive name that helps you and your collaborators identify this shared space.",
          style: TextStyle(
            fontSize: 13,
            color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
            fontFamily: 'Jura',
            height: 1.4,
          ),
        ),
      ],
    ),
  );
}
