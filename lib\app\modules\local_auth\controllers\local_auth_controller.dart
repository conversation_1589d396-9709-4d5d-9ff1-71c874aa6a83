import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/local_auth.dart';

class LocalAuthController extends GetxController {
  @override
  void onInit() {
    super.onInit();
    Future.delayed(Duration.zero, () {
      showAuthDialog();
    });
  }

  void showAuthDialog() {
    DialogHelper.confirmDialog(
      title: "Authentication Required",
      content: "Please authenticate to continue.",
      confirmBtnTxt: "Authenticate",
      hideCancel: true,
      icon: Icons.fingerprint,
      confirmBtnBgColor: Colors.green,
      onConfirm: () async {
        Get.back();
        bool authenticated = await LocalAuthService.authenticate();
        if (authenticated) {
          Get.offNamed(Routes.LOADING);
        } else {
          Get.back();
          showAuthDialog(); // Retry
        }
      },
    );
  }
}
