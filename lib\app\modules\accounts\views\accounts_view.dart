import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/banner_add.dart';
import 'package:ironlocker/app/components/bottom%20sheets/account_form.dart';
import 'package:ironlocker/app/components/empty_state.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/components/locker_drawer.dart';
import 'package:ironlocker/app/components/locker_item_icon.dart';
import 'package:ironlocker/app/components/locker_items.dart';

import 'package:ironlocker/app/constants/colors.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/modules/accounts/controllers/accounts_controller.dart';
import 'package:ironlocker/app/stores/locker_items.dart';

class AccountsPage extends GetView<AccountsController> {
  const AccountsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (controller.selectedItems.isNotEmpty) {
          controller.emptySelectedItems();
          return false;
        }
        return true;
      },
      child: Obx(
        () => Scaffold(
          appBar: LockerAppBar(
            title: "Accounts",
            addLockerItem: () {
              accountFormSheet(context: context);
            },
            searchController: controller.searchController,
            clearSearchText: controller.clearSearchText,
            selectionMode: controller.selectedItems.isNotEmpty,
            selectedItems: controller.selectedItems.length,
            selectAllItems: controller.selectAllItems,
            exitSelectionMode: () {
              controller.emptySelectedItems();
            },
            unmarkFavourite:
                () => DialogHelper.confirmDialog(
                  title:
                      "Are you sure you want to unmark these items as favourites?",
                  onConfirm: controller.unmarkFavourite,
                ),
            markFavourite:
                () => DialogHelper.confirmDialog(
                  title:
                      "Are you sure you want to mark these items as favourites?",
                  onConfirm: controller.markFavourite,
                ),
            deleteItems:
                () => DialogHelper.confirmDialog(
                  title: "Are you sure you want to delete these items?",
                  confirmBtnBgColor: Colors.redAccent,
                  onConfirm: controller.deleteItems,
                ),
          ),
          drawer: LockerDrawer(page: "accounts"),
          body: Column(
            children: [
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async {
                    await Future.delayed(const Duration(seconds: 3));

                    await LockerItemsStore.to.fetchItems();
                  },
                  color: AppColors.primaryColor,
                  backgroundColor: Get.isDarkMode ? null : Colors.white,
                  child: LockerItems(
                    selectedItems: controller.selectedItems,
                    items:
                        controller.searching.isTrue
                            ? controller.searchedItems
                            : LockerItemsStore.to.accounts,
                    onSelected: controller.addItemToSelectedItems,
                    onUnSelected: controller.removeItemFromSelectedItems,
                  ).withEmptyState(
                    isEmpty:
                        controller.searching.isTrue
                            ? controller.searchedItems.isEmpty
                            : LockerItemsStore.to.accounts.isEmpty,
                    emptyState:
                        controller.searching.isTrue
                            ? EmptyStatePresets.noSearchResults(
                              searchTerm: controller.searchController.text,
                            )
                            : EmptyStatePresets.noAccounts(
                              onAddPressed: () {
                                accountFormSheet(context: context);
                              },
                            ),
                  ),
                ),
              ),
              // AdMob Banner Ad at the bottom
              Container(
                color: Get.isDarkMode ? Colors.grey[900] : Colors.grey[50],
                child: SafeArea(
                  top: false,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: BannerAdWidget(useTestAds: kDebugMode),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
