var items = [
  {
    "id": "6503436abe21e790c84694fb",
    "creator": "64dbb3e9d6be17d714c3d62a",
    "itemType": "Note",
    "name": "MTN free net",
    "content":
        "http://free.facebook.com/zero_balance_redirect?operator=mtn_zmhttps://free.facebook.com/zero_balance_redirect?operator=mtn_zm",
    "favourite": true,
    "createdAt": "2023-09-14T17:31:22.169Z",
    "updatedAt": "2023-09-14T17:31:22.169Z",
    "__v": 0
  },
  {
    "id": "6503241dbdb5232af3cd8ac5",
    "creator": "64dbb3e9d6be17d714c3d62a",
    "itemType": "Note",
    "name": "Best 2023 css features",
    "content": "",
    "favourite": false,
    "createdAt": "2023-09-14T15:17:49.026Z",
    "updatedAt": "2023-09-14T15:17:49.026Z",
    "__v": 0
  },
  {
    "id": "6503228ebdb5232af3cd8abb",
    "creator": "64dbb3e9d6be17d714c3d62a",
    "itemType": "Note",
    "name": "Best movies",
    "content": "I love a lot of movies",
    "favourite": false,
    "createdAt": "2023-09-14T15:11:10.107Z",
    "updatedAt": "2023-09-14T15:11:10.107Z",
    "__v": 0
  },
  {
    "id": "6502ff7b6b8a76ab29312328",
    "creator": "64dbb3e9d6be17d714c3d62a",
    "itemType": "Address",
    "name": "US address",
    "street": "4280 Fairway Drive",
    "city": "Ukiah",
    "state": "California",
    "country": "United States of America",
    "zipCode": "95482",
    "notes": "",
    "favourite": false,
    "createdAt": "2023-09-14T12:41:31.701Z",
    "updatedAt": "2023-09-14T12:41:31.701Z",
    "__v": 0
  },
  {
    "id": "650195ed6f67c52ef652b567",
    "creator": "64dbb3e9d6be17d714c3d62a",
    "itemType": "PaymentCard",
    "name": "Zanaco payment card",
    "cardHolderName": "David Mukopa",
    "cardNumber": "4402-8222-7822-7645",
    "expiryMonth": "11",
    "expiryYear": "2026",
    "cvv": "897",
    "favourite": true,
    "createdAt": "2023-09-13T10:58:53.068Z",
    "updatedAt": "2023-09-13T12:39:53.667Z",
    "__v": 0
  },
  {
    "id": "650195ed6f67c52ef652b845",
    "creator": "64dbb3e9d6be17d714c3d62a",
    "itemType": "PaymentCard",
    "name": "Payment card",
    "cardHolderName": "Caroline Zboncak",
    "cardNumber": "4402-8299-2822-5034",
    "expiryMonth": "11",
    "expiryYear": "2028",
    "cvv": "633",
    "notes":
        "I love this payment card very much. It makes my life easier. I don't know how to appreciate this good payment card",
    "favourite": false,
    "createdAt": "2023-09-13T10:58:53.068Z",
    "updatedAt": "2023-09-13T12:39:53.667Z",
    "__v": 0
  },
  {
    "id": "64fd552550f8edc0336deced",
    "creator": "64dbb3e9d6be17d714c3d62a",
    "itemType": "Address",
    "name": "USA warehouse",
    "street": "755 Liberty Street",
    "city": "Plano",
    "state": "Texas",
    "country": "United States",
    "zipCode": "75074",
    "notes": "Address for gilgasoft",
    "favourite": true,
    "createdAt": "2023-09-10T05:33:25.408Z",
    "updatedAt": "2023-09-10T05:33:25.408Z",
    "__v": 0
  },
  {
    "id": "64e1c379ff1db2b5b9ae5202",
    "creator": "64dbb3e9d6be17d714c3d62a",
    "folder": "64df5071d4bbf628730c2303",
    "itemType": "BankAccount",
    "name": "Bank account in react js folder",
    "accountNumber": "",
    "accountHolderName": "",
    "bankName": "",
    "branchName": "",
    "accountType": "",
    "ibanNumber": "",
    "swiftCode": "",
    "pin": "",
    "notes": "This is so good, i really love this bank account",
    "favourite": false,
    "createdAt": "2023-08-20T07:40:41.378Z",
    "updatedAt": "2023-09-13T12:08:37.612Z",
    "__v": 0
  },
  {
    "id": "64e1bffcff1db2b5b9ae512e",
    "creator": "64dbb3e9d6be17d714c3d62a",
    "folder": "64df5071d4bbf628730c2303",
    "itemType": "Note",
    "name": "Item in react js",
    "content": "React js is a powerful javascript library",
    "favourite": false,
    "createdAt": "2023-08-20T07:25:48.187Z",
    "updatedAt": "2023-09-13T12:09:24.551Z",
    "__v": 0
  },
  {
    "id": "64e05905d56bfb6722fab658",
    "creator": "64dbb3e9d6be17d714c3d62a",
    "itemType": "Contact",
    "title": "mr",
    "firstName": "Anthony",
    "middleName": "",
    "lastName": "Simukonda",
    "countryCode": "",
    "phoneNumber": "",
    "country": "",
    "notes":
        "This is an amazing man. He has really helped me in my life. I don't even know how to thank him. My only prayer is that, the good LORD should always bless him",
    "favourite": false,
    "createdAt": "2023-08-19T05:54:13.573Z",
    "updatedAt": "2023-09-13T10:12:34.377Z",
    "__v": 0
  },
  {
    "id": "64e05882d56bfb6722fab644",
    "creator": "64dbb3e9d6be17d714c3d62a",
    "itemType": "Account",
    "name": "Netflix account",
    "userName": "codinboat",
    "email": "<EMAIL>",
    "password": "mwansamulenga",
    "website": "",
    "notes": "This is my official netflix account",
    "favourite": false,
    "createdAt": "2023-08-19T05:52:02.438Z",
    "updatedAt": "2023-09-10T05:29:22.821Z",
    "__v": 0,
    "folder": "64df5071d4bbf628730c2303"
  },
  {
    "id": "64e05882d56bfb6722fab667",
    "creator": "64dbb3e9d6be17d714c3d62a",
    "itemType": "Account",
    "name": "Ebay account",
    "userName": "princemdavi",
    "email": "<EMAIL>",
    "password": "akdffdskfkgdskgk",
    "website": "",
    "notes": "This is my official ebay account",
    "favourite": true,
    "createdAt": "2023-08-19T05:52:02.438Z",
    "updatedAt": "2023-09-10T05:29:22.821Z",
    "__v": 0
  },
  {
    "id": "64df5071d4bbf628730c2303",
    "creator": "64dbb3e9d6be17d714c3d62a",
    "itemType": "Folder",
    "name": "react js",
    "notes":
        "in this folder, i will be keeping my best react js packages. This will really help me to remember them. You are absolutely right",
    "favourite": false,
    "createdAt": "2023-08-18T11:05:21.617Z",
    "updatedAt": "2023-08-20T07:44:13.536Z",
    "__v": 0
  },
  {
    "id": "64df5071d4bbf628730c2309",
    "creator": "64dbb3e9d6be17d714c3d62a",
    "itemType": "Folder",
    "name": "Flutter",
    "favourite": true,
    "createdAt": "2023-08-18T11:05:21.617Z",
    "updatedAt": "2023-08-20T07:44:13.536Z",
    "__v": 0
  },
  {
    "id": "64df4fbfd4bbf628730c22ff",
    "creator": "64dbb3e9d6be17d714c3d62a",
    "itemType": "Contact",
    "firstName": "Mukopa",
    "middleName": "",
    "lastName": "alex",
    "countryCode": "",
    "phoneNumber": "",
    "notes": "This is my beloved brother",
    "favourite": true,
    "createdAt": "2023-08-18T11:02:23.136Z",
    "updatedAt": "2023-09-13T11:28:52.281Z",
    "__v": 0
  },
  {
    "id": "64df4f63d4bbf628730c22f4",
    "creator": "64dbb3e9d6be17d714c3d62a",
    "itemType": "Address",
    "name": "Cape town",
    "street": "1550 Protea St",
    "city": "Gelvandale",
    "state": "Eastern Cape",
    "country": "south africa",
    "zipCode": "6020",
    "notes": "This is the place where all nuclear weapons are kept",
    "favourite": false,
    "createdAt": "2023-08-18T11:00:51.827Z",
    "updatedAt": "2023-09-13T11:30:05.683Z",
    "__v": 0,
    "folder": "64df5071d4bbf628730c2303"
  },
  {
    "id": "64df4eb5d4bbf628730c22e2",
    "creator": "64dbb3e9d6be17d714c3d62a",
    "itemType": "BankAccount",
    "name": "Natsave bank account",
    "accountNumber": "",
    "accountHolderName": "",
    "bankName": "",
    "branchName": "",
    "accountType": "",
    "ibanNumber": "",
    "swiftCode": "",
    "pin": "",
    "notes":
        "This is the one i normally use for my adsense. This account has given me some decent among of money. I really love this man",
    "favourite": true,
    "createdAt": "2023-08-18T10:57:57.957Z",
    "updatedAt": "2023-09-14T18:02:08.013Z",
    "__v": 0
  },
  {
    "id": "64dbb5c4eb538419bd6f530c",
    "creator": "64dbb3e9d6be17d714c3d62a",
    "itemType": "Note",
    "name": "My first note",
    "content": "What a wonderful note. This is really amazing",
    "favourite": false,
    "createdAt": "2023-08-15T17:28:36.565Z",
    "updatedAt": "2023-09-13T12:40:17.045Z",
    "__v": 0,
    "folder": "64df5071d4bbf628730c2303"
  }
];
