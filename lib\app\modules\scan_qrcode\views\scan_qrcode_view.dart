import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/base64image.dart';
import 'package:ironlocker/app/components/button.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/helpers/copy_text_to_clipboard.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/themes/app_bar.dart';
import '../controllers/scan_qrcode_controller.dart';

class ScanQrcodeView extends GetView<ScanQrcodeController> {
  const ScanQrcodeView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Get.isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
      appBar: LockerAppBar(
        title: "Setup Authenticator",
        showSearchIcon: false,
        showAddLockerItemButton: false,
        leading: AppbarTheme.buildStyledBackButton(
          onPressed: () => Get.back(),
          tooltip: 'Back',
        ),
      ),
      body: SafeArea(
        child: Obx(
          () =>
              controller.loading.isTrue
                  ? _buildLoadingState()
                  : SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        const SizedBox(height: 20),

                        // Title and Description
                        Text(
                          "Scan QR Code",
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w700,
                            fontFamily: 'Jura',
                            color: IronLockerColors.getThemeTextPrimary(
                              Get.isDarkMode,
                            ),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),

                        Text(
                          "Scan the QR code below or enter the setup key manually in your authenticator app",
                          style: TextStyle(
                            fontSize: 16,
                            fontFamily: 'Jura',
                            color: IronLockerColors.getThemeTextSecondary(
                              Get.isDarkMode,
                            ),
                            height: 1.4,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 40),

                        // QR Code Card
                        _buildQRCodeCard(),
                        const SizedBox(height: 24),

                        // Setup Key Card
                        _buildSetupKeyCard(),
                        const SizedBox(height: 24),

                        // Instructions Card
                        _buildInstructionsCard(),
                        const SizedBox(height: 32),

                        // Continue Button
                        SizedBox(
                          width: double.infinity,
                          child: Button(
                            text: "Continue to Verification",
                            onPress:
                                () => Get.toNamed(
                                  Routes.TWO_FACTOR_APP,
                                  parameters: {
                                    "password": Get.parameters['password']!,
                                  },
                                ),
                            bgColor: IronLockerColors.blue02,
                            fgColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: IronLockerColors.getThemeCard(Get.isDarkMode),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: IronLockerColors.getThemeBorder(Get.isDarkMode),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              height: 50,
              width: 50,
              child: CircularProgressIndicator(
                color: IronLockerColors.blue02,
                strokeWidth: 3,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              "Generating QR Code...",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQRCodeCard() {
    return Container(
      decoration: BoxDecoration(
        color: IronLockerColors.getThemeCard(Get.isDarkMode),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: IronLockerColors.getThemeBorder(Get.isDarkMode),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // QR Code Icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: IronLockerColors.blue02.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: IronLockerColors.blue02.withValues(alpha: 0.3),
                  width: 2,
                ),
              ),
              child: Icon(
                Icons.qr_code,
                size: 30,
                color: IronLockerColors.blue02,
              ),
            ),
            const SizedBox(height: 20),

            // Title
            Text(
              "QR Code",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
              ),
            ),
            const SizedBox(height: 8),

            Text(
              "Scan this code with your authenticator app",
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // QR Code Image
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: IronLockerColors.getThemeBorder(Get.isDarkMode),
                  width: 1,
                ),
              ),
              child: Base64Image(base64String: controller.base64String),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSetupKeyCard() {
    return Container(
      decoration: BoxDecoration(
        color: IronLockerColors.getThemeCard(Get.isDarkMode),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: IronLockerColors.getThemeBorder(Get.isDarkMode),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.key, color: IronLockerColors.blue02, size: 20),
                const SizedBox(width: 8),
                Text(
                  "Setup Key",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Jura',
                    color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            Text(
              "Or enter this key manually in your authenticator app",
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
              ),
            ),
            const SizedBox(height: 16),

            // Setup Key Container
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Get.isDarkMode ? Colors.grey[800] : Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: IronLockerColors.getThemeBorder(Get.isDarkMode),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      controller.secret,
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'monospace',
                        fontWeight: FontWeight.w500,
                        color: IronLockerColors.getThemeTextPrimary(
                          Get.isDarkMode,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        copyTextToClipboard(controller.secret);
                        ToastHelper.success("Setup key copied to clipboard");
                      },
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        child: Icon(
                          Icons.copy,
                          size: 20,
                          color: IronLockerColors.blue02,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionsCard() {
    return Container(
      decoration: BoxDecoration(
        color: IronLockerColors.getThemeCard(Get.isDarkMode),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: IronLockerColors.getThemeBorder(Get.isDarkMode),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: IronLockerColors.blue02,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  "Next Steps",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Jura',
                    color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildInstructionStep(
              step: "1",
              text:
                  "Open your authenticator app (Google Authenticator, Authy, etc.)",
            ),
            _buildInstructionStep(
              step: "2",
              text: "Scan the QR code above or enter the setup key manually",
            ),
            _buildInstructionStep(
              step: "3",
              text: "Click 'Continue' to verify your setup with a 6-digit code",
              isLast: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionStep({
    required String step,
    required String text,
    bool isLast = false,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: isLast ? 0 : 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: IronLockerColors.blue02,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                step,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Jura',
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
