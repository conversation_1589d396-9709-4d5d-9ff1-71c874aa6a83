import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:ironlocker/app/components/button.dart';

class SomethingWentWrong extends StatelessWidget {
  const SomethingWentWrong({
    super.key,
    required this.retry,
  });

  final VoidCallback retry;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "Something went wrong",
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const Gap(10),
          <PERSON><PERSON>(
            text: "Try again",
            width: 230,
            onPress: retry,
          ),
        ],
      ),
    );
  }
}
