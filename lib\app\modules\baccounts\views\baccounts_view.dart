import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/banner_add.dart';
import 'package:ironlocker/app/components/bottom%20sheets/baccount_form.dart';
import 'package:ironlocker/app/components/empty_state.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/components/locker_drawer.dart';
import 'package:ironlocker/app/components/locker_item_icon.dart';
import 'package:ironlocker/app/components/locker_items.dart';

import 'package:ironlocker/app/constants/colors.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/modules/baccounts/controllers/baccounts_controller.dart';
import 'package:ironlocker/app/stores/locker_items.dart';

class BankAccountsPage extends GetView<BankAccountsController> {
  const BankAccountsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (controller.selectedItems.isNotEmpty) {
          controller.emptySelectedItems();
          return false;
        }
        return true;
      },
      child: Obx(
        () => Scaffold(
          appBar: LockerAppBar(
            title: "Bank accounts",
            addLockerItem: () {
              bankAccountFormSheet(context: context);
            },
            searchController: controller.searchController,
            clearSearchText: controller.clearSearchText,
            selectionMode: controller.selectedItems.isNotEmpty,
            selectedItems: controller.selectedItems.length,
            selectAllItems: controller.selectAllItems,
            exitSelectionMode: () {
              controller.emptySelectedItems();
            },
            unmarkFavourite:
                () => DialogHelper.confirmDialog(
                  title:
                      "Are you sure you want to unmark these items as favourites?",
                  onConfirm: controller.unmarkFavourite,
                ),
            markFavourite:
                () => DialogHelper.confirmDialog(
                  title:
                      "Are you sure you want to mark these items as favourites?",
                  onConfirm: controller.markFavourite,
                ),
            deleteItems:
                () => DialogHelper.confirmDialog(
                  title: "Are you sure you want to delete these items?",
                  confirmBtnBgColor: Colors.redAccent,
                  onConfirm: controller.deleteItems,
                ),
          ),
          drawer: LockerDrawer(page: "bankAccounts"),
          body: Column(
            children: [
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async {
                    await Future.delayed(const Duration(seconds: 3));

                    await LockerItemsStore.to.fetchItems();
                  },
                  color: AppColors.primaryColor,
                  backgroundColor: Get.isDarkMode ? null : Colors.white,
                  child: LockerItems(
                    selectedItems: controller.selectedItems,
                    items:
                        controller.searching.isTrue
                            ? controller.searchedItems
                            : LockerItemsStore.to.bankAccounts,
                    onSelected: controller.addItemToSelectedItems,
                    onUnSelected: controller.removeItemFromSelectedItems,
                  ).withEmptyState(
                    isEmpty:
                        controller.searching.isTrue
                            ? controller.searchedItems.isEmpty
                            : LockerItemsStore.to.bankAccounts.isEmpty,
                    emptyState:
                        controller.searching.isTrue
                            ? EmptyStatePresets.noSearchResults(
                              searchTerm: controller.searchController.text,
                            )
                            : EmptyState(
                              icon: Icons.account_balance_outlined,
                              title: 'No Bank Accounts',
                              description:
                                  'You haven\'t added any bank accounts yet. Store your banking information securely.',
                              actionText: 'Add Bank Account',
                              onActionPressed: () {
                                bankAccountFormSheet(context: context);
                              },
                              color: IronLockerColors.blue02,
                            ),
                  ),
                ),
              ),
              // AdMob Banner Ad at the bottom
              Container(
                color: Get.isDarkMode ? Colors.grey[900] : Colors.grey[50],
                child: SafeArea(
                  top: false,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: BannerAdWidget(useTestAds: kDebugMode),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
