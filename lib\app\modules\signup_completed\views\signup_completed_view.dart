import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import 'package:ironlocker/app/constants/sizes.dart';
import 'package:ironlocker/app/routes/app_pages.dart';

import '../controllers/signup_completed_controller.dart';

class SignupCompletedView extends GetView<SignupCompletedController> {
  const SignupCompletedView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(AppSizes.pagePadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text("Hey!", style: Theme.of(context).textTheme.titleMedium),
              const Gap(20),
              Text(
                "Congratulations! You have successfully registered your account with IronLocker. We're thrilled to have you on board.",
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium!.copyWith(fontSize: 17, height: 1.7),
                textAlign: TextAlign.center,
              ),
              const Gap(10),
              Text(
                "To start using your account and unlock all its features, please check your email inbox for a message from us. Open the email and follow the activation link provided.",
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium!.copyWith(fontSize: 17, height: 1.7),
                textAlign: TextAlign.center,
              ),
              const Gap(10),
              TextButton(
                onPressed: () {
                  Get.offAllNamed(Routes.signin);
                },
                child: const Text(
                  "Sign in",
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.blueAccent,
                    decoration: TextDecoration.underline,
                    decorationColor: Colors.blueAccent,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
