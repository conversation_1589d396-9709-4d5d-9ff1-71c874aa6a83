import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/add_locker_item_card.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';

void addLockerItemSheet({
  required BuildContext context,
  required Function(String) onPress,
}) {
  showModalBottomSheet(
    context: context,
    elevation: 0,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder:
        (context) => Container(
          decoration: BoxDecoration(
            color: IronLockerColors.getThemeBackground(Get.isDarkMode),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(
                  alpha: Get.isDarkMode ? 0.5 : 0.1,
                ),
                blurRadius: 20,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: SingleChildScrollView(
              child: SafeArea(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle bar
                    Container(
                      margin: const EdgeInsets.only(top: 12),
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: IronLockerColors.getThemeTextSecondary(
                          Get.isDarkMode,
                        ),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),

                    // Header
                    _buildAddItemHeader(),

                    // Content
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        children: [
                          // Items grid
                          _buildItemsGrid(onPress),

                          const SizedBox(height: 16),

                          // Footer text
                          Text(
                            'Choose an item type to add to your secure locker',
                            style: TextStyle(
                              fontSize: 14,
                              color: IronLockerColors.getThemeTextSecondary(
                                Get.isDarkMode,
                              ),
                              fontFamily: 'Jura',
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
  );
}

Widget _buildAddItemHeader() {
  return Container(
    margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    padding: const EdgeInsets.all(24),
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [
          IronLockerColors.blue02.withValues(alpha: 0.1),
          IronLockerColors.blue02.withValues(alpha: 0.05),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(20),
      border: Border.all(
        color: IronLockerColors.blue02.withValues(alpha: 0.2),
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: IronLockerColors.blue02.withValues(alpha: 0.1),
          blurRadius: 12,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: Row(
      children: [
        // Enhanced icon container
        Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                IronLockerColors.blue02,
                IronLockerColors.blue02.withValues(alpha: 0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(18),
            boxShadow: [
              BoxShadow(
                color: IronLockerColors.blue02.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: const Center(
            child: Icon(
              Icons.add_circle_outline,
              color: Colors.white,
              size: 32,
            ),
          ),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Add New Item',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w600,
                  color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  fontFamily: 'Jura',
                  letterSpacing: 0.5,
                ),
              ),
              const SizedBox(height: 6),
              Text(
                'Select the type of item you want to add',
                style: TextStyle(
                  fontSize: 14,
                  color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                  fontFamily: 'Jura',
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
        // Decorative element
        Container(
          width: 4,
          height: 40,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                IronLockerColors.blue02,
                IronLockerColors.blue02.withValues(alpha: 0.3),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ],
    ),
  );
}

Widget _buildItemsGrid(Function(String) onPress) {
  final items = [
    {'title': 'Folder', 'type': 'Folder'},
    {'title': 'Contact', 'type': 'Contact'},
    {'title': 'Address', 'type': 'Address'},
    {'title': 'Note', 'type': 'Note'},
    {'title': 'Payment Card', 'type': 'PaymentCard'},
    {'title': 'Account', 'type': 'Account'},
    {'title': 'Bank Account', 'type': 'BankAccount'},
  ];

  return LayoutBuilder(
    builder: (context, constraints) {
      // Calculate responsive columns based on screen width
      int crossAxisCount;
      double spacing = 16;

      if (constraints.maxWidth < 400) {
        crossAxisCount = 2;
      } else if (constraints.maxWidth < 600) {
        crossAxisCount = 3;
      } else if (constraints.maxWidth < 900) {
        crossAxisCount = 4;
      } else {
        crossAxisCount = 5;
      }

      // Ensure we don't have more columns than items
      crossAxisCount =
          crossAxisCount > items.length ? items.length : crossAxisCount;

      return GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: spacing,
          mainAxisSpacing: spacing,
          childAspectRatio: 0.85, // Adjust this to control card height
        ),
        itemCount: items.length,
        itemBuilder: (context, index) {
          final item = items[index];
          return AddLockerItemCard(
            title: item['title']!,
            itemType: item['type']!,
            onTap: () => onPress(item['type']!),
          );
        },
      );
    },
  );
}
