import 'package:flutter/material.dart';
import 'package:form_validator/form_validator.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/button.dart';
import 'package:ironlocker/app/components/input_field.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/routes/app_pages.dart';

import '../controllers/complete2fa_question_controller.dart';

class Complete2faQuestionView extends GetView<Complete2faQuestionController> {
  const Complete2faQuestionView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Get.isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              IronLockerColors.blue01.withValues(alpha: 0.1),
              IronLockerColors.getThemeBackground(Get.isDarkMode),
            ],
            stops: const [0.0, 0.3],
          ),
        ),
        child: Safe<PERSON><PERSON>(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                const SizedBox(height: 40),

                // Header Section
                _buildHeader(),
                const SizedBox(height: 60),

                // Main Content Card
                _buildMainCard(),
                const SizedBox(height: 32),

                // Recovery Code Link
                _buildRecoveryCodeLink(),
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // Question Icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: IronLockerColors.blue02.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(40),
            border: Border.all(
              color: IronLockerColors.blue02.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Icon(Icons.quiz, size: 40, color: IronLockerColors.blue02),
        ),
        const SizedBox(height: 24),

        // Title
        Text(
          "Security Question",
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.w700,
            fontFamily: 'Jura',
            color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),

        // Subtitle
        Text(
          "Answer your security question to continue",
          style: TextStyle(
            fontSize: 16,
            fontFamily: 'Jura',
            color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMainCard() {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(maxWidth: 420),
      decoration: BoxDecoration(
        color: IronLockerColors.getThemeCard(Get.isDarkMode),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: IronLockerColors.getThemeBorder(Get.isDarkMode),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Form(
          key: controller.formKey,
          child: Column(
            children: [
              // Question Icon
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: IronLockerColors.blue02.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Icon(
                  Icons.help_outline,
                  size: 30,
                  color: IronLockerColors.blue02,
                ),
              ),
              const SizedBox(height: 24),

              // Question Text
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: IronLockerColors.blue02.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: IronLockerColors.blue02.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Text(
                  Get.parameters['question'] ?? "Security Question",
                  style: TextStyle(
                    fontSize: 16,
                    fontFamily: 'Jura',
                    fontWeight: FontWeight.w600,
                    color: IronLockerColors.blue02,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 24),

              // Helper text
              Text(
                "Enter your answer below",
                style: TextStyle(
                  fontSize: 14,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),

              // Answer Input
              InputField(
                controller: controller.answerController,
                hintText: "Enter your answer",
                obscureText: true,
                validator: ValidationBuilder().minLength(1).build(),
              ),
              const SizedBox(height: 32),

              // Continue Button
              SizedBox(
                width: double.infinity,
                child: Obx(
                  () => Button(
                    text:
                        controller.loading.value ? "Verifying..." : "Continue",
                    loading: controller.loading.value,
                    disabled: controller.loading.value,
                    onPress: controller.complete2fa,
                    bgColor: IronLockerColors.blue02,
                    fgColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecoveryCodeLink() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.help_outline, size: 20, color: Colors.orange),
              const SizedBox(width: 8),
              Text(
                "Can't remember your answer?",
                style: TextStyle(
                  fontSize: 14,
                  fontFamily: 'Jura',
                  fontWeight: FontWeight.w600,
                  color: Colors.orange[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          TextButton(
            onPressed: () {
              Get.toNamed(
                Routes.COMPLETE2FA_RECOVERY_CODE,
                parameters: {
                  "complete2faToken": Get.parameters['complete2faToken']!,
                },
              );
            },
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            child: Text(
              "Use recovery code instead",
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Jura',
                fontWeight: FontWeight.w600,
                color: Colors.orange[700],
                decoration: TextDecoration.underline,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
