import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';

import '../helpers/copy_text_to_clipboard.dart';
import '../helpers/toast.dart';

class LockerItemPropertyDetail extends StatelessWidget {
  const LockerItemPropertyDetail({
    super.key,
    required this.name,
    required this.value,
  });
  final String name;
  final String value;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Label
          Padding(
            padding: const EdgeInsets.only(left: 4, bottom: 8),
            child: Text(
              name,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w600,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                letterSpacing: 0.5,
              ),
            ),
          ),

          // Value Container
          GestureDetector(
            onDoubleTap: () {
              copyTextToClipboard(value);
              ToastHelper.success("Copied to clipboard");
            },
            child: Container(
              width: double.infinity,
              constraints: const BoxConstraints(minHeight: 52),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              decoration: BoxDecoration(
                color:
                    Get.isDarkMode
                        ? Colors.grey[800]?.withValues(alpha: 0.6)
                        : Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: IronLockerColors.getThemeBorder(Get.isDarkMode),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(
                      alpha: Get.isDarkMode ? 0.2 : 0.04,
                    ),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      value.isNotEmpty ? value : "No data",
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Jura',
                        color:
                            value.isNotEmpty
                                ? IronLockerColors.getThemeTextPrimary(
                                  Get.isDarkMode,
                                )
                                : IronLockerColors.getThemeTextSecondary(
                                  Get.isDarkMode,
                                ),
                        height: 1.4,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Copy Button
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        copyTextToClipboard(value);
                        ToastHelper.success("Copied to clipboard");
                      },
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.copy_rounded,
                          size: 18,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
