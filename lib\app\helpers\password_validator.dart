class PwdValidator {
  static bool atleastEightChars(String password) {
    return password.length >= 8;
  }

  static bool atleastOneUppercase(String password) {
    return password.contains(RegExp(r'[A-Z]'));
  }

  static bool atleastOneNumber(String password) {
    return password.contains(RegExp(r'\d'));
  }

  static bool atleastOneLowercase(String password) {
    return password.contains(RegExp(r'[a-z]'));
  }

  static bool atleastOne<PERSON>haracter(String password) {
    return password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
  }

  static bool doesNotContainEmail(String password, String email) {
    return !password.contains(email);
  }
}
