import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/helpers.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/services/user.dart';
import 'package:ironlocker/app/services/user_sessions.dart';
import 'package:ironlocker/app/stores/user.dart';
import '../../../routes/app_pages.dart';

class LockedLockerController extends GetxController {
  final passwordController = TextEditingController();
  var loading = false.obs;
  var email = "".obs;

  final userService = Get.find<UserService>();
  final userSessionService = Get.find<UserSessionsService>();

  @override
  onInit() {
    email(UserStore.to.email.value);
    super.onInit();
  }

  handleUnlock(GlobalKey<FormState> formKey) async {
    if (!formKey.currentState!.validate()) return;

    DialogHelper.loading();

    final resp = await userSessionService.unlockSession(
      UserStore.to.sessionId.value,
      passwordController.text,
    );
    Get.back();

    if (resp.hasError) {
      return ToastHelper.error(
        resp.body != null ? resp.body['error'] : "Something went wrong",
      );
    }

    Get.offNamed(Routes.LOADING);
  }

  handleLogout() async {
    Get.back();

    DialogHelper.loading();

    final resp = await userService.signout();

    if (resp.hasError) {
      Get.back();
      ToastHelper.error("Something went wrong");
      return;
    }

    await Helpers.clearData();

    Get.offAllNamed(Routes.signin);
  }
}
