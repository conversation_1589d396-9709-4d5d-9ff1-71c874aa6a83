import 'package:flutter/material.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';

class AppbarTheme {
  static const lightTheme = AppBarTheme(
    titleTextStyle: TextStyle(
      color: Colors.white,
      fontSize: 20,
      fontWeight: FontWeight.w600,
      fontFamily: '<PERSON>ra',
    ),
    centerTitle: false,
    backgroundColor: IronLockerColors.blue02,
    iconTheme: IconThemeData(color: Colors.white),
    elevation: 0,
    shadowColor: IronLockerColors.blue01,
    // Custom back button styling
    leadingWidth: 56,
  );
  static const darkTheme = AppBarTheme(
    backgroundColor: IronLockerColors.blue02,
    titleTextStyle: TextStyle(
      color: Colors.white,
      fontSize: 20,
      fontWeight: FontWeight.w600,
      fontFamily: 'Jura',
    ),
    centerTitle: false,
    iconTheme: IconThemeData(color: Colors.white),
    elevation: 0,
    shadowColor: IronLockerColors.blue01,
    // Custom back button styling
    leadingWidth: 56,
  );

  /// Creates a styled back button that matches other app bar icons
  static Widget buildStyledBackButton({
    required VoidCallback onPressed,
    IconData? icon,
    String? tooltip,
  }) {
    return Container(
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon ?? Icons.arrow_back, color: Colors.white, size: 22),
        tooltip: tooltip ?? 'Back',
      ),
    );
  }
}
