import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/helpers/write_recovery_codes_to_file.dart';

class RecoveryCodesController extends GetxController {
  downloadRecoveryCodes() async {
    try {
      final recoveryCodes = Get.parameters["recoveryCodes"]!.split(" ");
      final result = await writeRecoveryCodesToFile(recoveryCodes);

      if (result != null) {
        ToastHelper.success("File downloaded successfully");
      } else {
        ToastHelper.error("Something went wrong");
      }
    } catch (e) {}
  }
}
