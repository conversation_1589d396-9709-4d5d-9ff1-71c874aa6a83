import 'dart:convert';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/storage.dart';
import 'package:ironlocker/app/services/subscription_service.dart';
import 'package:ironlocker/app/services/websocket_service.dart';
import 'package:ironlocker/app/stores/secure_storage.dart';
import 'package:ironlocker/app/stores/user.dart';
import 'package:uuid/uuid.dart';

class Helpers {
  static Future clearData() async {
    final storage = Storage();
    final SecureStorageService secureStorage = Get.find<SecureStorageService>();

    // Disconnect WebSocket before clearing tokens
    try {
      final webSocketService = Get.find<WebSocketService>();
      webSocketService.disconnect();
      debugPrint('WebSocket disconnected during logout');
    } catch (e) {
      // Don't block logout if WebSocket disconnect fails
      debugPrint('WebSocket disconnect error during clearData: $e');
    }

    // Logout from RevenueCat
    try {
      await SubscriptionService.to.logoutUser();
    } catch (e) {
      // Don't block logout if RevenueCat logout fails
      debugPrint('RevenueCat logout error during clearData: $e');
    }

    await storage.deleteItem("accessToken");
    await storage.deleteItem("refreshToken");
    await storage.deleteItem("encryptionKeyVerified");
    await storage.deleteItem("biometricEnabled");
    await secureStorage.clear();

    // Clear UserStore tokens after storage cleanup
    UserStore.to.accessToken("");
    UserStore.to.refreshToken("");
  }

  static Future<String> getDeviceID() async {
    final SecureStorageService secureStorage = Get.find<SecureStorageService>();

    String? id = await secureStorage.read('device_id');
    if (id != null) return id;

    id = Uuid().v4();
    await secureStorage.write('device_id', id);
    return id;
  }

  static String generateSecureKey(int length) {
    final Random secureRandom = Random.secure();
    final List<int> values = List<int>.generate(
      length,
      (i) => secureRandom.nextInt(256),
    );
    return base64Url.encode(values);
  }
}
