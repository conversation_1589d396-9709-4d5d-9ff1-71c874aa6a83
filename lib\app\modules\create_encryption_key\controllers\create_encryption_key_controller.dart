import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/rsa_key_helper.dart';
import 'package:ironlocker/app/helpers/secure_encryption.dart';
import 'package:ironlocker/app/helpers/storage.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/user.dart';
import 'package:ironlocker/app/services/websocket_service.dart';
import 'package:ironlocker/app/stores/secure_storage.dart';

class CreateEncryptionKeyController extends GetxController {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final encryptionKeyController = TextEditingController();
  final confirmEncryptionKeyController = TextEditingController();

  var loading = false.obs;
  var canCreate = false.obs;
  var showEncryptionKey = false.obs;
  var showConfirmEncryptionKey = false.obs;

  // Password requirement tracking
  var hasMinLength = false.obs;
  var hasUppercase = false.obs;
  var hasLowercase = false.obs;
  var hasNumber = false.obs;
  var hasSpecialChar = false.obs;
  var keysMatch = false.obs;

  final userService = Get.find<UserService>();
  final secureStorage = Get.find<SecureStorageService>();
  final storage = Storage();

  @override
  void onInit() {
    super.onInit();
    // Add listeners to check if both fields have content
    encryptionKeyController.addListener(_updateCanCreate);
    confirmEncryptionKeyController.addListener(_updateCanCreate);
    _updateCanCreate(); // Initial check
  }

  void _updateCanCreate() {
    final encryptionKey = encryptionKeyController.text;
    final confirmKey = confirmEncryptionKeyController.text;

    // Update individual requirement tracking
    hasMinLength.value = encryptionKey.length >= 8;
    hasUppercase.value = encryptionKey.contains(RegExp(r'[A-Z]'));
    hasLowercase.value = encryptionKey.contains(RegExp(r'[a-z]'));
    hasNumber.value = encryptionKey.contains(RegExp(r'[0-9]'));
    hasSpecialChar.value = encryptionKey.contains(
      RegExp(r'[!@#$%^&*(),.?":{}|<>]'),
    );
    keysMatch.value =
        encryptionKey.isNotEmpty &&
        confirmKey.isNotEmpty &&
        encryptionKey == confirmKey;

    // Enable button only if all requirements are met
    canCreate.value =
        hasMinLength.value &&
        hasUppercase.value &&
        hasLowercase.value &&
        hasNumber.value &&
        hasSpecialChar.value &&
        keysMatch.value;
  }

  void toggleEncryptionKeyVisibility() {
    showEncryptionKey.value = !showEncryptionKey.value;
  }

  void toggleConfirmEncryptionKeyVisibility() {
    showConfirmEncryptionKey.value = !showConfirmEncryptionKey.value;
  }

  handleSaveKeys() async {
    try {
      // Check if button should be enabled (all validations passed)
      if (!canCreate.value) return;

      loading(true);

      final salt = await SecureEncryptionHelper.generateSalt();
      final key = await SecureEncryptionHelper.generateKey(
        encryptionKeyController.text,
        salt,
      );

      await Future.wait([
        secureStorage.write(
          SecureEncryptionHelper.persistentSaltStorageKey,
          base64.encode(salt),
        ),
        secureStorage.write(
          SecureEncryptionHelper.persistentKeyStorageKey,
          base64.encode(key),
        ),
      ]);

      final keyPairBase64 = await RSAKeyHelper.generateAndEncodeKeys();

      final encryptedPrivateKey = await SecureEncryptionHelper.encryptText(
        keyPairBase64["privateKey"]!,
      );

      final resp = await userService.saveKeys({
        "privateKey": encryptedPrivateKey,
        "publicKey": keyPairBase64["publicKey"],
      });

      loading(false);

      if (resp.hasError) {
        debugPrint(resp.statusText);
        ToastHelper.error(
          resp.body != null ? resp.body['error'] : "Something went wrong",
        );
        return;
      }

      await Future.wait([
        storage.deleteItem("newUser"),
        storage.setItem(key: "encryptionKeyVerified", value: true),
      ]);

      // Connect WebSocket after encryption key is created
      try {
        final webSocketService = Get.find<WebSocketService>();
        await webSocketService.connect();
        debugPrint('✅ WebSocket connected after encryption key creation');
      } catch (e) {
        // Don't block navigation if WebSocket connection fails
        debugPrint('WebSocket connection error after encryption creation: $e');
      }

      Get.offAllNamed(Routes.LOADING);
    } catch (e) {
      debugPrint(e.toString());
      ToastHelper.error("Something went wrong");
      loading(false);
    }
  }

  @override
  void onClose() {
    encryptionKeyController.removeListener(_updateCanCreate);
    confirmEncryptionKeyController.removeListener(_updateCanCreate);
    super.onClose();
  }
}
