import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

import 'package:get/get.dart';
import 'package:ironlocker/app/components/banner_add.dart';
import 'package:ironlocker/app/components/bottom%20sheets/shared_locker_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/shared_locker_members.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/components/locker_items.dart';
import 'package:ironlocker/app/components/something_went_wrong.dart';
import 'package:ironlocker/app/constants/colors.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/constants/sizes.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/stores/user.dart';
import 'package:ironlocker/app/themes/app_bar.dart';

import '../controllers/shared_locker_controller.dart';

class SharedLockerView extends GetView<SharedLockerController> {
  const SharedLockerView({super.key});
  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        appBar: LockerAppBar(
          title: controller.sharedLockerData.value.name,
          leading: AppbarTheme.buildStyledBackButton(
            onPressed: () => Get.back(),
            tooltip: 'Back',
          ),
          searchController: controller.searchController,
          showAddLockerItemButton: false, // Move to footer
          clearSearchText: controller.clearSearchText,
          popupMenuButton:
              UserStore.to.id.value != controller.sharedLockerData.value.creator
                  ? null
                  : PopupMenuButton(
                    iconColor: Colors.white,
                    itemBuilder:
                        (context) => [
                          PopupMenuItem(
                            height: 35,
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            onTap: () {
                              sharedLockerMembersSheet(
                                context: context,
                                sharedLocker: controller.sharedLockerData,
                                encryptionKey: controller.originalKey.value,
                              );
                            },
                            child: Text(
                              "Members",
                              style: Theme.of(context).textTheme.labelMedium,
                            ),
                          ),
                          PopupMenuItem(
                            height: 35,
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            onTap: () {
                              sharedLockerFormSheet(
                                context: context,
                                updateSharedLocker:
                                    controller.sharedLockerData.value,
                              );
                            },
                            child: Text(
                              "Update Name",
                              style: Theme.of(context).textTheme.labelMedium,
                            ),
                          ),
                          PopupMenuItem(
                            height: 35,
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            onTap: () {
                              DialogHelper.confirmDialog(
                                title:
                                    "Are you sure you want to delete this shared locker?",
                                onConfirm: controller.deleteLocker,
                              );
                            },
                            child: Text(
                              "Delete",
                              style: Theme.of(context).textTheme.labelMedium,
                            ),
                          ),
                        ],
                  ),
        ),
        body: Column(
          children: [
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  await controller.fetchSharedLockerItems();
                },
                color: AppColors.primaryColor,
                backgroundColor: Get.isDarkMode ? null : Colors.white,
                child:
                    controller.loading.isTrue
                        ? const Center(
                          child: SizedBox(
                            height: 50,
                            width: 50,
                            child: CircularProgressIndicator(
                              color: AppColors.primaryColor,
                            ),
                          ),
                        )
                        : controller.error.isNotEmpty
                        ? SomethingWentWrong(
                          retry: controller.fetchSharedLockerItems,
                        )
                        : controller.sharedLockerItems.isEmpty
                        ? Center(
                          child: Container(
                            padding: const EdgeInsets.all(AppSizes.pagePadding),
                            constraints: const BoxConstraints(maxWidth: 420),
                            child: const Text(
                              "There are no items in this shared locker. Click the plus button above to start adding items.",
                              textAlign: TextAlign.center,
                              style: TextStyle(fontSize: 16, height: 1.6),
                            ),
                          ),
                        )
                        : LockerItems(
                          items:
                              controller.searching.isTrue
                                  ? controller.searchedItems
                                  : controller.sharedLockerItems,
                          encryptionKey: controller.encryptionKey.value,
                          onSelected: (dynamic) {},
                          onUnSelected: (dynamic) {},
                        ),
              ),
            ),
            // Footer with Add Button
            _buildFooter(context, controller),
            // AdMob Banner Ad at the bottom
            Container(
              decoration: BoxDecoration(
                color: Get.isDarkMode ? Colors.grey[850] : Colors.grey[100],
                border: Border(
                  top: BorderSide(
                    color:
                        Get.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
                    width: 0.5,
                  ),
                ),
              ),
              child: SafeArea(
                top: false,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2.0),
                  child: BannerAdWidget(useTestAds: kDebugMode),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFooter(BuildContext context, SharedLockerController controller) {
    // Only show footer if user has write permission
    if (!controller.hasWritePermission) {
      return const SizedBox.shrink();
    }

    return Container(
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[900] : Colors.white,
        border: Border(
          top: BorderSide(
            color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
            width: 0.5,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Center(
          child: SizedBox(
            width: 56,
            height: 56,
            child: ElevatedButton(
              onPressed:
                  () => controller.addLockerItem(
                    context,
                    controller.sharedLockerData.value.id,
                  ),
              style: ElevatedButton.styleFrom(
                backgroundColor: IronLockerColors.blue02,
                foregroundColor: Colors.white,
                elevation: 4,
                padding: EdgeInsets.zero,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              child: const Icon(Icons.add, size: 28),
            ),
          ),
        ),
      ),
    );
  }
}
