import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/user.dart';
import 'package:ironlocker/app/stores/user.dart';

class TwoFactorQuestionController extends GetxController {
  var questionController = TextEditingController();
  var answerController = TextEditingController();
  var confirmAnswerController = TextEditingController();

  var answerMatch = false.obs;

  final userService = Get.find<UserService>();

  @override
  onInit() {
    super.onInit();

    answerController.addListener(() {
      if (answerController.text == confirmAnswerController.text) {
        answerMatch(true);
      } else {
        answerMatch(false);
      }
    });
    confirmAnswerController.addListener(() {
      if (answerController.text == confirmAnswerController.text) {
        answerMatch(true);
      } else {
        answerMatch(false);
      }
    });
  }

  enable2faQuestion(GlobalKey<FormState> formKey) async {
    if (!formKey.currentState!.validate()) return;

    if (answerMatch.isFalse) return;

    DialogHelper.loading();

    final resp = await userService.enable2fa({
      "twoFaMethod": "question",
      "password": Get.parameters['password'],
      "question": questionController.text,
      "answer": answerController.text,
    });

    if (resp.hasError) {
      Get.back();
      ToastHelper.error(
        resp.body != null ? resp.body['error'] : "Something went wrong",
      );
      return;
    }

    final recoveryCodes = resp.body['recoveryCodes'];

    UserStore.to.twoFactorEnabled(true);
    ToastHelper.success("2fa enabled");

    Get.offNamed(
      Routes.RECOVERY_CODES,
      parameters: {"recoveryCodes": recoveryCodes.join(" ")},
    );
  }
}
