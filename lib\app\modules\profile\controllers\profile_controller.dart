import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/error_helper.dart';
import 'package:ironlocker/app/helpers/helpers.dart';
import 'package:ironlocker/app/helpers/storage.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/user.dart';
import 'package:ironlocker/app/services/analytics_service.dart';
import 'package:ironlocker/app/stores/user.dart';
import 'package:url_launcher/url_launcher.dart';

class ProfileController extends GetxController {
  final nameController = TextEditingController(text: UserStore.to.name.value);
  final emailController = TextEditingController(text: UserStore.to.email.value);
  final currentPasswordController = TextEditingController();

  final userService = Get.find<UserService>();

  @override
  void onInit() {
    super.onInit();
    // Track profile screen view
    Get.find<AnalyticsService>().trackScreenView(
      screenName: 'ProfileScreen',
      screenClass: 'ProfileView',
    );
  }

  void updateName() async {
    if (nameController.text.length < 3) {
      ToastHelper.error("Your name should be atleast 3 characters");
      return;
    }

    Get.back();
    DialogHelper.loading();

    final resp = await userService.updateName(nameController.text);
    Get.back();

    if (resp.body?["error"] == "SESSION_LOCKED") {
      Get.offNamed(Routes.lockedLocker);
      return;
    } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
        resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
      await Helpers.clearData();
      Get.offAllNamed(Routes.signin);
      return;
    }

    if (resp.hasError) {
      ToastHelper.error("Something went wrong");
      return;
    }

    UserStore.to.name(nameController.text);

    ToastHelper.success("Changes saved");
  }

  void updateEmail() async {
    if (currentPasswordController.text.length < 8) {
      ToastHelper.error("Your password should be atleast 8 characters");
      return;
    }

    DialogHelper.loading();

    final resp = await userService.updateEmail(
      email: emailController.text,
      currentPassword: currentPasswordController.text,
    );
    Get.back();

    if (resp.hasError) {
      ToastHelper.error(resp.body?["error"] ?? "Something went wrong");
      return;
    }

    Get.back();

    DialogHelper.info(
      info: resp.body?["msg"] ?? "Check your email to complete the process",
    );
    emailController.text = UserStore.to.email.value;
    currentPasswordController.clear();
  }

  void deleteAccount(String currentPassword) async {
    if (currentPassword.length < 8) {
      ToastHelper.error("Enter your password");
      return;
    }

    Get.back();
    DialogHelper.loading();

    final resp = await userService.deleteAccount(
      currentPassword: currentPassword,
    );

    if (resp.hasError) {
      Get.back();
      ToastHelper.error(resp.body?["error"] ?? "Something went wrong");
      return;
    }

    final storage = Storage();
    await storage.deleteItem("refreshToken");
    await storage.deleteItem("accessToken");

    Get.offAllNamed(Routes.signin);
  }

  /// Request account deletion by redirecting to the deletion request page
  void requestAccountDeletion() async {
    try {
      final url = Uri.parse('https://www.ironlocker.app/delete-account');

      // Show loading message
      ErrorHelper.showSuccess(
        'Redirecting to account deletion request page...',
        title: 'Opening Browser',
      );

      // Try to launch the URL
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        // If can't launch, show the URL in a dialog
        _showAccountDeletionUrlDialog();
      }
    } catch (e) {
      // If any error occurs, show the URL in a dialog
      _showAccountDeletionUrlDialog();
    }
  }

  /// Show URL in a dialog if browser can't be opened
  void _showAccountDeletionUrlDialog() {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: Get.width > 600 ? 400 : Get.width * 0.9,
            maxHeight: Get.height * 0.6,
          ),
          child: Padding(
            padding: EdgeInsets.all(Get.width > 600 ? 32 : 24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Request Account Deletion',
                  style: TextStyle(
                    fontSize: Get.width > 600 ? 20 : 18,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Jura',
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: Get.width > 600 ? 24 : 20),
                Text(
                  'Please visit the following URL to request account deletion:',
                  style: TextStyle(
                    fontSize: Get.width > 600 ? 16 : 15,
                    fontFamily: 'Jura',
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: Get.width > 600 ? 20 : 16),
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(Get.width > 600 ? 16 : 12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(
                      Get.width > 600 ? 12 : 8,
                    ),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: SelectableText(
                    'https://www.ironlocker.app/delete-account',
                    style: TextStyle(
                      fontSize: Get.width > 600 ? 15 : 14,
                      fontFamily: 'monospace',
                      color: Colors.blue.shade700,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(height: Get.width > 600 ? 32 : 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Get.back(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: EdgeInsets.symmetric(
                        vertical: Get.width > 600 ? 16 : 14,
                      ),
                    ),
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Jura',
                        fontSize: Get.width > 600 ? 16 : 15,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
