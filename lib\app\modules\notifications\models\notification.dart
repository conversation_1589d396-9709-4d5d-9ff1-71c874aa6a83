class NotificationData {
  late String id;
  String? user;
  late String title;
  late String message;
  late String type;
  late bool isRead;
  late String createdAt;
  late String updatedAt;
  int? iV;

  NotificationData({
    required this.id,
    required this.user,
    required this.title,
    required this.message,
    required this.type,
    required this.isRead,
    required this.createdAt,
    required this.updatedAt,
    this.iV,
  });

  NotificationData.fromJson(Map<String, dynamic> json) {
    id = json['_id'];
    user = json['user'];
    title = json['title'];
    message = json['message'];
    type = json['type'];
    isRead = json['isRead'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    iV = json['__v'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['_id'] = id;
    data['user'] = user;
    data['title'] = title;
    data['message'] = message;
    data['type'] = type;
    data['isRead'] = isRead;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    data['__v'] = iV;
    return data;
  }
}
