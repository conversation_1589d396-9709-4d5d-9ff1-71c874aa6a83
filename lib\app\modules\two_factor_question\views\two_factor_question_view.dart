import 'package:flutter/material.dart';
import 'package:form_validator/form_validator.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/button.dart';
import 'package:ironlocker/app/components/input_field.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/themes/app_bar.dart';
import '../controllers/two_factor_question_controller.dart';

class TwoFactorQuestionView extends GetView<TwoFactorQuestionController> {
  const TwoFactorQuestionView({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();

    return Scaffold(
      backgroundColor:
          Get.isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
      appBar: LockerAppBar(
        title: "Setup Security Question",
        showSearchIcon: false,
        showAddLockerItemButton: false,
        leading: AppbarTheme.buildStyledBackButton(
          onPressed: () => Get.back(),
          tooltip: 'Back',
        ),
      ),
      body: Safe<PERSON>rea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              const SizedBox(height: 20),

              // Title and Description
              Text(
                "Create Security Question",
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w700,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),

              Text(
                "Set up a personal question and answer for two-factor authentication",
                style: TextStyle(
                  fontSize: 16,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),

              // Main Content Card
              _buildMainCard(formKey),
              const SizedBox(height: 24),

              // Security Tips
              _buildSecurityTips(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMainCard(GlobalKey<FormState> formKey) {
    return Container(
      decoration: BoxDecoration(
        color: IronLockerColors.getThemeCard(Get.isDarkMode),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: IronLockerColors.getThemeBorder(Get.isDarkMode),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Form(
          key: formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Question Section
              Text(
                "Security Question",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                ),
              ),
              const SizedBox(height: 8),

              Text(
                "Choose a question only you would know the answer to",
                style: TextStyle(
                  fontSize: 14,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                ),
              ),
              const SizedBox(height: 16),

              InputField(
                labelText: "Enter your security question",
                hintText: "e.g., What was the name of your first pet?",
                validator: ValidationBuilder().minLength(10).build(),
                controller: controller.questionController,
              ),
              const SizedBox(height: 24),

              // Answer Section
              Text(
                "Answer",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                ),
              ),
              const SizedBox(height: 8),

              Text(
                "Enter the answer to your security question",
                style: TextStyle(
                  fontSize: 14,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                ),
              ),
              const SizedBox(height: 16),

              InputField(
                labelText: "Enter your answer",
                hintText: "Your answer (case sensitive)",
                obscureText: true,
                validator: ValidationBuilder().minLength(6).build(),
                controller: controller.answerController,
              ),
              const SizedBox(height: 20),

              InputField(
                labelText: "Confirm your answer",
                hintText: "Re-enter your answer",
                obscureText: true,
                controller: controller.confirmAnswerController,
              ),
              const SizedBox(height: 24),

              // Answer Match Indicator
              Obx(() => _buildAnswerMatchIndicator()),
              const SizedBox(height: 32),

              // Enable Button
              Obx(
                () => SizedBox(
                  width: double.infinity,
                  child: Button(
                    text: "Enable 2FA with Security Question",
                    disabled: controller.answerMatch.isFalse,
                    onPress: () => controller.enable2faQuestion(formKey),
                    bgColor: IronLockerColors.blue02,
                    fgColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnswerMatchIndicator() {
    final isMatching = controller.answerMatch.isTrue;
    final hasInput =
        controller.answerController.text.isNotEmpty &&
        controller.confirmAnswerController.text.isNotEmpty;

    if (!hasInput) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color:
            isMatching
                ? Colors.green.withValues(alpha: 0.1)
                : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color:
              isMatching
                  ? Colors.green.withValues(alpha: 0.3)
                  : Colors.red.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            isMatching ? Icons.check_circle : Icons.error,
            color: isMatching ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 12),
          Text(
            isMatching ? "Answers match" : "Answers don't match",
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              fontFamily: 'Jura',
              color: isMatching ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityTips() {
    return Container(
      decoration: BoxDecoration(
        color: IronLockerColors.getThemeCard(Get.isDarkMode),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: IronLockerColors.getThemeBorder(Get.isDarkMode),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.security, color: IronLockerColors.blue02, size: 20),
                const SizedBox(width: 8),
                Text(
                  "Security Tips",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Jura',
                    color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildSecurityTip(
              icon: Icons.quiz,
              text: "Choose a question with an answer only you know",
            ),
            _buildSecurityTip(
              icon: Icons.visibility_off,
              text: "Avoid questions with answers that can be found online",
            ),
            _buildSecurityTip(
              icon: Icons.text_fields,
              text: "Remember that answers are case-sensitive",
            ),
            _buildSecurityTip(
              icon: Icons.save,
              text: "Save your recovery codes in a safe place",
              isLast: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityTip({
    required IconData icon,
    required String text,
    bool isLast = false,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: isLast ? 0 : 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
            size: 16,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
