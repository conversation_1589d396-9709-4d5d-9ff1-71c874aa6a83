import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/bottom%20sheets/shared_locker_member_permission.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/helpers.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/modules/shared-locker/models/shared_locker_member.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/shared_locker_member.dart';
import 'package:ironlocker/app/stores/shared_lockers.dart';

void sharedLockerMemberActionSheet({
  required BuildContext context,
  required SharedLockerMember member,
}) {
  final sharedLockerMemberService = Get.find<SharedLockerMemberService>();
  final sharedLockersStore = Get.find<SharedLockersStore>();

  removeMember() async {
    Get.back();
    Get.back();

    DialogHelper.loading();
    var resp = await sharedLockerMemberService.removeMember(
      member.membershipId,
    );

    if (resp.hasError) {
      Get.back();

      if (resp.body?["error"] == "SESSION_LOCKED") {
        Get.offAllNamed(Routes.lockedLocker);
      } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
          resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
        await Helpers.clearData();
        Get.offAllNamed(Routes.signin);
      } else {
        ToastHelper.error("Something went wrong");
      }
      return;
    }

    await sharedLockersStore.fetchSharedLockers();
    Get.back();

    ToastHelper.success("Member removed");
  }

  showModalBottomSheet(
    context: context,
    elevation: 0,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder:
        (context) => Container(
          decoration: BoxDecoration(
            color: IronLockerColors.getThemeBackground(Get.isDarkMode),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                _buildActionHeader(context, member),

                // Actions
                Padding(
                  padding: const EdgeInsets.fromLTRB(24, 8, 24, 32),
                  child: Column(
                    children: [
                      _buildActionItem(
                        context: context,
                        icon: Icons.edit_outlined,
                        title: 'Update Permission',
                        subtitle: 'Change member access level',
                        color: IronLockerColors.blue02,
                        onTap: () {
                          sharedLockerMemberPermissionSheet(
                            context: context,
                            member: member,
                          );
                        },
                      ),
                      const SizedBox(height: 16),
                      _buildActionItem(
                        context: context,
                        icon: Icons.person_remove_outlined,
                        title: 'Remove Member',
                        subtitle: 'Remove ${member.name} from this locker',
                        color: IronLockerColors.error,
                        onTap: () {
                          DialogHelper.confirmDialog(
                            title:
                                "Are you sure you want to remove ${member.name}?",
                            onConfirm: removeMember,
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
  );
}

Widget _buildActionHeader(BuildContext context, SharedLockerMember member) {
  return Container(
    padding: const EdgeInsets.fromLTRB(24, 20, 24, 16),
    decoration: BoxDecoration(
      gradient: IronLockerColors.getPrimaryGradient(opacity: 0.1),
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(24),
        topRight: Radius.circular(24),
      ),
      border: Border(
        bottom: BorderSide(
          color: IronLockerColors.blue02.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
    ),
    child: Column(
      children: [
        // Handle bar
        Container(
          width: 40,
          height: 4,
          decoration: BoxDecoration(
            color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(height: 20),

        // Member info
        Row(
          children: [
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: IronLockerColors.blue02,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: IronLockerColors.blue02.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Icon(
                Icons.person_outlined,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    member.name,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: IronLockerColors.getThemeTextPrimary(
                        Get.isDarkMode,
                      ),
                      fontFamily: 'Jura',
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    member.email,
                    style: TextStyle(
                      fontSize: 14,
                      color: IronLockerColors.getThemeTextSecondary(
                        Get.isDarkMode,
                      ),
                      fontFamily: 'Jura',
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    ),
  );
}

Widget _buildActionItem({
  required BuildContext context,
  required IconData icon,
  required String title,
  required String subtitle,
  required Color color,
  required VoidCallback onTap,
}) {
  return Container(
    decoration: BoxDecoration(
      color: IronLockerColors.getThemeCard(Get.isDarkMode),
      borderRadius: BorderRadius.circular(16),
      border: Border.all(
        color: IronLockerColors.getThemeBorder(Get.isDarkMode),
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.2 : 0.05),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: color.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: IronLockerColors.getThemeTextPrimary(
                          Get.isDarkMode,
                        ),
                        fontFamily: 'Jura',
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: IronLockerColors.getThemeTextSecondary(
                          Get.isDarkMode,
                        ),
                        fontFamily: 'Jura',
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}
