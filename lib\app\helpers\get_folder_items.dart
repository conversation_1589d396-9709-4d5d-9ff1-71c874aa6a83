List<String> getFolderItems({required List items, required String folderId}) {
  List<String> folderItems = [];

  findItems(String currentFolderId) {
    final references =
        items.where((item) => item?['folder'] == currentFolderId).toList();

    for (final reference in references) {
      folderItems.add(reference['_id']);
      if (reference['itemType'] == "Folder") {
        findItems(reference['_id']);
      }
    }
  }

  findItems(folderId);

  return folderItems;
}
