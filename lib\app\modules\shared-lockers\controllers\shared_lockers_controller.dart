import 'package:get/get.dart';
import 'package:ironlocker/app/services/shared_locker.dart';
import 'package:ironlocker/app/stores/shared_lockers.dart';

class SharedLockersController extends GetxController {
  var loading = true.obs;
  var error = "".obs;
  var refetching = false.obs;

  var sharedLockerService = Get.find<SharedLockerService>();

  @override
  void onInit() async {
    await fetchSharedLockers();
    loading(false);

    super.onInit();
  }

  Future fetchSharedLockers() async {
    if (error.isNotEmpty) {
      loading(true);
    }

    error("");
    var resp = await SharedLockersStore.to.fetchSharedLockers();
    loading(false);

    if (!resp) {
      error("Something went wrong");
    }
  }
}
