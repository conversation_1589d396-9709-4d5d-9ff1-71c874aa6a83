import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/device_info.dart';
import 'package:ironlocker/app/stores/user.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// Enhanced Firebase Analytics Service for comprehensive user tracking
/// Tracks logged-in users, active users, screen visits, and custom events
class AnalyticsService extends GetxService {
  static AnalyticsService get to => Get.find();

  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  // Session tracking
  DateTime? _sessionStartTime;
  String? _currentScreen;
  final Map<String, int> _screenVisitCounts = {};
  final Map<String, Duration> _screenTimeSpent = {};
  final Map<String, DateTime> _screenStartTimes = {};

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeAnalytics();
  }

  /// Initialize analytics with app and device information
  Future<void> _initializeAnalytics() async {
    try {
      // Enable analytics collection
      await _analytics.setAnalyticsCollectionEnabled(true);

      // Set session timeout for better tracking
      await _analytics.setSessionTimeoutDuration(const Duration(minutes: 30));

      // Set app info
      final packageInfo = await PackageInfo.fromPlatform();
      await _analytics.setDefaultEventParameters({
        'app_version': packageInfo.version,
        'app_build': packageInfo.buildNumber,
        'platform': GetPlatform.isAndroid ? 'android' : 'ios',
      });

      // Set device info
      final deviceInfo = await DeviceInfo.device();
      await _analytics.setUserProperty(name: 'device_info', value: deviceInfo);

      // Log app open event
      await logAppOpen();
    } catch (e) {
      // Silently handle analytics initialization errors in production
    }
  }

  /// Helper method to safely convert dynamic parameters to Object parameters
  Map<String, Object> _sanitizeParameters(Map<String, dynamic>? parameters) {
    final sanitized = <String, Object>{};
    if (parameters != null) {
      parameters.forEach((key, value) {
        if (value != null) {
          // Ensure value is a valid Object type for Firebase Analytics
          if (value is String || value is num || value is bool) {
            sanitized[key] = value;
          } else {
            // Convert other types to string
            sanitized[key] = value.toString();
          }
        }
      });
    }
    return sanitized;
  }

  /// Log app open event for production
  Future<void> logAppOpen() async {
    try {
      await _analytics.logAppOpen();
    } catch (e) {
      // Silently handle analytics errors in production
    }
  }

  /// Set user properties when user logs in
  Future<void> setUserProperties({
    required String userId,
    required String email,
    String? name,
    bool? twoFactorEnabled,
  }) async {
    try {
      await _analytics.setUserId(id: userId);
      await _analytics.setUserProperty(name: 'user_email', value: email);

      if (name != null) {
        await _analytics.setUserProperty(name: 'user_name', value: name);
      }

      if (twoFactorEnabled != null) {
        await _analytics.setUserProperty(
          name: 'two_factor_enabled',
          value: twoFactorEnabled.toString(),
        );
      }

      // Track user type
      await _analytics.setUserProperty(
        name: 'user_type',
        value: 'authenticated',
      );
    } catch (e) {
      // Silently handle analytics errors in production
    }
  }

  /// Track user login with method
  Future<void> trackLogin({String method = 'email'}) async {
    try {
      await _analytics.logLogin(loginMethod: method);
      await _analytics.logEvent(
        name: 'user_login_success',
        parameters: {
          'login_method': method,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      _startSession();
    } catch (e) {
      // Silently handle analytics errors in production
    }
  }

  /// Track user logout
  Future<void> trackLogout({String reason = 'user_initiated'}) async {
    try {
      await _analytics.logEvent(
        name: 'user_logout',
        parameters: {
          'logout_reason': reason,
          'session_duration': _getSessionDuration(),
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      _endSession();
      await _clearUserProperties();
    } catch (e) {
      // Silently handle analytics errors in production
    }
  }

  /// Track screen views with enhanced data
  Future<void> trackScreenView({
    required String screenName,
    String? screenClass,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      // End previous screen tracking
      if (_currentScreen != null) {
        _endScreenTracking(_currentScreen!);
      }

      // Start new screen tracking
      _startScreenTracking(screenName);

      await _analytics.logScreenView(
        screenName: screenName,
        screenClass: screenClass ?? screenName,
      );

      // Track custom screen view event with additional data
      final eventParameters = <String, Object>{
        'screen_name': screenName,
        'screen_class': screenClass ?? screenName,
        'visit_count': _screenVisitCounts[screenName] ?? 1,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      // Safely add user ID if available
      try {
        final userId = UserStore.to.id.value;
        if (userId.isNotEmpty) {
          eventParameters['user_id'] = userId;
        }
      } catch (e) {
        // UserStore might not be initialized yet
      }

      // Add custom parameters if provided (with type safety)
      eventParameters.addAll(_sanitizeParameters(parameters));

      await _analytics.logEvent(
        name: 'screen_view_enhanced',
        parameters: eventParameters,
      );

      _currentScreen = screenName;
    } catch (e) {
      // Silently handle analytics errors in production
    }
  }

  /// Track user interaction events
  Future<void> trackUserEngagement({
    required String action,
    String? category,
    String? label,
    int? value,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final eventParameters = <String, Object>{
        'action': action,
        'category': category ?? 'user_interaction',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      // Add optional parameters safely
      if (label != null) eventParameters['label'] = label;
      if (value != null) eventParameters['value'] = value;
      if (_currentScreen != null) {
        eventParameters['screen_name'] = _currentScreen!;
      }

      // Safely add user ID if available
      try {
        final userId = UserStore.to.id.value;
        if (userId.isNotEmpty) {
          eventParameters['user_id'] = userId;
        }
      } catch (e) {
        // UserStore might not be initialized yet
      }

      // Add custom parameters if provided (with type safety)
      eventParameters.addAll(_sanitizeParameters(parameters));

      await _analytics.logEvent(
        name: 'user_interaction',
        parameters: eventParameters,
      );
    } catch (e) {
      // Silently handle analytics errors in production
    }
  }

  /// Track locker item operations
  Future<void> trackLockerAction({
    required String action, // create, edit, delete, view, search
    required String itemType, // password, note, contact, folder
    int? itemCount,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final eventParameters = <String, Object>{
        'action': action,
        'item_type': itemType,
        'item_count': itemCount ?? 1,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      // Add optional parameters safely
      if (_currentScreen != null) {
        eventParameters['screen_name'] = _currentScreen!;
      }

      // Safely add user ID if available
      try {
        final userId = UserStore.to.id.value;
        if (userId.isNotEmpty) {
          eventParameters['user_id'] = userId;
        }
      } catch (e) {
        // UserStore might not be initialized yet
      }

      // Add custom parameters if provided (with type safety)
      eventParameters.addAll(_sanitizeParameters(parameters));

      await _analytics.logEvent(
        name: 'locker_action',
        parameters: eventParameters,
      );
    } catch (e) {
      // Silently handle analytics errors in production
    }
  }

  /// Track security events
  Future<void> trackSecurityEvent({
    required String
    event, // biometric_enabled, 2fa_enabled, session_locked, etc.
    bool? success,
    String? method,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final eventParameters = <String, Object>{
        'security_event': event,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      // Add optional parameters safely
      if (success != null) eventParameters['success'] = success;
      if (method != null) eventParameters['method'] = method;

      // Safely add user ID if available
      try {
        final userId = UserStore.to.id.value;
        if (userId.isNotEmpty) {
          eventParameters['user_id'] = userId;
        }
      } catch (e) {
        // UserStore might not be initialized yet
      }

      // Add custom parameters if provided (with type safety)
      eventParameters.addAll(_sanitizeParameters(parameters));

      await _analytics.logEvent(
        name: 'security_event',
        parameters: eventParameters,
      );
    } catch (e) {
      // Silently handle analytics errors in production
    }
  }

  /// Track app performance metrics
  Future<void> trackPerformance({
    required String metric, // app_start, screen_load, api_call, etc.
    required int duration, // in milliseconds
    bool? success,
    String? details,
  }) async {
    try {
      final eventParameters = <String, Object>{
        'metric': metric,
        'duration_ms': duration,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      // Add optional parameters safely
      if (success != null) eventParameters['success'] = success;
      if (details != null) eventParameters['details'] = details;
      if (_currentScreen != null) {
        eventParameters['screen_name'] = _currentScreen!;
      }

      await _analytics.logEvent(
        name: 'app_performance',
        parameters: eventParameters,
      );
    } catch (e) {
      // Silently handle analytics errors in production
    }
  }

  /// Track errors and exceptions
  Future<void> trackError({
    required String error,
    String? context,
    String? stackTrace,
    bool? fatal,
  }) async {
    try {
      final eventParameters = <String, Object>{
        'error_message': error,
        'fatal': fatal ?? false,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      // Add optional parameters safely
      final errorContext = context ?? _currentScreen;
      if (errorContext != null) {
        eventParameters['error_context'] = errorContext;
      }

      if (stackTrace != null) {
        // Truncate stack trace to avoid Firebase parameter size limits
        final truncatedStackTrace =
            stackTrace.length > 500
                ? '${stackTrace.substring(0, 500)}...'
                : stackTrace;
        eventParameters['stack_trace'] = truncatedStackTrace;
      }

      // Safely add user ID if available
      try {
        final userId = UserStore.to.id.value;
        if (userId.isNotEmpty) {
          eventParameters['user_id'] = userId;
        }
      } catch (e) {
        // UserStore might not be initialized yet
      }

      await _analytics.logEvent(name: 'app_error', parameters: eventParameters);
    } catch (e) {
      // Silently handle analytics errors in production
    }
  }

  /// Get most visited screens
  Map<String, int> getMostVisitedScreens() {
    final sortedScreens = Map.fromEntries(
      _screenVisitCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value)),
    );
    return sortedScreens;
  }

  /// Get screen time analytics
  Map<String, Duration> getScreenTimeAnalytics() {
    return Map.from(_screenTimeSpent);
  }

  /// Private helper methods
  void _startSession() {
    _sessionStartTime = DateTime.now();
  }

  void _endSession() {
    _sessionStartTime = null;
    _currentScreen = null;
  }

  int _getSessionDuration() {
    if (_sessionStartTime == null) return 0;
    return DateTime.now().difference(_sessionStartTime!).inSeconds;
  }

  void _startScreenTracking(String screenName) {
    _screenVisitCounts[screenName] = (_screenVisitCounts[screenName] ?? 0) + 1;
    _screenStartTimes[screenName] = DateTime.now();
  }

  void _endScreenTracking(String screenName) {
    final startTime = _screenStartTimes[screenName];
    if (startTime != null) {
      final duration = DateTime.now().difference(startTime);
      _screenTimeSpent[screenName] =
          (_screenTimeSpent[screenName] ?? Duration.zero) + duration;
      _screenStartTimes.remove(screenName);
    }
  }

  Future<void> _clearUserProperties() async {
    try {
      await _analytics.setUserId(id: null);
      await _analytics.setUserProperty(name: 'user_type', value: 'anonymous');
    } catch (e) {
      // Silently handle analytics errors in production
    }
  }
}
