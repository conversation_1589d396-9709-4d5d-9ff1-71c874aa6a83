import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import '../controllers/create_encryption_key_controller.dart';

class CreateEncryptionKeyView extends GetView<CreateEncryptionKeyController> {
  const CreateEncryptionKeyView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(28),
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 480),
              child: _buildForm(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: controller.formKey,
      child: Builder(
        builder:
            (context) => Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Gap(16),

                // IronLocker Logo
                Container(
                  width: double.infinity,
                  alignment: Alignment.center,
                  child: Image.asset(
                    'assets/images/blue_full_logo.png',
                    width: 180,
                    height: 40,
                    fit: BoxFit.contain,
                  ),
                ),
                const Gap(24),

                // Subtitle
                Text(
                  "Create your encryption key to continue",
                  style: TextStyle(
                    fontSize: _getResponsiveSubtitleSize(context),
                    fontFamily: 'Jura',
                    color:
                        Get.isDarkMode
                            ? Colors.grey.shade300
                            : Colors.grey.shade600,
                  ),
                  textAlign: TextAlign.center,
                ),
                const Gap(32),

                // Encryption Key Field
                Obx(
                  () => _buildFormField(
                    label: "Encryption Key",
                    hintText: "Enter your encryption key",
                    obscureText: !controller.showEncryptionKey.value,
                    controller: controller.encryptionKeyController,
                    showPasswordToggle: true,
                    onTogglePassword: controller.toggleEncryptionKeyVisibility,
                  ),
                ),
                const Gap(20),

                // Confirm Encryption Key Field
                Obx(
                  () => _buildFormField(
                    label: "Confirm Encryption Key",
                    hintText: "Confirm your encryption key",
                    obscureText: !controller.showConfirmEncryptionKey.value,
                    controller: controller.confirmEncryptionKeyController,
                    showPasswordToggle: true,
                    onTogglePassword:
                        controller.toggleConfirmEncryptionKeyVisibility,
                  ),
                ),
                const Gap(20),

                // Password Requirements Checklist
                _buildPasswordRequirements(),
                const Gap(24),

                // Create Key Button
                Obx(
                  () => _buildPrimaryButton(
                    text: controller.loading.value ? "Creating..." : "CREATE",
                    loading: controller.loading.value,
                    onPressed:
                        controller.canCreate.value
                            ? controller.handleSaveKeys
                            : () {},
                    enabled: controller.canCreate.value,
                  ),
                ),
                const Gap(40),
              ],
            ),
      ),
    );
  }

  // Helper method to build simple form field
  Widget _buildFormField({
    required String label,
    required String hintText,
    required TextEditingController controller,
    TextInputType? keyboardType,
    bool obscureText = false,
    bool showPasswordToggle = false,
    VoidCallback? onTogglePassword,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Text(
          label.toUpperCase(),
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            fontFamily: 'Jura',
            color: Get.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
          ),
        ),
        const Gap(8),

        // Input Field
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          obscureText: obscureText,
          style: TextStyle(
            fontSize: 14,
            fontFamily: 'Jura',
            fontWeight: FontWeight.w400,
            color: Get.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
          ),
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: TextStyle(
              color:
                  Get.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade500,
              fontFamily: 'Jura',
              fontSize: 14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color:
                    Get.isDarkMode
                        ? Colors.grey.shade600
                        : Colors.grey.shade300,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color:
                    Get.isDarkMode
                        ? Colors.grey.shade600
                        : Colors.grey.shade300,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: IronLockerColors.blue02, width: 2),
            ),
            filled: true,
            fillColor: Get.isDarkMode ? Colors.grey.shade800 : Colors.white,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
            suffixIcon:
                showPasswordToggle
                    ? Container(
                      width: 40,
                      height: 40,
                      margin: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: IronLockerColors.blue02.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color: IronLockerColors.blue02.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: onTogglePassword,
                          borderRadius: BorderRadius.circular(6),
                          child: Icon(
                            obscureText
                                ? Icons.visibility_outlined
                                : Icons.visibility_off_outlined,
                            color: IronLockerColors.blue02,
                            size: 18,
                          ),
                        ),
                      ),
                    )
                    : null,
          ),
        ),
      ],
    );
  }

  // Helper method to build password requirements checklist
  Widget _buildPasswordRequirements() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            Get.isDarkMode
                ? Colors.grey.shade800.withValues(alpha: 0.5)
                : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Get.isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "ENCRYPTION KEY REQUIREMENTS",
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w600,
              fontFamily: 'Jura',
              color:
                  Get.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
            ),
          ),
          const Gap(12),
          Obx(
            () => _buildRequirementItem(
              "At least 8 characters",
              controller.hasMinLength.value,
            ),
          ),
          const Gap(8),
          Obx(
            () => _buildRequirementItem(
              "One uppercase letter (A-Z)",
              controller.hasUppercase.value,
            ),
          ),
          const Gap(8),
          Obx(
            () => _buildRequirementItem(
              "One lowercase letter (a-z)",
              controller.hasLowercase.value,
            ),
          ),
          const Gap(8),
          Obx(
            () => _buildRequirementItem(
              "One number (0-9)",
              controller.hasNumber.value,
            ),
          ),
          const Gap(8),
          Obx(
            () => _buildRequirementItem(
              "One special character (!@#\$%^&*)",
              controller.hasSpecialChar.value,
            ),
          ),
          const Gap(8),
          Obx(
            () => _buildRequirementItem(
              "Encryption keys match",
              controller.keysMatch.value,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build individual requirement item
  Widget _buildRequirementItem(String text, bool isValid) {
    return Row(
      children: [
        Icon(
          isValid ? Icons.check_circle : Icons.radio_button_unchecked,
          size: 16,
          color:
              isValid
                  ? Colors.green.shade600
                  : (Get.isDarkMode
                      ? Colors.grey.shade500
                      : Colors.grey.shade400),
        ),
        const Gap(8),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 13,
              fontFamily: 'Jura',
              fontWeight: FontWeight.w400,
              color:
                  isValid
                      ? (Get.isDarkMode
                          ? Colors.green.shade400
                          : Colors.green.shade700)
                      : (Get.isDarkMode
                          ? Colors.grey.shade400
                          : Colors.grey.shade600),
            ),
          ),
        ),
      ],
    );
  }

  // Helper method to build primary button
  Widget _buildPrimaryButton({
    required String text,
    required VoidCallback onPressed,
    bool loading = false,
    bool enabled = true,
  }) {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        color:
            enabled && !loading
                ? IronLockerColors.blue02
                : IronLockerColors.blue02.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: enabled && !loading ? onPressed : null,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            child:
                loading
                    ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        ),
                        const Gap(12),
                        Text(
                          text,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w700,
                            fontFamily: 'Jura',
                            color: Colors.white,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ],
                    )
                    : Center(
                      child: Text(
                        text,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w700,
                          fontFamily: 'Jura',
                          color: Colors.white,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
          ),
        ),
      ),
    );
  }

  // Helper method to get responsive subtitle size
  double _getResponsiveSubtitleSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 360) {
      return 13;
    } else if (screenWidth < 400) {
      return 14;
    } else if (screenWidth < 600) {
      return 15;
    } else if (screenWidth < 900) {
      return 16;
    } else {
      return 17;
    }
  }
}
