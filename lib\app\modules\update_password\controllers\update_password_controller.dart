import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/services/user.dart';

class UpdatePasswordController extends GetxController {
  final currentPasswordController = TextEditingController();
  final newPasswordController = TextEditingController();
  final confirmPasswordController = TextEditingController();

  final userService = Get.find<UserService>();

  updatePassword() async {
    if (currentPasswordController.text.isEmpty) {
      ToastHelper.error("Kindly fill the whole form");
      return;
    }

    if (newPasswordController.text.isEmpty ||
        newPasswordController.text != confirmPasswordController.text) {
      return;
    }

    DialogHelper.loading();

    final resp = await userService.updatePassword(
      currentPassword: currentPasswordController.text,
      newPassword: newPasswordController.text,
    );
    Get.back();

    if (resp.hasError) {
      ToastHelper.error(
        resp.body != null ? resp.body['error'] : "Something went wrong",
      );
      return;
    }

    currentPasswordController.clear();
    newPasswordController.clear();
    confirmPasswordController.clear();

    ToastHelper.success("Password updated");
  }
}
