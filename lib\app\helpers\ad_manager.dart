import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';

/// Singleton class to manage AdMob initialization and global ad settings
class AdManager {
  static final AdManager _instance = AdManager._internal();
  factory AdManager() => _instance;
  AdManager._internal();

  static AdManager get instance => _instance;

  bool _isInitialized = false;
  bool _initializationFailed = false;
  String? _initializationError;
  final Completer<bool> _initCompleter = Completer<bool>();

  /// Initialize AdMob SDK with enhanced error handling
  Future<bool> initialize() async {
    if (_isInitialized) return true;
    if (_initializationFailed) return false;
    if (_initCompleter.isCompleted) return _initCompleter.future;

    try {
      debugPrint('Initializing AdMob SDK...');

      // Initialize with enhanced configuration
      await MobileAds.instance.initialize();

      // Set request configuration for better ad targeting
      await MobileAds.instance.updateRequestConfiguration(
        RequestConfiguration(
          tagForChildDirectedTreatment: TagForChildDirectedTreatment.no,
          tagForUnderAgeOfConsent: TagForUnderAgeOfConsent.no,
          maxAdContentRating: MaxAdContentRating.t,
          testDeviceIds: kDebugMode ? ['YOUR_TEST_DEVICE_ID'] : [],
        ),
      );

      _isInitialized = true;
      debugPrint('AdMob SDK initialized successfully');

      if (!_initCompleter.isCompleted) {
        _initCompleter.complete(true);
      }

      return true;
    } catch (e) {
      _initializationFailed = true;
      _initializationError = e.toString();
      debugPrint('AdMob SDK initialization failed: $e');

      if (!_initCompleter.isCompleted) {
        _initCompleter.complete(false);
      }

      return false;
    }
  }

  /// Check if AdMob is properly initialized
  bool get isInitialized => _isInitialized;

  /// Get initialization error if any
  String? get initializationError => _initializationError;

  /// Wait for initialization to complete
  Future<bool> waitForInitialization() => _initCompleter.future;

  /// Check if the current device/network supports ads
  Future<bool> isAdSupportAvailable() async {
    try {
      // Wait for initialization
      await waitForInitialization();

      if (!_isInitialized) return false;

      // Additional checks can be added here
      // For example, checking network connectivity, device capabilities, etc.

      return true;
    } catch (e) {
      debugPrint('Ad support check failed: $e');
      return false;
    }
  }

  /// Get optimized ad request with enhanced targeting
  AdRequest getOptimizedAdRequest() {
    return const AdRequest(
      keywords: [
        'security',
        'privacy',
        'password',
        'vault',
        'encryption',
        'protection',
        'mobile',
        'app',
        'utility',
        'productivity',
      ],
      nonPersonalizedAds: false,
    );
  }

  /// Reset initialization state (useful for testing)
  void reset() {
    _isInitialized = false;
    _initializationFailed = false;
    _initializationError = null;
  }

  /// Report ad loading errors to Crashlytics with proper filtering
  static void reportAdError({
    required String adType,
    required String errorMessage,
    required int errorCode,
    required int retryAttempt,
    Map<String, dynamic>? additionalData,
  }) {
    try {
      // Only report significant errors to avoid spam
      bool shouldReport = _shouldReportError(errorCode, retryAttempt);

      if (shouldReport && !kDebugMode) {
        // Create structured error data
        Map<String, dynamic> errorData = {
          'ad_type': adType,
          'error_code': errorCode,
          'error_message': errorMessage,
          'retry_attempt': retryAttempt,
          'platform': Platform.operatingSystem,
          'timestamp': DateTime.now().toIso8601String(),
          ...?additionalData,
        };

        // Report as non-fatal error with context
        FirebaseCrashlytics.instance.recordError(
          'AdMob Loading Error',
          StackTrace.current,
          fatal: false,
          information: [DiagnosticsProperty('errorData', errorData)],
        );

        // Set custom keys for better filtering in Crashlytics
        FirebaseCrashlytics.instance.setCustomKey('ad_error_type', adType);
        FirebaseCrashlytics.instance.setCustomKey('ad_error_code', errorCode);
        FirebaseCrashlytics.instance.setCustomKey(
          'ad_retry_count',
          retryAttempt,
        );
      }
    } catch (e) {
      // Don't let Crashlytics reporting break ad functionality
      debugPrint('Failed to report ad error to Crashlytics: $e');
    }
  }

  /// Determine if an ad error should be reported to Crashlytics
  static bool _shouldReportError(int errorCode, int retryAttempt) {
    // Don't report common/expected errors
    if (errorCode == 0 || errorCode == 3) {
      // No fill errors
      return retryAttempt >= 3; // Only report after multiple failures
    }

    // Report network errors only after several attempts
    if (errorCode == 2) {
      // Network error
      return retryAttempt >= 2;
    }

    // Report invalid request errors immediately (these indicate config issues)
    if (errorCode == 1) {
      // Invalid request
      return true;
    }

    // Report other errors after first retry
    return retryAttempt >= 1;
  }
}
