import 'dart:async';
import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/helpers.dart';
import 'package:ironlocker/app/helpers/storage.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/helpers/interstitial_ad_manager.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/user_sessions.dart';
import 'package:ironlocker/app/stores/user_sessions.dart';

class SessionsController extends GetxController {
  var loading = true.obs;
  var refetching = false.obs;
  var userSessionsStore = Get.find<UserSessionsStore>();
  var userSessionsService = Get.find<UserSessionsService>();

  @override
  void onInit() async {
    if (UserSessionsStore.to.userSessions.isEmpty) {
      await fetchSessions();
      loading(false);
    } else {
      loading(false);
    }

    // Preload interstitial ad for better user experience
    _preloadInterstitialAd();

    super.onInit();
  }

  /// Helper method to show interstitial ad and execute callback ONLY after ad interaction
  Future<void> _showInterstitialAdAndExecute(
    Future<void> Function() callback,
  ) async {
    debugPrint(
      '🎯 Loading interstitial ad - action blocked until ad interaction complete',
    );

    await InterstitialAdManager.instance.loadAndShowWithCallback(
      useTestAds: kDebugMode,
      onAdDismissedOrFailed: () async {
        debugPrint('🎯 Ad interaction complete, executing action');
        await callback();
      },
    );
  }

  /// Preload interstitial ad for session termination
  void _preloadInterstitialAd() {
    debugPrint('🔄 Interstitial ads will be loaded on-demand when needed');
    // Note: Ads are loaded on-demand for better memory management
    // and to ensure ads are fresh when displayed.
  }

  Future fetchSessions() async {
    await userSessionsStore.fetchSessions();
  }

  terminateAllSessions() async {
    // Load and show interstitial ad directly
    await _showInterstitialAdAndExecute(() async {
      await _performTerminateAllSessions();
    });
  }

  /// Performs the actual session termination logic
  Future<void> _performTerminateAllSessions() async {
    DialogHelper.loading();

    final storage = Storage();

    var resp = await userSessionsService.terminateAllSessions();

    if (resp.hasError) {
      if (resp.body?["error"] == "SESSION_LOCKED") {
        Get.offNamed(Routes.lockedLocker);
        return;
      } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
          resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
        await Helpers.clearData();
        Get.offAllNamed(Routes.signin);
        return;
      }

      Get.back();

      ToastHelper.error("Something went wrong");
      return;
    }

    await storage.deleteItem("accessToken");
    await storage.deleteItem("refreshToken");

    Get.offAllNamed(Routes.signin);
  }

  terminateSession(String sessionId) async {
    Get.back();

    // Load and show interstitial ad directly
    await _showInterstitialAdAndExecute(() async {
      await _performTerminateSession(sessionId);
    });
  }

  /// Performs the actual individual session termination logic
  Future<void> _performTerminateSession(String sessionId) async {
    DialogHelper.loading();

    var resp = await userSessionsService.terminateSession(sessionId);

    Get.back();

    if (resp.hasError) {
      if (resp.body?["error"] == "SESSION_LOCKED") {
        Get.offNamed(Routes.lockedLocker);
        return;
      } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
          resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
        await Helpers.clearData();
        Get.offAllNamed(Routes.signin);
        return;
      }

      ToastHelper.error("Something went wrong");
      return;
    }

    userSessionsStore.removeSession(sessionId);

    ToastHelper.success("Session terminated");
  }

  lockSession(String sessionId) async {
    Get.back();
    DialogHelper.loading();

    var resp = await userSessionsService.lockSession(sessionId);

    Get.back();

    if (resp.hasError) {
      if (resp.body?["error"] == "SESSION_LOCKED") {
        Get.offNamed(Routes.lockedLocker);
        return;
      } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
          resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
        await Helpers.clearData();
        Get.offAllNamed(Routes.signin);
        return;
      }

      ToastHelper.error("Something went wrong");
      return;
    }

    userSessionsStore.lockSession(sessionId);

    ToastHelper.success("Session locked");
  }
}
