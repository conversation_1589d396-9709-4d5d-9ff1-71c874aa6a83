import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:ironlocker/app/helpers/ad_free_manager.dart';
import 'package:ironlocker/app/helpers/ad_helper.dart';
import 'package:ironlocker/app/helpers/ad_manager.dart';
import 'package:ironlocker/app/stores/user.dart';

/// Manages interstitial ads for session termination and other key actions
class InterstitialAdManager {
  static final InterstitialAdManager _instance =
      InterstitialAdManager._internal();
  factory InterstitialAdManager() => _instance;
  InterstitialAdManager._internal();

  static InterstitialAdManager get instance => _instance;

  InterstitialAd? _interstitialAd;
  bool _isLoading = false;
  bool _isShowing = false;
  int _loadAttempts = 0;
  static const int _maxLoadAttempts = 3;
  static const int _loadTimeoutSeconds = 3; // Faster timeout for better UX

  /// Load an interstitial ad for session termination
  Future<bool> loadSessionTerminationAd({bool useTestAds = false}) async {
    if (_isLoading || _interstitialAd != null) {
      debugPrint('InterstitialAd: Already loading or loaded');
      return _interstitialAd != null;
    }

    // Check if AdMob is initialized
    if (!AdManager.instance.isInitialized) {
      debugPrint('InterstitialAd: AdMob not initialized, waiting...');
      final initialized = await AdManager.instance.waitForInitialization();
      if (!initialized) {
        debugPrint('InterstitialAd: AdMob initialization failed');
        return false;
      }
    }

    _isLoading = true;
    _loadAttempts++;

    try {
      debugPrint(
        'InterstitialAd: Loading session termination ad (attempt $_loadAttempts/$_maxLoadAttempts)',
      );

      final adUnitId =
          useTestAds
              ? AdHelper.testInterstitialAdUnitId
              : AdHelper.interstitialAdUnitId;

      final completer = Completer<bool>();

      await InterstitialAd.load(
        adUnitId: adUnitId,
        request: AdManager.instance.getOptimizedAdRequest(),
        adLoadCallback: InterstitialAdLoadCallback(
          onAdLoaded: (InterstitialAd ad) {
            debugPrint(
              'InterstitialAd: Session termination ad loaded successfully',
            );
            _interstitialAd = ad;
            _isLoading = false;
            _loadAttempts = 0;
            _setupAdCallbacks();
            if (!completer.isCompleted) completer.complete(true);
          },
          onAdFailedToLoad: (LoadAdError error) {
            debugPrint(
              'InterstitialAd: Failed to load session termination ad: ${error.message}',
            );
            _isLoading = false;

            // Log to Crashlytics for monitoring
            if (!kDebugMode) {
              FirebaseCrashlytics.instance.recordError(
                'InterstitialAd load failed',
                StackTrace.current,
                information: [
                  'Error Code: ${error.code}',
                  'Error Message: ${error.message}',
                  'Load Attempt: $_loadAttempts',
                ],
              );
            }

            if (!completer.isCompleted) completer.complete(false);
          },
        ),
      );

      // Wait for load completion with timeout
      return await completer.future.timeout(
        const Duration(seconds: _loadTimeoutSeconds),
        onTimeout: () {
          debugPrint(
            'InterstitialAd: Load timeout after $_loadTimeoutSeconds seconds',
          );
          _isLoading = false;
          return false;
        },
      );
    } catch (e) {
      debugPrint('InterstitialAd: Exception during load: $e');
      _isLoading = false;

      if (!kDebugMode) {
        FirebaseCrashlytics.instance.recordError(
          'InterstitialAd load exception',
          StackTrace.current,
          information: ['Exception: $e', 'Load Attempt: $_loadAttempts'],
        );
      }

      return false;
    }
  }

  /// Setup callbacks for the loaded interstitial ad
  void _setupAdCallbacks() {
    _interstitialAd?.fullScreenContentCallback = FullScreenContentCallback(
      onAdShowedFullScreenContent: (InterstitialAd ad) {
        debugPrint(
          'InterstitialAd: Session termination ad showed full screen content',
        );
        _isShowing = true;
      },
      onAdDismissedFullScreenContent: (InterstitialAd ad) {
        debugPrint('InterstitialAd: Session termination ad dismissed');
        _isShowing = false;
        ad.dispose();
        _interstitialAd = null;
        // Preload next ad for future use
        _preloadNextAd();
      },
      onAdFailedToShowFullScreenContent: (InterstitialAd ad, AdError error) {
        debugPrint(
          'InterstitialAd: Failed to show session termination ad: ${error.message}',
        );
        _isShowing = false;
        ad.dispose();
        _interstitialAd = null;

        if (!kDebugMode) {
          FirebaseCrashlytics.instance.recordError(
            'InterstitialAd show failed',
            StackTrace.current,
            information: [
              'Error Code: ${error.code}',
              'Error Message: ${error.message}',
            ],
          );
        }
      },
      onAdClicked: (InterstitialAd ad) {
        debugPrint('InterstitialAd: Session termination ad clicked');
      },
    );
  }

  /// Show the loaded interstitial ad before session termination
  Future<bool> showSessionTerminationAd() async {
    if (_interstitialAd == null) {
      debugPrint('InterstitialAd: No ad loaded to show');
      return false;
    }

    if (_isShowing) {
      debugPrint('InterstitialAd: Ad is already showing');
      return false;
    }

    try {
      debugPrint('InterstitialAd: Showing session termination ad');
      await _interstitialAd!.show();
      return true;
    } catch (e) {
      debugPrint('InterstitialAd: Exception while showing ad: $e');

      if (!kDebugMode) {
        FirebaseCrashlytics.instance.recordError(
          'InterstitialAd show exception',
          StackTrace.current,
          information: ['Exception: $e'],
        );
      }

      return false;
    }
  }

  /// Load and show interstitial ad in one call (convenience method)
  Future<bool> loadAndShowSessionTerminationAd({
    bool useTestAds = false,
  }) async {
    final loaded = await loadSessionTerminationAd(useTestAds: useTestAds);
    if (loaded) {
      return await showSessionTerminationAd();
    }
    return false;
  }

  /// Load and show interstitial ad with callback when dismissed
  /// This method blocks until the ad is dismissed or fails
  Future<void> loadAndShowWithCallback({
    bool useTestAds = false,
    required VoidCallback onAdDismissedOrFailed,
  }) async {
    debugPrint(
      '🎯 Loading interstitial ad with callback - blocking until interaction complete',
    );

    // Check if user has premium or lifetime plan
    if (UserStore.to.plan["name"] == "premium" ||
        UserStore.to.plan["name"] == "lifetime") {
      debugPrint(
        '🚫 Skipping interstitial ad - user has premium/lifetime plan',
      );
      onAdDismissedOrFailed();
      return;
    }

    try {
      final loaded = await loadSessionTerminationAd(useTestAds: useTestAds);

      if (loaded && _interstitialAd != null) {
        debugPrint(
          '✅ Interstitial ad loaded, setting up callback and showing...',
        );

        // Create completer to wait for ad interaction
        final Completer<void> adCompleter = Completer<void>();

        // Setup callback to complete when ad is dismissed
        _interstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
          onAdShowedFullScreenContent: (InterstitialAd ad) {
            debugPrint('📱 Interstitial ad showing - user must interact');
            _isShowing = true;
          },
          onAdDismissedFullScreenContent: (InterstitialAd ad) {
            debugPrint('✅ Interstitial ad dismissed - proceeding with action');
            _isShowing = false;
            ad.dispose();
            _interstitialAd = null;
            onAdDismissedOrFailed();
            if (!adCompleter.isCompleted) {
              adCompleter.complete();
            }
            // Preload next ad for future use
            _preloadNextAd();
          },
          onAdFailedToShowFullScreenContent: (
            InterstitialAd ad,
            AdError error,
          ) {
            debugPrint(
              '❌ Interstitial ad failed to show: ${error.message} - proceeding with action',
            );
            _isShowing = false;
            ad.dispose();
            _interstitialAd = null;
            onAdDismissedOrFailed();
            if (!adCompleter.isCompleted) {
              adCompleter.complete();
            }

            if (!kDebugMode) {
              FirebaseCrashlytics.instance.recordError(
                'InterstitialAd show failed',
                StackTrace.current,
                information: [
                  'Error Code: ${error.code}',
                  'Error Message: ${error.message}',
                ],
              );
            }
          },
          onAdClicked: (InterstitialAd ad) {
            debugPrint('👆 Interstitial ad clicked');
          },
        );

        // Show the ad
        try {
          await _interstitialAd!.show();
          debugPrint('⏳ Waiting for user to dismiss interstitial ad...');
          // Block here until ad is dismissed or fails
          await adCompleter.future;
          debugPrint('🎯 Ad interaction complete');
        } catch (e) {
          debugPrint('❌ Exception showing interstitial ad: $e');
          onAdDismissedOrFailed();
        }
      } else {
        debugPrint('⚠️ Failed to load interstitial ad, proceeding anyway');
        onAdDismissedOrFailed();
      }
    } catch (e) {
      debugPrint('❌ Exception in loadAndShowWithCallback: $e');
      onAdDismissedOrFailed();
    }
  }

  /// Preload the next ad for better user experience
  void _preloadNextAd() {
    // Wait a bit before preloading to avoid rapid successive requests
    Future.delayed(const Duration(seconds: 2), () {
      if (_interstitialAd == null && !_isLoading) {
        loadSessionTerminationAd(useTestAds: kDebugMode);
      }
    });
  }

  /// Check if an interstitial ad is ready to show
  bool get isAdReady => _interstitialAd != null && !_isShowing;

  /// Check if an ad is currently loading
  bool get isLoading => _isLoading;

  /// Check if an ad is currently showing
  bool get isShowing => _isShowing;

  /// Dispose of the current ad (cleanup)
  void dispose() {
    debugPrint('InterstitialAd: Disposing current ad');
    _interstitialAd?.dispose();
    _interstitialAd = null;
    _isLoading = false;
    _isShowing = false;
    _loadAttempts = 0;
  }

  /// Reset the manager state
  void reset() {
    dispose();
  }

  // Frequency control for item creation ads
  static int _itemCreationAdCount = 0;
  static DateTime? _lastItemCreationAdTime;
  static const int _maxItemCreationAdsPerSession = 3;
  static const int _minTimeBetweenItemAdsMinutes = 2;

  /// Show interstitial ad after successful item creation
  /// Non-blocking - shows ad but doesn't wait for dismissal
  Future<void> showItemCreationAd({bool useTestAds = false}) async {
    try {
      debugPrint('🎯 Showing interstitial ad for item creation');

      // Check if user has premium or lifetime plan
      if (UserStore.to.plan["name"] == "premium" ||
          UserStore.to.plan["name"] == "lifetime") {
        debugPrint(
          '🚫 Skipping item creation ad - user has premium/lifetime plan',
        );
        return;
      }

      // Check if user has ad-free experience active
      final isAdFree = await AdFreeManager.instance.isAdFreeActive();
      if (isAdFree) {
        debugPrint(
          '🚫 Skipping item creation ad - user has ad-free experience',
        );
        return;
      }

      // Check if we should show ad (frequency control)
      if (!_shouldShowItemCreationAd()) {
        debugPrint('⏭️ Skipping item creation ad due to frequency control');
        return;
      }

      final loaded = await loadSessionTerminationAd(useTestAds: useTestAds);

      if (loaded && _interstitialAd != null) {
        _setupAdCallbacks();
        await _interstitialAd!.show();
        _updateItemCreationAdFrequency();
        debugPrint('✅ Item creation ad shown successfully');
      } else {
        debugPrint('⚠️ Failed to load item creation ad');
      }
    } catch (e) {
      debugPrint('❌ Error showing item creation ad: $e');
    }
  }

  bool _shouldShowItemCreationAd() {
    // Check session limit
    if (_itemCreationAdCount >= _maxItemCreationAdsPerSession) {
      return false;
    }

    // Check time limit
    if (_lastItemCreationAdTime != null) {
      final timeSinceLastAd = DateTime.now().difference(
        _lastItemCreationAdTime!,
      );
      if (timeSinceLastAd.inMinutes < _minTimeBetweenItemAdsMinutes) {
        return false;
      }
    }

    return true;
  }

  void _updateItemCreationAdFrequency() {
    _itemCreationAdCount++;
    _lastItemCreationAdTime = DateTime.now();
  }
}
