import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/modules/shared-locker/models/shared_locker_member.dart';
import 'package:ironlocker/app/services/shared_locker_member.dart';
import 'package:ironlocker/app/stores/shared_lockers.dart';

void sharedLockerMemberPermissionSheet({
  required BuildContext context,
  required SharedLockerMember member,
}) {
  var selectedPermission = member.permission.obs;
  var loading = false.obs;

  final sharedLockersStore = Get.find<SharedLockersStore>();
  final sharedLockerMemberService = Get.find<SharedLockerMemberService>();

  handleSubmit() async {
    DialogHelper.loading();

    var resp = await sharedLockerMemberService.updatePermission(
      permission: selectedPermission.value,
      membershipId: member.membershipId,
    );

    if (resp.hasError) {
      Get.back();
      if (resp.statusCode == 404) {
        ToastHelper.error("User with this email doesn't exist");
      } else if (resp.statusCode == 409) {
        ToastHelper.error("Already a member");
      } else if (resp.body?["error"] == "MEMBERSHIP_LIMIT_REACHED") {
        ToastHelper.error("Membership limit reached");
      } else {
        ToastHelper.error("Something went wrong");
      }
      return;
    }

    await sharedLockersStore.fetchSharedLockers();

    Get.back();
    Get.back();
    Get.back();
    ToastHelper.success("Permission updated");
  }

  showModalBottomSheet(
    context: context,
    elevation: 0,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder:
        (context) => SafeArea(
          child: Container(
            height: MediaQuery.of(context).size.height * 0.75,
            decoration: BoxDecoration(
              color: IronLockerColors.getThemeBackground(Get.isDarkMode),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: Column(
              children: [
                // Header Section
                _buildPermissionHeader(context, member),

                // Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      children: [
                        // Member Info Card
                        _buildMemberInfoCard(member),

                        const SizedBox(height: 24),

                        // Permission Options
                        _buildPermissionOptions(selectedPermission),

                        const SizedBox(height: 32),

                        // Submit Button
                        _buildUpdateButton(
                          handleSubmit,
                          loading,
                          selectedPermission,
                          member.permission,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
  );
}

Widget _buildPermissionHeader(BuildContext context, SharedLockerMember member) {
  return Container(
    padding: const EdgeInsets.fromLTRB(24, 20, 24, 16),
    decoration: BoxDecoration(
      gradient: IronLockerColors.getPrimaryGradient(opacity: 0.1),
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(24),
        topRight: Radius.circular(24),
      ),
      border: Border(
        bottom: BorderSide(
          color: IronLockerColors.blue02.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
    ),
    child: Column(
      children: [
        // Handle bar
        Container(
          width: 40,
          height: 4,
          decoration: BoxDecoration(
            color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(height: 20),

        // Header content
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: IronLockerColors.blue02.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: IronLockerColors.blue02.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Icon(
                Icons.security_outlined,
                color: IronLockerColors.blue02,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Update Permission',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: IronLockerColors.getThemeTextPrimary(
                        Get.isDarkMode,
                      ),
                      fontFamily: 'Jura',
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Change access level for ${member.name}',
                    style: TextStyle(
                      fontSize: 14,
                      color: IronLockerColors.getThemeTextSecondary(
                        Get.isDarkMode,
                      ),
                      fontFamily: 'Jura',
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    ),
  );
}

Widget _buildMemberInfoCard(SharedLockerMember member) {
  return Container(
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: IronLockerColors.getThemeCard(Get.isDarkMode),
      borderRadius: BorderRadius.circular(16),
      border: Border.all(
        color: IronLockerColors.getThemeBorder(Get.isDarkMode),
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.2 : 0.05),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Row(
      children: [
        // Avatar
        Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            color: IronLockerColors.blue02,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: IronLockerColors.blue02.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: const Icon(
            Icons.person_outlined,
            color: Colors.white,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),

        // Member Info
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                member.name,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  fontFamily: 'Jura',
                ),
              ),
              const SizedBox(height: 4),
              Text(
                member.email,
                style: TextStyle(
                  fontSize: 14,
                  color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                  fontFamily: 'Jura',
                ),
              ),
              const SizedBox(height: 8),
              _buildCurrentPermissionBadge(member.permission),
            ],
          ),
        ),
      ],
    ),
  );
}

Widget _buildCurrentPermissionBadge(String permission) {
  Color badgeColor;
  Color textColor;
  IconData icon;
  String label;

  switch (permission.toUpperCase()) {
    case 'READ':
      badgeColor = IronLockerColors.info.withValues(alpha: 0.15);
      textColor = IronLockerColors.info;
      icon = Icons.visibility_outlined;
      label = 'READ ONLY';
      break;
    case 'READ_WRITE':
    case 'WRITE':
      badgeColor = IronLockerColors.success.withValues(alpha: 0.15);
      textColor = IronLockerColors.success;
      icon = Icons.edit_outlined;
      label = 'READ & WRITE';
      break;
    case 'ADMIN':
      badgeColor = IronLockerColors.warning.withValues(alpha: 0.15);
      textColor = IronLockerColors.warning;
      icon = Icons.admin_panel_settings_outlined;
      label = 'ADMIN';
      break;
    default:
      badgeColor = IronLockerColors.blue02.withValues(alpha: 0.15);
      textColor = IronLockerColors.blue02;
      icon = Icons.person_outlined;
      label = permission.toUpperCase();
  }

  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
    decoration: BoxDecoration(
      color: badgeColor,
      borderRadius: BorderRadius.circular(12),
      border: Border.all(color: textColor.withValues(alpha: 0.3), width: 1),
    ),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 14, color: textColor),
        const SizedBox(width: 6),
        Text(
          'Current: $label',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: textColor,
            fontFamily: 'Jura',
          ),
        ),
      ],
    ),
  );
}

Widget _buildPermissionOptions(RxString selectedPermission) {
  final permissions = [
    {
      'id': 'READ',
      'name': 'Read Only',
      'description': 'Can view items but cannot add, edit, or delete',
      'icon': Icons.visibility_outlined,
      'color': IronLockerColors.info,
    },
    {
      'id': 'READ_WRITE',
      'name': 'Read & Write',
      'description': 'Can view, add, edit, and delete items',
      'icon': Icons.edit_outlined,
      'color': IronLockerColors.success,
    },
  ];

  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        'Select Permission Level',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
          fontFamily: 'Jura',
        ),
      ),
      const SizedBox(height: 16),
      ...permissions.map(
        (permission) => Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: Obx(
            () => _buildPermissionOption(
              permission: permission,
              isSelected: selectedPermission.value == permission['id'],
              onTap:
                  () => selectedPermission.value = permission['id'] as String,
            ),
          ),
        ),
      ),
    ],
  );
}

Widget _buildPermissionOption({
  required Map<String, dynamic> permission,
  required bool isSelected,
  required VoidCallback onTap,
}) {
  final color = permission['color'] as Color;

  return Container(
    decoration: BoxDecoration(
      color: IronLockerColors.getThemeCard(Get.isDarkMode),
      borderRadius: BorderRadius.circular(16),
      border: Border.all(
        color:
            isSelected
                ? color.withValues(alpha: 0.5)
                : IronLockerColors.getThemeBorder(Get.isDarkMode),
        width: isSelected ? 2 : 1,
      ),
      boxShadow: [
        BoxShadow(
          color:
              isSelected
                  ? color.withValues(alpha: 0.2)
                  : Colors.black.withValues(alpha: Get.isDarkMode ? 0.2 : 0.05),
          blurRadius: isSelected ? 12 : 8,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              // Icon
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: color.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Icon(
                  permission['icon'] as IconData,
                  color: color,
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      permission['name'] as String,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: IronLockerColors.getThemeTextPrimary(
                          Get.isDarkMode,
                        ),
                        fontFamily: 'Jura',
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      permission['description'] as String,
                      style: TextStyle(
                        fontSize: 14,
                        color: IronLockerColors.getThemeTextSecondary(
                          Get.isDarkMode,
                        ),
                        fontFamily: 'Jura',
                      ),
                    ),
                  ],
                ),
              ),

              // Selection indicator
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color:
                        isSelected
                            ? color
                            : IronLockerColors.getThemeBorder(Get.isDarkMode),
                    width: 2,
                  ),
                  color: isSelected ? color : Colors.transparent,
                ),
                child:
                    isSelected
                        ? const Icon(Icons.check, color: Colors.white, size: 16)
                        : null,
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

Widget _buildUpdateButton(
  VoidCallback onSubmit,
  RxBool loading,
  RxString selectedPermission,
  String currentPermission,
) {
  return Obx(() {
    final hasChanged = selectedPermission.value != currentPermission;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow:
            hasChanged
                ? [
                  BoxShadow(
                    color: IronLockerColors.blue02.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ]
                : [],
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient:
              hasChanged
                  ? LinearGradient(
                    colors: [IronLockerColors.blue01, IronLockerColors.blue02],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                  : null,
          color:
              hasChanged
                  ? null
                  : IronLockerColors.getThemeBorder(Get.isDarkMode),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: hasChanged && !loading.value ? onSubmit : null,
            borderRadius: BorderRadius.circular(16),
            child: Container(
              height: 56,
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (loading.value)
                    Container(
                      width: 20,
                      height: 20,
                      margin: const EdgeInsets.only(right: 12),
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          hasChanged
                              ? Colors.white
                              : IronLockerColors.getThemeTextSecondary(
                                Get.isDarkMode,
                              ),
                        ),
                      ),
                    )
                  else
                    Container(
                      margin: const EdgeInsets.only(right: 12),
                      child: Icon(
                        hasChanged
                            ? Icons.security_update_outlined
                            : Icons.security_outlined,
                        color:
                            hasChanged
                                ? Colors.white
                                : IronLockerColors.getThemeTextSecondary(
                                  Get.isDarkMode,
                                ),
                        size: 20,
                      ),
                    ),
                  Text(
                    loading.value
                        ? 'Updating Permission...'
                        : hasChanged
                        ? 'Update Permission'
                        : 'No Changes Made',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color:
                          hasChanged
                              ? Colors.white
                              : IronLockerColors.getThemeTextSecondary(
                                Get.isDarkMode,
                              ),
                      fontFamily: 'Jura',
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  });
}
