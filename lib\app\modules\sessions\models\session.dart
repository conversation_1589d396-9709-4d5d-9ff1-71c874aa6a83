class SessionData {
  final String id;
  final String user;
  final String? continent;
  final String? country;
  final String? city;
  final String? timezone;
  final String? countryFlag;
  final String ipAddress;
  final String userAgent;
  final bool locked;
  final DateTime createdAt;
  final DateTime lastActive;

  SessionData({
    required this.id,
    required this.user,
    required this.ipAddress,
    required this.userAgent,
    required this.locked,
    required this.continent,
    required this.country,
    required this.city,
    required this.timezone,
    required this.countryFlag,
    required this.createdAt,
    required this.lastActive,
  });

  factory SessionData.fromMap(Map<dynamic, dynamic> map) {
    return SessionData(
      id: map['_id'],
      user: map['user'],
      continent: map['continent'],
      country: map['country'],
      city: map['city'],
      timezone: map['timezone'],
      ipAddress: map['ipAddress'],
      userAgent: map['userAgent'],
      countryFlag: map['countryFlag'],
      locked: map['locked'],
      createdAt: DateTime.parse(map['createdAt']),
      lastActive: DateTime.parse(map['lastActive']),
    );
  }
}
