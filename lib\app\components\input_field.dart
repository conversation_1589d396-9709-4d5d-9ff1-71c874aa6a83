import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/colors.dart';
import '../constants/ironlocker_colors.dart';

class InputField extends StatelessWidget {
  const InputField({
    super.key,
    this.controller,
    this.validator,
    this.obscureText = false,
    this.enabled = true,
    this.labelText,
    this.hintText,
    this.maxLines = 1,
    this.initialValue,
    this.onChanged,
    this.keyboardType,
  });
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final bool obscureText;
  final bool enabled;
  final String? labelText;
  final String? hintText;
  final String? initialValue;
  final int? maxLines;
  final TextInputType? keyboardType;
  final void Function(String)? onChanged;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (labelText != null)
          Text(
            labelText!,
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              fontFamily: 'Jura',
              fontWeight: FontWeight.w500,
              color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
            ),
          ),
        const SizedBox(height: 8),
        TextFormField(
          initialValue: initialValue,
          keyboardType: keyboardType,
          controller: controller,
          style: TextStyle(
            fontSize: 14,
            fontFamily: 'Jura',
            color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
          ),
          onChanged: onChanged,
          validator: validator,
          autocorrect: false,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          obscureText: obscureText,
          enabled: enabled,
          maxLines: maxLines,
          cursorColor: IronLockerColors.blue02,
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.symmetric(
              vertical: 12,
              horizontal: 16,
            ),
            hintText: hintText,
            hintStyle: TextStyle(
              fontSize: 14,
              fontFamily: 'Jura',
              color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
            ),
            filled: true,
            fillColor: IronLockerColors.getThemeCard(Get.isDarkMode),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: IronLockerColors.getThemeBorder(Get.isDarkMode),
                width: 1,
              ),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: IronLockerColors.getThemeBorder(Get.isDarkMode),
                width: 1,
              ),
            ),
            errorStyle: Theme.of(context).textTheme.labelSmall!.copyWith(
              color: IronLockerColors.error,
              fontFamily: 'Jura',
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: IronLockerColors.error, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: IronLockerColors.error, width: 2),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: IronLockerColors.blue02, width: 2),
            ),
          ),
        ),
      ],
    );
  }
}
