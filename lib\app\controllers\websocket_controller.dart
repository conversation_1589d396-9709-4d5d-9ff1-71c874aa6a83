import 'dart:developer';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/storage.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/services/websocket_service.dart';
import 'package:ironlocker/app/stores/user.dart';

class WebSocketController extends GetxController {
  static WebSocketController get to => Get.find();

  final WebSocketService _webSocketService = WebSocketService.to;

  RxBool get isConnected => _webSocketService.isConnected;
  RxBool get isConnecting => _webSocketService.isConnecting;
  RxString get connectionStatus => _webSocketService.connectionStatus;

  @override
  void onInit() {
    super.onInit();
    log('WebSocketController initialized');

    _setupAuthenticationListener();

    // Only connect if user is fully authenticated (has token + verified encryption key)
    _connectIfAuthenticated();

    setupCommonListeners();
  }

  @override
  void onClose() {
    _webSocketService.disconnect();
    super.onClose();
  }

  void _setupAuthenticationListener() {
    // Only listen for token removal to disconnect WebSocket
    ever(UserStore.to.accessToken, (String token) {
      // Only auto-disconnect when token is cleared (logout)
      if (token.isEmpty) {
        log('Disconnecting WebSocket due to logout');
        _disconnectFromWebSocket();
      }
      // Note: No auto-connect on token set - connection happens explicitly
      // after encryption key verification in verify/create encryption key screens
    });
  }

  void _connectIfAuthenticated() async {
    // Only auto-connect if user is fully authenticated AND encryption key is verified
    if (UserStore.to.accessToken.value.isNotEmpty) {
      final storage = Storage();
      final encryptionKeyVerified =
          await storage.getItem("encryptionKeyVerified") ?? false;

      if (encryptionKeyVerified == true) {
        log(
          'User is fully authenticated with verified encryption key, connecting WebSocket',
        );
        _connectToWebSocket();
      } else {
        log(
          'User has token but encryption key not verified, skipping WebSocket connection',
        );
      }
    }
  }

  Future<void> _connectToWebSocket() async {
    log('Connecting to WebSocket...');
    await _webSocketService.connect();
  }

  void _disconnectFromWebSocket() {
    log('Disconnecting from WebSocket...');
    _webSocketService.disconnect();
  }

  Future<void> connect() async {
    await _webSocketService.connect();
  }

  void disconnect() {
    _webSocketService.disconnect();
  }

  Future<void> reconnect() async {
    await _webSocketService.reconnect();
  }

  void emit(String event, [dynamic data]) {
    _webSocketService.emit(event, data);
  }

  void on(String event, Function(dynamic) callback) {
    _webSocketService.on(event, callback);
  }

  void off(String event) {
    _webSocketService.off(event);
  }

  void setupCommonListeners() {
    on('notification', (data) {
      log('Received notification: $data');
      _handleNotification(data);
    });

    on('new_plan', (data) {
      log('New Plan from server: $data');
      log('WebSocket connected: ${isConnected.value}');
      log('Connection status: ${connectionStatus.value}');
      ToastHelper.success("New Plan Received");
      UserStore.to.plan(data);
    });

    on('locker_item_updated', (data) {
      log('Locker item updated: $data');
      _handleLockerItemUpdate(data);
    });

    on('shared_locker_updated', (data) {
      log('Shared locker updated: $data');
      _handleSharedLockerUpdate(data);
    });

    on('user_session_updated', (data) {
      log('User session updated: $data');
      _handleUserSessionUpdate(data);
    });

    on('security_alert', (data) {
      log('Security alert: $data');
      _handleSecurityAlert(data);
    });

    on('test_event', (data) {
      log('Test event received: $data');
      ToastHelper.success("Test event received!");
    });
  }

  void _handleNotification(dynamic data) {
    log('Processing notification: $data');
  }

  void _handleLockerItemUpdate(dynamic data) {
    log('Processing locker item update: $data');
  }

  void _handleSharedLockerUpdate(dynamic data) {
    log('Processing shared locker update: $data');
  }

  void _handleUserSessionUpdate(dynamic data) {
    log('Processing user session update: $data');
  }

  void _handleSecurityAlert(dynamic data) {
    log('Processing security alert: $data');
  }

  void joinRoom(String roomId) {
    emit('join_room', {'room': roomId});
    log('Joined room: $roomId');
  }

  void leaveRoom(String roomId) {
    emit('leave_room', {'room': roomId});
    log('Left room: $roomId');
  }

  void sendTypingIndicator(String sharedLockerId, bool isTyping) {
    emit('typing', {'shared_locker_id': sharedLockerId, 'is_typing': isTyping});
  }

  void updatePresence(String status) {
    emit('presence', {'status': status});
    log('Updated presence: $status');
  }
}
