import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/storage.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/services/websocket_service.dart';
import 'package:ironlocker/app/stores/user.dart';

class WebSocketController extends GetxController {
  static WebSocketController get to => Get.find();

  final WebSocketService _webSocketService = WebSocketService.to;

  RxBool get isConnected => _webSocketService.isConnected;
  RxBool get isConnecting => _webSocketService.isConnecting;
  RxString get connectionStatus => _webSocketService.connectionStatus;

  @override
  void onInit() {
    super.onInit();

    _setupAuthenticationListener();

    // Setup listeners BEFORE attempting connection
    setupCommonListeners();

    // Only connect if user is fully authenticated (has token + verified encryption key)
    _connectIfAuthenticated();
  }

  @override
  void onClose() {
    _webSocketService.disconnect();
    super.onClose();
  }

  void _setupAuthenticationListener() {
    // Only listen for token removal to disconnect WebSocket
    ever(UserStore.to.accessToken, (String token) {
      // Only auto-disconnect when token is cleared (logout)
      if (token.isEmpty) {
        _disconnectFromWebSocket();
      }
      // Note: No auto-connect on token set - connection happens explicitly
      // after encryption key verification in verify/create encryption key screens
    });
  }

  void _connectIfAuthenticated() async {
    // Only auto-connect if user is fully authenticated AND encryption key is verified
    if (UserStore.to.accessToken.value.isNotEmpty) {
      final storage = Storage();
      final encryptionKeyVerified =
          await storage.getItem("encryptionKeyVerified") ?? false;

      if (encryptionKeyVerified == true) {
        _connectToWebSocket();
      }
    }
  }

  Future<void> _connectToWebSocket() async {
    await _webSocketService.connect();
  }

  void _disconnectFromWebSocket() {
    _webSocketService.disconnect();
  }

  Future<void> connect() async {
    await _webSocketService.connect();
  }

  void disconnect() {
    _webSocketService.disconnect();
  }

  Future<void> reconnect() async {
    await _webSocketService.reconnect();
  }

  void emit(String event, [dynamic data]) {
    _webSocketService.emit(event, data);
  }

  void on(String event, Function(dynamic) callback) {
    _webSocketService.on(event, callback);
  }

  void off(String event) {
    _webSocketService.off(event);
  }

  void setupCommonListeners() {
    on('notification', (data) {
      _handleNotification(data);
    });

    on('new_plan', (data) {
      ToastHelper.success("New Plan Received");
      UserStore.to.plan(data);
    });

    on('locker_item_updated', (data) {
      _handleLockerItemUpdate(data);
    });

    on('shared_locker_updated', (data) {
      _handleSharedLockerUpdate(data);
    });

    on('user_session_updated', (data) {
      _handleUserSessionUpdate(data);
    });

    on('security_alert', (data) {
      _handleSecurityAlert(data);
    });

    on('test_event', (data) {
      ToastHelper.success("Test event received!");
    });
  }

  void _handleNotification(dynamic data) {
    // Process notification
  }

  void _handleLockerItemUpdate(dynamic data) {
    // Process locker item update
  }

  void _handleSharedLockerUpdate(dynamic data) {
    // Process shared locker update
  }

  void _handleUserSessionUpdate(dynamic data) {
    // Process user session update
  }

  void _handleSecurityAlert(dynamic data) {
    // Process security alert
  }

  void joinRoom(String roomId) {
    emit('join_room', {'room': roomId});
  }

  void leaveRoom(String roomId) {
    emit('leave_room', {'room': roomId});
  }

  void sendTypingIndicator(String sharedLockerId, bool isTyping) {
    emit('typing', {'shared_locker_id': sharedLockerId, 'is_typing': isTyping});
  }

  void updatePresence(String status) {
    emit('presence', {'status': status});
  }

  /// Re-setup listeners (useful for debugging or manual refresh)
  void refreshListeners() {
    setupCommonListeners();
  }

  /// Test WebSocket functionality by emitting a test event
  void testWebSocket() {
    if (isConnected.value) {
      emit('test_client_event', {
        'timestamp': DateTime.now().toIso8601String(),
        'message': 'Test from Flutter client',
      });
    } else {
      ToastHelper.error("WebSocket not connected");
    }
  }
}
