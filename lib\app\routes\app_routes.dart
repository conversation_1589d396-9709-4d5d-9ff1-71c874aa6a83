part of "app_pages.dart";

abstract class Routes {
  static const home = "/home";
  static const favourites = "/favourites";
  static const contacts = "/contacts";
  static const addresses = "/addresses";
  static const folders = "/folders";
  static const folder = "/folder";
  static const paymentCards = "/payment_cards";
  static const bankAccounts = "/bank_accounts";
  static const notes = "/notes";
  static const accounts = "/accounts";
  static const signin = "/signin";

  static const lockedLocker = "/locked_locker";
  static const settings = "/settings";

  static const LOADING = '/loading';
  static const UPDATE_PASSWORD = '/update-password';
  static const PROFILE = '/profile';
  static const SECURITY = '/security';
  static const TWO_FACTOR_METHODS = '/two-factor-methods';
  static const TWO_FACTOR_PIN = '/two-factor-pin';
  static const TWO_FACTOR_APP = '/two-factor-app';
  static const TWO_FACTOR_QUESTION = '/two-factor-question';
  static const SCAN_QRCODE = '/scan-qrcode';
  static const COMPLETE2FA_APP = '/complete2fa-app';
  static const COMPLETE2FA_PIN = '/complete2fa-pin';
  static const COMPLETE2FA_QUESTION = '/complete2fa-question';
  static const ONBOARDING = '/onboarding';
  static const SIGNUP_COMPLETED = '/signup-completed';
  static const RECOVERY_CODES = '/recovery-codes';
  static const COMPLETE2FA_RECOVERY_CODE = '/complete2fa-recovery-code';
  static const SESSIONS = '/sessions';
  static const SESSION_DETAIL = '/session-detail';

  static const SHARED_LOCKERS = '/shared-lockers';
  static const SHARED_LOCKER = '/shared-locker';
  static const SHARED_FOLDER = '/shared-folder';
  static const VERIFY_ENCRYPTION_KEY = '/verify-encryption-key';
  static const CREATE_ENCRYPTION_KEY = '/create-encryption-key';
  static const LOCAL_AUTH = '/local-auth';
  static const WELCOME = '/welcome';
  static const NOTIFICATIONS = '/notifications';
}
