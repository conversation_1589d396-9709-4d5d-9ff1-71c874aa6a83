import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/init_services.dart';
import 'package:ironlocker/app/helpers/theme.dart';
import 'package:ironlocker/app/helpers/ad_manager.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/themes/theme.dart';
import 'package:upgrader/upgrader.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'firebase_options.dart';

main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Initialize AdMob with enhanced error handling
  await AdManager.instance.initialize();

  await initServices();

  // Non-async exceptions
  FlutterError.onError = (errorDetails) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  };

  // Async exceptions
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  static FirebaseAnalyticsObserver observer = FirebaseAnalyticsObserver(
    analytics: analytics,
  );

  @override
  Widget build(BuildContext context) {
    final themeController = Get.find<ThemeController>();

    return Obx(
      () => UpgradeAlert(
        showIgnore: false,
        showLater: false,
        showReleaseNotes: false,
        child: GetMaterialApp(
          title: 'IronLocker',
          debugShowCheckedModeBanner: false,
          themeMode: themeController.themeMode.value,
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          getPages: AppPages.pages,
          initialRoute: Routes.WELCOME,
          navigatorObservers: <NavigatorObserver>[observer],
        ),
      ),
    );
  }
}
