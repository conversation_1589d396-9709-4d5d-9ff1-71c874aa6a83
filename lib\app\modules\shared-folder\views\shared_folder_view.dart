import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

import 'package:get/get.dart';
import 'package:ironlocker/app/components/banner_add.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/components/locker_items.dart';
import 'package:ironlocker/app/components/something_went_wrong.dart';
import 'package:ironlocker/app/constants/colors.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/constants/sizes.dart';
import 'package:ironlocker/app/themes/app_bar.dart';

import '../controllers/shared_folder_controller.dart';

class SharedFolderView extends GetView<SharedFolderController> {
  const SharedFolderView({super.key});
  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        appBar: LockerAppBar(
          title: controller.folderName.value,
          leading: AppbarTheme.buildStyledBackButton(
            onPressed: () => Get.back(),
            tooltip: 'Back',
          ),
          searchController: controller.searchController,
          showAddLockerItemButton: false, // Move to footer
          clearSearchText: controller.clearSearchText,
        ),
        body: Column(
          children: [
            Expanded(
              child:
                  controller.loading.isTrue
                      ? const Center(
                        child: SizedBox(
                          height: 50,
                          width: 50,
                          child: CircularProgressIndicator(
                            color: AppColors.primaryColor,
                          ),
                        ),
                      )
                      : controller.error.isNotEmpty
                      ? SomethingWentWrong(retry: controller.fetchFolderItems)
                      : RefreshIndicator(
                        onRefresh: () async {
                          await controller.fetchFolderItems();
                        },
                        color: AppColors.primaryColor,
                        backgroundColor: Get.isDarkMode ? null : Colors.white,
                        child:
                            controller.folderItems.isEmpty
                                ? Center(
                                  child: Container(
                                    padding: const EdgeInsets.all(
                                      AppSizes.pagePadding,
                                    ),
                                    constraints: const BoxConstraints(
                                      maxWidth: 420,
                                    ),
                                    child: const Text(
                                      "There are no items in this shared folder. Click the plus button above to start adding items.",
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: 16,
                                        height: 1.6,
                                      ),
                                    ),
                                  ),
                                )
                                : LockerItems(
                                  items:
                                      controller.searching.isTrue
                                          ? controller.searchedItems
                                          : controller.folderItems,
                                  onSelected: (dynamic) {},
                                  onUnSelected: (dynamic) {},
                                ),
                      ),
            ),
            // Footer with Add Button
            _buildFooter(context, controller),
            // AdMob Banner Ad at the bottom
            Container(
              decoration: BoxDecoration(
                color: Get.isDarkMode ? Colors.grey[850] : Colors.grey[100],
                border: Border(
                  top: BorderSide(
                    color:
                        Get.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
                    width: 0.5,
                  ),
                ),
              ),
              child: SafeArea(
                top: false,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 6.0),
                  child: BannerAdWidget(useTestAds: kDebugMode),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFooter(BuildContext context, SharedFolderController controller) {
    // Only show footer if user has write permission
    if (!controller.hasWritePermission) {
      return const SizedBox.shrink();
    }

    return Container(
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[900] : Colors.white,
        border: Border(
          top: BorderSide(
            color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
            width: 0.5,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(20, 16, 20, 12),
        child: SafeArea(
          top: false,
          child: SizedBox(
            width: double.infinity,
            height: 52,
            child: ElevatedButton.icon(
              onPressed: () => controller.addLockerItem(context),
              icon: const Icon(Icons.add_rounded, size: 22),
              label: const Text(
                'Add Item',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Jura',
                  letterSpacing: 0.5,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: IronLockerColors.blue02,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                elevation: 3,
                shadowColor: IronLockerColors.blue02.withValues(alpha: 0.4),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
