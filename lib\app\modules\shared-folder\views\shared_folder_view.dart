import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

import 'package:get/get.dart';
import 'package:ironlocker/app/components/banner_add.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/components/locker_items.dart';
import 'package:ironlocker/app/components/something_went_wrong.dart';
import 'package:ironlocker/app/constants/colors.dart';
import 'package:ironlocker/app/constants/sizes.dart';
import 'package:ironlocker/app/themes/app_bar.dart';

import '../controllers/shared_folder_controller.dart';

class SharedFolderView extends GetView<SharedFolderController> {
  const SharedFolderView({super.key});
  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        appBar: LockerAppBar(
          title: controller.folderName.value,
          leading: AppbarTheme.buildStyledBackButton(
            onPressed: () => Get.back(),
            tooltip: 'Back',
          ),
          searchController: controller.searchController,
          showAddLockerItemButton:
              controller.hasWritePermission == false ? false : true,
          addLockerItem: () => controller.addLockerItem(context),
          clearSearchText: controller.clearSearchText,
        ),
        body: Column(
          children: [
            Expanded(
              child:
                  controller.loading.isTrue
                      ? const Center(
                        child: SizedBox(
                          height: 50,
                          width: 50,
                          child: CircularProgressIndicator(
                            color: AppColors.primaryColor,
                          ),
                        ),
                      )
                      : controller.error.isNotEmpty
                      ? SomethingWentWrong(retry: controller.fetchFolderItems)
                      : RefreshIndicator(
                        onRefresh: () async {
                          await controller.fetchFolderItems();
                        },
                        color: AppColors.primaryColor,
                        backgroundColor: Get.isDarkMode ? null : Colors.white,
                        child:
                            controller.folderItems.isEmpty
                                ? Center(
                                  child: Container(
                                    padding: const EdgeInsets.all(
                                      AppSizes.pagePadding,
                                    ),
                                    constraints: const BoxConstraints(
                                      maxWidth: 420,
                                    ),
                                    child: const Text(
                                      "There are no items in this shared folder. Click the plus button above to start adding items.",
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: 16,
                                        height: 1.6,
                                      ),
                                    ),
                                  ),
                                )
                                : LockerItems(
                                  items:
                                      controller.searching.isTrue
                                          ? controller.searchedItems
                                          : controller.folderItems,
                                  onSelected: (dynamic) {},
                                  onUnSelected: (dynamic) {},
                                ),
                      ),
            ),
            // AdMob Banner Ad at the bottom
            Container(
              color: Get.isDarkMode ? Colors.grey[900] : Colors.grey[50],
              child: SafeArea(
                top: false,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: BannerAdWidget(useTestAds: kDebugMode),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
