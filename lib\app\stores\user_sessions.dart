import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/helpers.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/user_sessions.dart';

class UserSessionsStore extends GetxController {
  final userSessions = [].obs;

  static UserSessionsStore get to => Get.find();
  final userSessionsService = Get.find<UserSessionsService>();

  updateUserSessions(List items) {
    userSessions(items);
  }

  Future fetchSessions() async {
    final resp = await userSessionsService.getUserSessions();

    if (resp.hasError) {
      if (resp.body?["error"] == "SESSION_LOCKED") {
        ToastHelper.error("Session is locked");
        Get.offAllNamed(Routes.lockedLocker);
      } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
          resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
        await Helpers.clearData();
        Get.offAllNamed(Routes.signin);
      } else {
        ToastHelper.error("Something went wrong");
      }
      return;
    }
    userSessions(resp.body["data"]);
    ToastHelper.success("Sessions fetched");
  }

  removeSession(String sessionId) {
    userSessions.value =
        userSessions.where((item) => item["_id"] != sessionId).toList();
  }

  void lockSession(String sessionId) {
    userSessions.value =
        userSessions.map((item) {
          if (item["_id"] == sessionId) {
            return {...item, "locked": true};
          }
          return item;
        }).toList();
  }

  getSession(String sessionId) {
    return userSessions.firstWhere((item) => item["_id"] == sessionId);
  }
}
