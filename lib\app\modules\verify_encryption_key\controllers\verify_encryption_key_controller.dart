import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/secure_encryption.dart';
import 'package:ironlocker/app/helpers/storage.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/user.dart';
import 'package:ironlocker/app/services/websocket_service.dart';
import 'package:ironlocker/app/stores/secure_storage.dart';

class VerifyEncryptionKeyController extends GetxController {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final encryptionKeyController = TextEditingController();
  var privateKey = "".obs;
  var publicKey = "".obs;

  final userService = Get.find<UserService>();

  var loading = false.obs;
  var canVerify = false.obs;
  var showEncryptionKey = false.obs;

  final secureStorage = Get.find<SecureStorageService>();

  @override
  void onInit() {
    super.onInit();
    // Add listener to check if field has content
    encryptionKeyController.addListener(_updateCanVerify);
    _updateCanVerify(); // Initial check
  }

  void _updateCanVerify() {
    canVerify.value = encryptionKeyController.text.trim().isNotEmpty;
  }

  void toggleEncryptionKeyVisibility() {
    showEncryptionKey.value = !showEncryptionKey.value;
  }

  handleValidateEncryptionKey() async {
    // Simple check - if field is empty, don't proceed
    if (!canVerify.value) return;

    loading(true);

    if (privateKey.isEmpty) {
      final resp = await userService.getKeys();

      if (resp.hasError) {
        loading(false);

        return ToastHelper.error(
          resp.body != null ? resp.body['error'] : "Something went wrong",
        );
      }

      privateKey(resp.body['privateKey']);
      publicKey(resp.body['publicKey']);
    }

    var isEncryptionKeyValid =
        await SecureEncryptionHelper.isEncryptionKeyValid(
          encryptedText: privateKey.value,
          encryptionKey: encryptionKeyController.text,
        );

    if (!isEncryptionKeyValid) {
      loading(false);
      return ToastHelper.error("Wrong encryption key");
    }

    var descriptedPrivateKey = await SecureEncryptionHelper.decryptText(
      privateKey.value,
    );

    await secureStorage.write("privateKey", descriptedPrivateKey);
    await secureStorage.write("publicKey", publicKey.value);

    final storage = Storage();

    await storage.setItem(key: "encryptionKeyVerified", value: true);

    // Connect WebSocket after encryption key is verified
    try {
      final webSocketService = Get.find<WebSocketService>();
      await webSocketService.connect();
      debugPrint('WebSocket connected after encryption key verification');
    } catch (e) {
      // Don't block navigation if WebSocket connection fails
      debugPrint(
        'WebSocket connection error after encryption verification: $e',
      );
    }

    ToastHelper.success("Encryption key verified");

    Get.offAllNamed(Routes.LOADING);
  }

  @override
  void onClose() {
    encryptionKeyController.removeListener(_updateCanVerify);
    super.onClose();
  }
}
