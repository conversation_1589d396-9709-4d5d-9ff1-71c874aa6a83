import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/button.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/themes/app_bar.dart';
import 'package:otp_text_field/otp_field.dart';
import 'package:otp_text_field/style.dart';
import '../controllers/two_factor_pin_controller.dart';

class TwoFactorPinView extends GetView<TwoFactorPinController> {
  const TwoFactorPinView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Get.isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
      appBar: LockerAppBar(
        title: "Setup PIN Code",
        showSearchIcon: false,
        showAddLockerItemButton: false,
        leading: AppbarTheme.buildStyledBackButton(
          onPressed: () => Get.back(),
          tooltip: 'Back',
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              const SizedBox(height: 20),

              // Title and Description
              Text(
                "Create Your PIN",
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w700,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),

              Text(
                "Set up a 4-digit PIN for quick two-factor authentication",
                style: TextStyle(
                  fontSize: 16,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),

              // Main Content Card
              _buildMainCard(),
              const SizedBox(height: 24),

              // Security Tips
              _buildSecurityTips(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMainCard() {
    return Container(
      decoration: BoxDecoration(
        color: IronLockerColors.getThemeCard(Get.isDarkMode),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: IronLockerColors.getThemeBorder(Get.isDarkMode),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // PIN Input Section
            Text(
              "Enter PIN",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
              ),
            ),
            const SizedBox(height: 16),

            // PIN Input
            Center(
              child: OTPTextField(
                length: 4,
                width: MediaQuery.of(Get.context!).size.width - 80,
                fieldWidth: 60,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                ),
                spaceBetween: 16,
                contentPadding: const EdgeInsets.symmetric(vertical: 16),
                fieldStyle: FieldStyle.box,
                obscureText: true,
                onChanged: (pin) {
                  controller.pinController.text = pin;
                },
              ),
            ),
            const SizedBox(height: 32),

            // Confirm PIN Section
            Text(
              "Confirm PIN",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
              ),
            ),
            const SizedBox(height: 16),

            // Confirm PIN Input
            Center(
              child: OTPTextField(
                length: 4,
                width: MediaQuery.of(Get.context!).size.width - 80,
                fieldWidth: 60,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                ),
                spaceBetween: 16,
                contentPadding: const EdgeInsets.symmetric(vertical: 16),
                fieldStyle: FieldStyle.box,
                obscureText: true,
                onChanged: (pin) {
                  controller.confirmPinController.text = pin;
                },
              ),
            ),
            const SizedBox(height: 24),

            // PIN Match Indicator
            Obx(() => _buildPinMatchIndicator()),
            const SizedBox(height: 32),

            // Enable Button
            Obx(
              () => SizedBox(
                width: double.infinity,
                child: Button(
                  text: "Enable 2FA with PIN",
                  disabled: controller.pinMatch.isFalse,
                  onPress: controller.enable2faPin,
                  bgColor: IronLockerColors.blue02,
                  fgColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPinMatchIndicator() {
    final isMatching = controller.pinMatch.isTrue;
    final hasInput =
        controller.pinController.text.isNotEmpty &&
        controller.confirmPinController.text.isNotEmpty;

    if (!hasInput) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color:
            isMatching
                ? Colors.green.withValues(alpha: 0.1)
                : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color:
              isMatching
                  ? Colors.green.withValues(alpha: 0.3)
                  : Colors.red.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            isMatching ? Icons.check_circle : Icons.error,
            color: isMatching ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 12),
          Text(
            isMatching ? "PINs match" : "PINs don't match",
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              fontFamily: 'Jura',
              color: isMatching ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityTips() {
    return Container(
      decoration: BoxDecoration(
        color: IronLockerColors.getThemeCard(Get.isDarkMode),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: IronLockerColors.getThemeBorder(Get.isDarkMode),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.security, color: IronLockerColors.blue02, size: 20),
                const SizedBox(width: 8),
                Text(
                  "Security Tips",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Jura',
                    color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildSecurityTip(
              icon: Icons.visibility_off,
              text: "Choose a PIN that's not easily guessable",
            ),
            _buildSecurityTip(
              icon: Icons.lock,
              text: "Don't use common patterns like 1234 or 0000",
            ),
            _buildSecurityTip(
              icon: Icons.save,
              text: "Remember to save your recovery codes",
              isLast: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityTip({
    required IconData icon,
    required String text,
    bool isLast = false,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: isLast ? 0 : 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
            size: 16,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
