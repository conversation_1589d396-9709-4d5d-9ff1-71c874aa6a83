import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/user.dart';
import 'package:ironlocker/app/stores/user.dart';

class TwoFactorAppController extends GetxController {
  final tokenController = TextEditingController();
  final userService = Get.find<UserService>();

  enable2fa() async {
    if (tokenController.text.length < 4) return;

    DialogHelper.loading();

    final resp = await userService.enable2fa({
      "twoFaMethod": "authenticator",
      "password": Get.parameters['password'],
      "token": tokenController.text
    });

    if (resp.hasError) {
      Get.back();
      ToastHelper.error(
        resp.body != null ? resp.body['error'] : "Something went wrong",
      );
      return;
    }

    final recoveryCodes = resp.body['recoveryCodes'];

    UserStore.to.twoFactorEnabled(true);
    ToastHelper.success("2fa enabled");

    Get.offNamed(Routes.RECOVERY_CODES,
        parameters: {"recoveryCodes": recoveryCodes.join(" ")});
  }
}
