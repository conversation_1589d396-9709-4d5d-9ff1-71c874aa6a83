import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/widgets/websocket_status_widget.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';

class LockerAppBar extends StatefulWidget implements PreferredSizeWidget {
  const LockerAppBar({
    super.key,
    required this.title,
    this.selectedItems,
    this.addLockerItem,
    this.page,
    this.popupMenuButton,
    this.searchController,
    this.clearSearchText,
    this.exitSelectionMode,
    this.selectAllItems,
    this.deleteItems,
    this.markFavourite,
    this.unmarkFavourite,
    this.leading,
    this.selectionMode = false,
    this.showAddLockerItemButton = true,
    this.showSearchIcon = true,
    this.showNotificationsIcon = false,
    this.actions,
  });
  final String title;
  final String? page;
  final bool selectionMode;
  final bool showAddLockerItemButton;
  final bool showSearchIcon;
  final bool showNotificationsIcon;
  final VoidCallback? addLockerItem;
  final VoidCallback? clearSearchText;
  final TextEditingController? searchController;
  final VoidCallback? exitSelectionMode;
  final VoidCallback? selectAllItems;
  final VoidCallback? deleteItems;
  final VoidCallback? markFavourite;
  final VoidCallback? unmarkFavourite;
  final int? selectedItems;
  final List<Widget>? actions;
  final Widget? leading;
  final PopupMenuButton? popupMenuButton;

  @override
  Size get preferredSize => const Size.fromHeight(90); // Increased to accommodate status banner

  @override
  State<LockerAppBar> createState() => _LockerAppBarState();
}

class _LockerAppBarState extends State<LockerAppBar> {
  bool _searchMode = false;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // WebSocket status banner
          _buildStatusBanner(),
          // Main app bar
          Container(
            decoration: BoxDecoration(
              color: IronLockerColors.blue02, // Use consistent brand color
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: AppBar(
              backgroundColor: Colors.transparent,
              surfaceTintColor: Colors.transparent,
              scrolledUnderElevation: 0,
              elevation: 0,
              titleSpacing: _searchMode ? 16 : 0,
              automaticallyImplyLeading: _searchMode ? false : true,
              leading:
                  widget.selectionMode
                      ? _buildIconButton(
                        icon: Icons.close,
                        onPressed: widget.exitSelectionMode,
                        tooltip: "Exit selection",
                      )
                      : widget.leading != null
                      ? Container(
                        margin: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: widget.leading,
                      )
                      : null,
              title: _searchMode ? _buildSearchBar() : _buildTitle(),
              actions: widget.actions ?? _buildActions(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBanner() {
    return Container(
      width: double.infinity,
      color: Colors.black.withValues(alpha: 0.1),
      child: const WebSocketStatusWidget(
        showText: true,
        iconSize: 12.0,
        padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 2.0),
      ),
    );
  }

  Widget _buildIconButton({
    required IconData icon,
    required VoidCallback? onPressed,
    String? tooltip,
  }) {
    return Container(
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, color: Colors.white, size: 22),
        tooltip: tooltip,
      ),
    );
  }

  Widget _buildTitle() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        widget.selectionMode
            ? "${widget.selectedItems} Selected"
            : widget.title,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: TextField(
        autofocus: true,
        style: const TextStyle(color: Colors.white, fontSize: 16),
        cursorColor: Colors.white,
        controller: widget.searchController,
        decoration: const InputDecoration(
          hintText: "Search...",
          hintStyle: TextStyle(color: Colors.white70),
          border: InputBorder.none,
          prefixIcon: Icon(Icons.search, color: Colors.white70, size: 20),
        ),
      ),
    );
  }

  List<Widget> _buildActions() {
    if (_searchMode) {
      return [
        _buildIconButton(
          icon: Icons.close,
          onPressed: () {
            if (widget.clearSearchText != null) {
              widget.clearSearchText!();
            }
            setState(() {
              _searchMode = false;
            });
          },
          tooltip: "Close search",
        ),
      ];
    }

    if (widget.selectionMode) {
      return [
        _buildIconButton(
          icon: Icons.delete,
          onPressed: widget.deleteItems,
          tooltip: "Delete selected",
        ),
        _buildPopupMenu(),
      ];
    }

    return [
      if (widget.showAddLockerItemButton)
        _buildIconButton(
          icon: Icons.add,
          onPressed: widget.addLockerItem,
          tooltip: "Add item",
        ),
      if (widget.showSearchIcon)
        _buildIconButton(
          icon: Icons.search,
          onPressed: () {
            setState(() {
              _searchMode = true;
            });
          },
          tooltip: "Search",
        ),
      if (widget.showNotificationsIcon)
        _buildIconButton(
          icon: Icons.notifications,
          onPressed: () {
            Get.toNamed(Routes.NOTIFICATIONS);
          },
          tooltip: "Notifications",
        ),
      if (widget.popupMenuButton != null)
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: widget.popupMenuButton,
        ),
    ];
  }

  Widget _buildPopupMenu() {
    return Container(
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: PopupMenuButton(
        iconColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        itemBuilder:
            (context) => [
              _buildPopupMenuItem(
                text: "Select all",
                onTap: widget.selectAllItems,
                icon: Icons.select_all,
              ),
              if (widget.page != "favourites")
                _buildPopupMenuItem(
                  text: "Mark favourite",
                  onTap: widget.markFavourite,
                  icon: Icons.favorite_outline,
                ),
              _buildPopupMenuItem(
                text: "Unmark favourite",
                onTap: widget.unmarkFavourite,
                icon: Icons.favorite_border,
              ),
            ],
      ),
    );
  }

  PopupMenuItem _buildPopupMenuItem({
    required String text,
    required VoidCallback? onTap,
    required IconData icon,
  }) {
    return PopupMenuItem(
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      onTap: onTap,
      child: Row(
        children: [
          Icon(
            icon,
            size: 18,
            color: Get.isDarkMode ? Colors.white : Colors.black87,
          ),
          const SizedBox(width: 12),
          Text(
            text,
            style: TextStyle(
              fontSize: 14,
              color: Get.isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }
}
