import 'dart:convert';
import 'dart:math';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/rsa_key_helper.dart';
import 'package:ironlocker/app/stores/secure_storage.dart';
import 'package:pointycastle/export.dart';
import 'package:flutter/foundation.dart'; // For compute isolate

class SecureEncryptionHelper {
  static const String persistentSaltStorageKey = "persistentSalt";
  static const String persistentKeyStorageKey = "persistentKey";

  static const int saltLength = 16;
  static const int ivLength = 12;
  static const int authTagLength = 16;
  static const int keyLength = 32;

  static final SecureStorageService storageService =
      Get.find<SecureStorageService>();

  static Future<String> encryptText(
    String text, [
    String? encryptionKey,
  ]) async {
    late Uint8List key;
    late Uint8List salt;

    if (encryptionKey != null) {
      final Uint8List keyData = base64.decode(encryptionKey);
      salt = keyData.sublist(0, saltLength);
      key = keyData.sublist(saltLength);
    } else {
      key = await _getPersistentKey();
      salt = await _getPersistentSalt();
    }

    final Uint8List iv = _generateRandomBytes(ivLength);
    final AEADBlockCipher cipher = _initCipher(true, key, iv);

    final Uint8List plaintext = Uint8List.fromList(utf8.encode(text));
    final Uint8List encryptedBytes = cipher.process(plaintext);

    final Uint8List authTag = encryptedBytes.sublist(
      encryptedBytes.length - authTagLength,
    );
    final Uint8List encryptedData = encryptedBytes.sublist(
      0,
      encryptedBytes.length - authTagLength,
    );

    final Uint8List combinedData = Uint8List.fromList(
      salt + iv + encryptedData + authTag,
    );
    return base64.encode(combinedData);
  }

  static Future<String> decryptText(
    String encryptedText, [
    String? encryptionKey,
  ]) async {
    late Uint8List key;
    late Uint8List salt;
    late Uint8List encryptedBytes;
    late Uint8List iv;

    if (encryptionKey != null) {
      final Uint8List keyData = base64.decode(encryptionKey);
      key = keyData.sublist(saltLength);
      encryptedBytes = base64.decode(encryptedText);
    } else {
      encryptedBytes = base64.decode(encryptedText);
      salt = encryptedBytes.sublist(0, saltLength);
      key = await _getPersistentKeyUsingSalt(salt);
    }

    iv = encryptedBytes.sublist(saltLength, saltLength + ivLength);

    final Uint8List encryptedData = encryptedBytes.sublist(
      saltLength + ivLength,
      encryptedBytes.length - authTagLength,
    );
    final Uint8List authTag = encryptedBytes.sublist(
      encryptedBytes.length - authTagLength,
    );

    final AEADBlockCipher cipher = _initCipher(false, key, iv);

    try {
      final Uint8List decryptedBytes = cipher.process(
        Uint8List.fromList(encryptedData + authTag),
      );
      return utf8.decode(decryptedBytes);
    } catch (e) {
      return "Decryption error";
    }
  }

  static Future<bool> isEncryptionKeyValid({
    required String encryptedText,
    required String encryptionKey,
  }) async {
    final Uint8List combinedData = base64.decode(encryptedText);

    final Uint8List salt = combinedData.sublist(0, saltLength);
    final Uint8List iv = combinedData.sublist(
      saltLength,
      saltLength + ivLength,
    );
    final Uint8List encryptedData = combinedData.sublist(
      saltLength + ivLength,
      combinedData.length - authTagLength,
    );
    final Uint8List authTag = combinedData.sublist(
      combinedData.length - authTagLength,
    );

    final Uint8List key = await _deriveKeyWithArgon2(encryptionKey, salt);
    final AEADBlockCipher cipher = _initCipher(false, key, iv);

    try {
      cipher.process(Uint8List.fromList(encryptedData + authTag));

      await Future.wait([
        storageService.write(persistentKeyStorageKey, base64.encode(key)),
        storageService.write(persistentSaltStorageKey, base64.encode(salt)),
      ]);
      return true;
    } catch (e) {
      return false;
    }
  }

  static Future<Uint8List> _getPersistentKey() async {
    final String? storedKeyBase64 = await storageService.read(
      persistentKeyStorageKey,
    );
    if (storedKeyBase64 != null && storedKeyBase64.isNotEmpty) {
      return base64.decode(storedKeyBase64);
    }
    return Uint8List(0);
  }

  static Future<Uint8List> _getPersistentKeyUsingSalt(Uint8List salt) async {
    final String? saltBase64 = await storageService.read(
      persistentSaltStorageKey,
    );
    if (saltBase64 == null) {
      await storageService.write(persistentSaltStorageKey, base64.encode(salt));
    }
    return await _getPersistentKey();
  }

  static Future<Uint8List> _getPersistentSalt() async {
    final String? saltBase64 = await storageService.read(
      persistentSaltStorageKey,
    );
    if (saltBase64 != null && saltBase64.isNotEmpty) {
      return base64.decode(saltBase64);
    }

    final Uint8List newSalt = await generateSalt();

    await storageService.write(
      persistentSaltStorageKey,
      base64.encode(newSalt),
    );
    return newSalt;
  }

  static Future<Uint8List> generateSalt() async {
    return _generateRandomBytes(saltLength);
  }

  static Future<Uint8List> generatePersistentSalt() async {
    final String? saltBase64 = await storageService.read(
      persistentSaltStorageKey,
    );
    if (saltBase64 != null && saltBase64.isNotEmpty) {
      return base64.decode(saltBase64);
    }
    return generateSalt();
  }

  static Future<Map<String, dynamic>> generateSharedLockerKey(
    String key,
  ) async {
    final String? privateKeyBase64 = await storageService.read("privateKey");
    if (privateKeyBase64 == null) {
      throw Exception("Private key not found");
    }
    final RSAPrivateKey privateKey = RSAKeyHelper.privateKeyFromBase64(
      privateKeyBase64,
    );
    final String combinedKey = RSAKeyHelper.decryptWithPrivateKey(
      key,
      privateKey,
    );

    final Uint8List combinedKeyData = base64.decode(combinedKey);
    final Uint8List salt = combinedKeyData.sublist(0, saltLength);
    final String originalKey = base64.encode(
      combinedKeyData.sublist(saltLength),
    );

    final Uint8List argon2Key = await _deriveKeyWithArgon2(originalKey, salt);
    final String encryptionKey = base64.encode(
      Uint8List.fromList(salt + argon2Key),
    );

    return {"encryptionKey": encryptionKey, "originalKey": combinedKey};
  }

  static Future<Uint8List> _deriveKeyWithArgon2Helper(
    Map<String, dynamic> args,
  ) async {
    final String passphrase = args['passphrase'] as String;
    final Uint8List salt = args['salt'] as Uint8List;

    final Argon2BytesGenerator argon2 = Argon2BytesGenerator();
    final params = Argon2Parameters(
      Argon2Parameters.ARGON2_id,
      salt,
      secret: utf8.encode(passphrase),
      desiredKeyLength: keyLength,
      iterations: 4,
      memoryPowerOf2: 16,
      lanes: 4,
    );
    argon2.init(params);
    final Uint8List derivedKey = Uint8List(keyLength);
    argon2.deriveKey(
      Uint8List.fromList(utf8.encode(passphrase)),
      0,
      derivedKey,
      0,
    );
    return derivedKey;
  }

  static Future<Uint8List> _deriveKeyWithArgon2(
    String passphrase,
    Uint8List salt,
  ) async {
    return await compute(_deriveKeyWithArgon2Helper, {
      'passphrase': passphrase,
      'salt': salt,
    });
  }

  static Future<Uint8List> generateKey(String passphrase, Uint8List salt) {
    return _deriveKeyWithArgon2(passphrase, salt);
  }

  static Uint8List _generateRandomBytes(int length) {
    final Random random = Random.secure();
    return Uint8List.fromList(
      List<int>.generate(length, (_) => random.nextInt(256)),
    );
  }

  static AEADBlockCipher _initCipher(
    bool forEncryption,
    Uint8List key,
    Uint8List iv,
  ) {
    final GCMBlockCipher cipher = GCMBlockCipher(AESEngine());
    final AEADParameters params = AEADParameters(
      KeyParameter(key),
      128,
      iv,
      Uint8List(0),
    );
    cipher.init(forEncryption, params);
    return cipher;
  }
}
