import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/components/button.dart';
import 'package:ironlocker/app/components/drop_down_field.dart';
import 'package:ironlocker/app/components/input_field.dart';
import 'package:ironlocker/app/helpers/decryption.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/interstitial_ad_manager.dart';
import 'package:ironlocker/app/helpers/secure_encryption.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/services/locker.dart';
import 'package:ironlocker/app/stores/locker_items.dart';

void bankAccountFormSheet({
  required BuildContext context,
  Map? updateBankAccount,
  String? folderId,
  Function(Map item)? onCreated,
  String? sharedLockerId,
  String? encryptionKey,
  List? folders,
}) {
  final nameController = TextEditingController(
    text: updateBankAccount?['name'] ?? "",
  );
  final folderController = TextEditingController(
    text: folderId ?? updateBankAccount?['folder'] ?? "",
  );
  final sharedLockerController = TextEditingController(
    text: sharedLockerId ?? "",
  );
  final accountHolderNameController = TextEditingController(
    text: updateBankAccount?['accountHolderName'] ?? "",
  );
  final accountNumberController = TextEditingController(
    text: updateBankAccount?['accountNumber'] ?? "",
  );
  final bankNameController = TextEditingController(
    text: updateBankAccount?['bankName'] ?? "",
  );
  final branchNameController = TextEditingController(
    text: updateBankAccount?['branchName'] ?? "",
  );
  final ibanNumberController = TextEditingController(
    text: updateBankAccount?['ibanNumber'] ?? "",
  );
  final swiftCodeController = TextEditingController(
    text: updateBankAccount?['swiftCode'] ?? "",
  );
  final pinController = TextEditingController(
    text: updateBankAccount?['pin'] ?? "",
  );
  final notesController = TextEditingController(
    text: updateBankAccount?['notes'] ?? "",
  );

  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  ScrollController scrollController = ScrollController();

  var loading = false.obs;
  final localFolders = folders ?? LockerItemsStore.to.folders;

  final lockerService = Get.find<LockerService>();

  handleSubmit() async {
    if (formKey.currentState!.validate()) {
      DialogHelper.loading();

      var encryptedFields = await Future.wait([
        SecureEncryptionHelper.encryptText(nameController.text, encryptionKey),

        SecureEncryptionHelper.encryptText(
          accountHolderNameController.text,
          encryptionKey,
        ),

        SecureEncryptionHelper.encryptText(
          accountNumberController.text,
          encryptionKey,
        ),

        SecureEncryptionHelper.encryptText(
          bankNameController.text,
          encryptionKey,
        ),

        SecureEncryptionHelper.encryptText(
          branchNameController.text,
          encryptionKey,
        ),

        SecureEncryptionHelper.encryptText(
          ibanNumberController.text,
          encryptionKey,
        ),

        SecureEncryptionHelper.encryptText(
          swiftCodeController.text,
          encryptionKey,
        ),

        SecureEncryptionHelper.encryptText(pinController.text, encryptionKey),

        SecureEncryptionHelper.encryptText(notesController.text, encryptionKey),
      ]);

      final resp =
          updateBankAccount == null
              ? await lockerService.addItem(
                itemType: "BankAccount",
                bankAccount: {
                  "name": encryptedFields[0],
                  "accountHolderName": encryptedFields[1],
                  "accountNumber": encryptedFields[2],
                  "bankName": encryptedFields[3],
                  "branchName": encryptedFields[4],
                  "ibanNumber": encryptedFields[5],
                  "swiftCode": encryptedFields[6],
                  "pin": encryptedFields[7],
                  "notes": encryptedFields[8],
                  "folder":
                      folderController.text != "no_folder"
                          ? folderController.text
                          : "",
                  "sharedLocker": sharedLockerController.text,
                },
              )
              : await lockerService.updateItem(
                itemType: "BankAccount",
                itemId: updateBankAccount["_id"],
                bankAccount: {
                  "name": encryptedFields[0],
                  "accountHolderName": encryptedFields[1],
                  "accountNumber": encryptedFields[2],
                  "bankName": encryptedFields[3],
                  "branchName": encryptedFields[4],
                  "ibanNumber": encryptedFields[5],
                  "swiftCode": encryptedFields[6],
                  "pin": encryptedFields[7],
                  "notes": encryptedFields[8],
                  "folder":
                      folderController.text != "no_folder"
                          ? folderController.text
                          : "",
                },
              );

      if (resp.hasError) {
        Get.back();
        ToastHelper.error("Something went wrong");
        return;
      }

      if (sharedLockerId != null) {
        Get.back();
        Get.back();

        var data = await DecryptionHelper.decryptDynamicData(
          resp.body,
          encryptionKey!,
        );

        if (updateBankAccount != null) {
          ToastHelper.success("Bank Account Updated");
        } else {
          onCreated!(data);
          ToastHelper.success("Bank Account Added");
          // Show interstitial ad after successful item creation
          InterstitialAdManager.instance.showItemCreationAd(
            useTestAds: kDebugMode,
          );
        }
        return;
      }

      var data = await DecryptionHelper.decryptDynamicData(resp.body);

      if (updateBankAccount != null) {
        LockerItemsStore.to.updateLockerItem(data);
        Get.back();
        Get.back();
        ToastHelper.success("Bank Account Updated");
        return;
      } else {
        if (onCreated != null) {
          Get.back();
          Get.back();
          ToastHelper.success("Bank Account Added");
          onCreated(data);
          // Show interstitial ad after successful item creation
          InterstitialAdManager.instance.showItemCreationAd(
            useTestAds: kDebugMode,
          );
          return;
        }
      }

      if (folderController.text.isEmpty) {
        LockerItemsStore.to.addItem(data);
      }

      nameController.clear();
      accountHolderNameController.clear();
      accountNumberController.clear();
      bankNameController.clear();
      branchNameController.clear();
      ibanNumberController.clear();
      swiftCodeController.clear();
      pinController.clear();
      notesController.clear();

      Get.back();
      Get.back();
      ToastHelper.success("Bank Account Added");

      // Show interstitial ad after successful item creation
      InterstitialAdManager.instance.showItemCreationAd(useTestAds: kDebugMode);
    }
  }

  showModalBottomSheet(
    context: context,
    elevation: 0,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder:
        (context) => Container(
          decoration: BoxDecoration(
            color: Get.isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(
                  alpha: Get.isDarkMode ? 0.5 : 0.1,
                ),
                blurRadius: 20,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: SingleChildScrollView(
              controller: scrollController,
              child: SafeArea(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle bar
                    Container(
                      margin: const EdgeInsets.only(top: 12),
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color:
                            Get.isDarkMode
                                ? Colors.grey[600]
                                : Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),

                    // Header
                    _buildBankAccountHeader(updateBankAccount != null),

                    // Form Content
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: Form(
                        key: formKey,
                        child: Column(
                          children: [
                            // Basic Information Section
                            _buildBankFormSection(
                              title: "Basic Information",
                              icon: Icons.info_outline,
                              color: IronLockerColors.blue02,
                              children: [
                                InputField(
                                  controller: nameController,
                                  labelText: "Name",
                                  validator: (val) {
                                    if (val!.isEmpty) {
                                      return "This is a required field";
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: 16),
                                if (folderId == null)
                                  DropDownField(
                                    controller: folderController,
                                    items: [
                                      const {
                                        "name": "No folder",
                                        "_id": "no_folder",
                                      },
                                      ...localFolders,
                                    ],
                                    labelText: "Folder",
                                    selectedItem:
                                        updateBankAccount?['folder'] != null
                                            ? localFolders.firstWhere(
                                              (folder) =>
                                                  folder["_id"] ==
                                                  updateBankAccount?['folder'],
                                            )
                                            : const {
                                              "name": "No folder",
                                              "_id": "no_folder",
                                            },
                                  ),
                              ],
                            ),

                            const SizedBox(height: 24),

                            // Account Details Section
                            _buildBankFormSection(
                              title: "Account Details",
                              icon: Icons.account_balance,
                              color: IronLockerColors.blue02,
                              children: [
                                InputField(
                                  controller: accountHolderNameController,
                                  labelText: "Account Holder Name",
                                ),
                                const SizedBox(height: 16),
                                InputField(
                                  controller: accountNumberController,
                                  labelText: "Account Number",
                                ),
                                const SizedBox(height: 16),
                                InputField(
                                  controller: bankNameController,
                                  labelText: "Bank Name",
                                ),
                                const SizedBox(height: 16),
                                InputField(
                                  controller: branchNameController,
                                  labelText: "Branch Name",
                                ),
                              ],
                            ),

                            const SizedBox(height: 24),

                            // Banking Codes Section
                            _buildBankFormSection(
                              title: "Banking Codes",
                              icon: Icons.code,
                              color: IronLockerColors.blue02,
                              children: [
                                InputField(
                                  controller: ibanNumberController,
                                  labelText: "IBAN Number",
                                ),
                                const SizedBox(height: 16),
                                InputField(
                                  controller: swiftCodeController,
                                  labelText: "SWIFT Code",
                                ),
                                const SizedBox(height: 16),
                                InputField(
                                  controller: pinController,
                                  labelText: "PIN",
                                ),
                              ],
                            ),

                            const SizedBox(height: 24),

                            // Additional Information Section
                            _buildBankFormSection(
                              title: "Additional Information",
                              icon: Icons.note_outlined,
                              color: IronLockerColors.blue02.withValues(
                                alpha: 0.7,
                              ),
                              children: [
                                InputField(
                                  controller: notesController,
                                  labelText: "Notes",
                                  maxLines: 4,
                                ),
                              ],
                            ),

                            const SizedBox(height: 32),

                            // Submit Button
                            Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: IronLockerColors.blue02.withValues(
                                      alpha: 0.3,
                                    ),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Obx(
                                () => Button(
                                  text:
                                      updateBankAccount != null
                                          ? 'Update Bank Account'
                                          : 'Add Bank Account',
                                  loading: loading.value,
                                  onPress: handleSubmit,
                                  bgColor: IronLockerColors.blue02,
                                  height: 50,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
  );
}

Widget _buildBankAccountHeader(bool isUpdate) {
  return Container(
    margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    padding: const EdgeInsets.all(24),
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [
          IronLockerColors.blue02.withValues(alpha: 0.1),
          IronLockerColors.blue02.withValues(alpha: 0.05),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(20),
      border: Border.all(
        color: IronLockerColors.blue02.withValues(alpha: 0.2),
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: IronLockerColors.blue02.withValues(alpha: 0.1),
          blurRadius: 12,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: Row(
      children: [
        // Enhanced icon container
        Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                IronLockerColors.blue02,
                IronLockerColors.blue02.withValues(alpha: 0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(18),
            boxShadow: [
              BoxShadow(
                color: IronLockerColors.blue02.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: const Center(
            child: Icon(
              Icons.account_balance_outlined,
              color: Colors.white,
              size: 32,
            ),
          ),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "${isUpdate ? 'Update' : 'Add'} Bank Account",
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w600,
                  color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  fontFamily: 'Jura',
                  letterSpacing: 0.5,
                ),
              ),
              const SizedBox(height: 6),
              Text(
                "${isUpdate ? 'Update your' : 'Add a new'} bank account to your locker",
                style: TextStyle(
                  fontSize: 14,
                  color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                  fontFamily: 'Jura',
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
        // Decorative element
        Container(
          width: 4,
          height: 40,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                IronLockerColors.blue02,
                IronLockerColors.blue02.withValues(alpha: 0.3),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ],
    ),
  );
}

Widget _buildBankFormSection({
  required String title,
  required IconData icon,
  required Color color,
  required List<Widget> children,
}) {
  return Container(
    decoration: BoxDecoration(
      color: Get.isDarkMode ? Colors.grey[850] : Colors.grey[50],
      borderRadius: BorderRadius.circular(16),
      border: Border.all(
        color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
        width: 1,
      ),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Get.isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
          child: Column(children: children),
        ),
      ],
    ),
  );
}
