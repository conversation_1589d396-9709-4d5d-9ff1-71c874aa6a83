import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/decryption.dart';
import 'package:ironlocker/app/helpers/helpers.dart';
import 'package:ironlocker/app/helpers/storage.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/locker.dart';
import 'package:ironlocker/app/services/user.dart';
import 'package:ironlocker/app/stores/locker_items.dart';
import 'package:ironlocker/app/stores/user.dart';

class LoadingController extends GetxController {
  var error = "".obs;
  final lockerService = Get.find<LockerService>();
  final userService = Get.find<UserService>();
  final storage = Storage();

  @override
  void onInit() async {
    await getData();
    super.onInit();
  }

  getData() async {
    error("");
    final lockerResp = await lockerService.getLockerItems();

    if (lockerResp.hasError) {
      final storage = Storage();

      if (lockerResp.statusCode == 401) {
        await storage.deleteItem("refreshToken");
        await storage.deleteItem("accessToken");

        Get.offNamed(Routes.signin);
        return;
      } else if (lockerResp.body?["error"] == "SESSION_LOCKED") {
        UserStore.to.sessionId(lockerResp.body['sessionId']);
        UserStore.to.email(lockerResp.body['email']);
        Get.offNamed(Routes.lockedLocker);
        return;
      } else if (lockerResp.body?["error"] == "SESSION_DESTROYED" ||
          lockerResp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
        await Helpers.clearData();
        Get.offAllNamed(Routes.signin);
        return;
      }

      error("Something went wrong");
      return;
    }

    final userResp = await userService.getProfile();

    if (userResp.hasError) {
      if (userResp.body?["error"] == "SESSION_LOCKED") {
        Get.offAllNamed(Routes.lockedLocker);
        return;
      } else if (userResp.body?["error"] == "SESSION_DESTROYED") {
        Get.offAllNamed(Routes.signin);
        return;
      }

      error("Something went wrong");
      return;
    }

    if (!userResp.body["hasKeys"]) {
      await storage.setItem(key: "newUser", value: true);
      Get.offAllNamed(Routes.CREATE_ENCRYPTION_KEY);
      return;
    }

    final decryptedData = await DecryptionHelper.decryptDynamicData(
      lockerResp.body,
    );

    LockerItemsStore.to.lockerItems(decryptedData);
    UserStore.to.name(userResp.body['name']);
    UserStore.to.email(userResp.body['email']);
    UserStore.to.id(userResp.body['_id']);
    UserStore.to.twoFactorEnabled(userResp.body['twoFactorEnabled']);
    UserStore.to.sessionId(userResp.body['sessionId']);
    UserStore.to.plan(userResp.body['plan']);

    Get.offAllNamed(Routes.home);
  }
}
