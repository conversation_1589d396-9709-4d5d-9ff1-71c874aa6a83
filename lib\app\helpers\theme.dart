import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class Theme<PERSON>ontroller extends GetxController {
  Rx<ThemeMode> themeMode = Rx<ThemeMode>(ThemeMode.system);

  get to => Get.find<ThemeController>();

  @override
  void onInit() {
    super.onInit();
    loadThemeFromStorage();
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    switch (mode) {
      case ThemeMode.system:
        themeMode.value = ThemeMode.system;
        break;
      case ThemeMode.dark:
        themeMode.value = ThemeMode.dark;
        break;
      case ThemeMode.light:
        themeMode.value = ThemeMode.light;
        break;
    }
    await saveTheme();
  }

  Future<void> saveTheme() async {
    final box = GetStorage();
    await box.write('themeMode', themeMode.value.index);
  }

  Future<void> loadThemeFromStorage() async {
    final box = GetStorage();
    int themeModeIndex = box.read<int>('themeMode') ?? ThemeMode.system.index;
    themeMode.value = ThemeMode.values[themeModeIndex];
  }
}
