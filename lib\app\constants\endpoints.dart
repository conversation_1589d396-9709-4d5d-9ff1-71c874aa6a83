import "package:flutter/foundation.dart";

class ApiEndpoints {
  late String baseUrl;
  late String websocketUrl;
  late String refreshToken,
      lockerItems,
      userSessions,
      sharedLockers,
      sharedLockerMembers,
      user,
      addresses,
      contacts,
      folders,
      accounts,
      paymentCards,
      bankAccounts,
      notifications,
      notes,
      auth;

  ApiEndpoints() {
    if (kDebugMode) {
      baseUrl = "https://api.ironlocker.app/v1";
      websocketUrl = "https://api.ironlocker.app";
    } else {
      baseUrl = "https://api.ironlocker.app/v1";
      websocketUrl = "https://api.ironlocker.app";
    }

    auth = "$baseUrl/auth";
    lockerItems = "$baseUrl/locker-items";
    user = "$baseUrl/user";
    userSessions = "$baseUrl/user-sessions";
    sharedLockers = "$baseUrl/shared-lockers";
    sharedLockerMembers = "$baseUrl/shared-locker-members";
    addresses = "$baseUrl/addresses";
    contacts = "$baseUrl/contacts";
    folders = "$baseUrl/folders";
    accounts = "$baseUrl/accounts";
    notifications = "$baseUrl/notifications";
    paymentCards = "$baseUrl/payment-cards";
    bankAccounts = "$baseUrl/bank-accounts";
    notes = "$baseUrl/notes";
  }
}
