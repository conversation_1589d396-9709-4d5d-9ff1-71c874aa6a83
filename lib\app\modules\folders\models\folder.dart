class Folder {
  final String id;
  final String name;
  final String itemType;
  final String? notes;
  final bool favourite;

  Folder({
    required this.id,
    required this.name,
    required this.favourite,
    required this.itemType,
    this.notes,
  });

  static Folder fromJSON(data) {
    return Folder(
      id: data['_id'],
      name: data['name'],
      itemType: data['itemType'],
      notes: data['notes'],
      favourite: data['favourite'],
    );
  }
}
