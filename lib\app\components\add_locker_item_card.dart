import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/locker_item_icon.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';

/// Simplified card widget specifically for the add locker item bottom sheet
class AddLockerItemCard extends StatelessWidget {
  const AddLockerItemCard({
    super.key,
    required this.title,
    required this.itemType,
    required this.onTap,
  });

  final String title;
  final String itemType;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        splashColor: IronLockerColors.blue02.withValues(alpha: 0.1),
        highlightColor: IronLockerColors.blue02.withValues(alpha: 0.05),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Get.isDarkMode ? Colors.grey[800]! : Colors.white,
                Get.isDarkMode
                    ? Colors.grey[800]!.withValues(alpha: 0.95)
                    : Colors.white.withValues(alpha: 0.95),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: IronLockerColors.blue02.withValues(alpha: 0.2),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: IronLockerColors.blue02.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: Colors.black.withValues(
                  alpha: Get.isDarkMode ? 0.3 : 0.05,
                ),
                blurRadius: 12,
                offset: const Offset(0, 2),
                spreadRadius: -2,
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icon container matching locker item design
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: IronLockerColors.blue02.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: IronLockerColors.blue02.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: LockerItemIcon(
                  itemType: itemType,
                  size: 28,
                  color: IronLockerColors.blue02,
                ),
              ),

              const SizedBox(height: 16),

              // Title with enhanced typography
              Text(
                title,
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                  color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  fontFamily: 'Jura',
                  letterSpacing: 0.2,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 4),

              // Subtle accent line
              Container(
                width: 24,
                height: 2,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      IronLockerColors.blue02.withValues(alpha: 0.6),
                      IronLockerColors.blue02.withValues(alpha: 0.2),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(1),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
