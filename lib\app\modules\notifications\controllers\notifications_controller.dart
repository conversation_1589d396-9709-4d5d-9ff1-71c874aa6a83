import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/helpers.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/notifications.dart';

class NotificationsController extends GetxController {
  var loading = true.obs;
  var error = "".obs;
  var refetching = false.obs;
  var notifications = [].obs;

  var notificationsService = Get.find<NotificationsService>();

  @override
  void onInit() async {
    await fetchNotifications();
    loading(false);

    super.onInit();
  }

  Future<void> fetchNotifications() async {
    if (error.isNotEmpty) {
      loading(true);
    }
    final resp = await notificationsService.getNotifications();
    loading(false);

    if (resp.hasError) {
      if (resp.body?["error"] == "SESSION_LOCKED") {
        ToastHelper.error("Session is locked");
        Get.offAllNamed(Routes.lockedLocker);
      } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
          resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
        await Helpers.clearData();
        Get.offAllNamed(Routes.signin);
      } else {
        error("Something went wrong");
      }
      return;
    }

    notifications(resp.body["data"]);
  }

  markAllAsRead() async {
    Get.back();

    DialogHelper.loading();

    final resp = await notificationsService.markAllAsRead();

    Get.back();

    if (resp.hasError) {
      Get.back();
      ToastHelper.error("Something went wrong");
      return;
    }

    ToastHelper.success("Marked all as read");
  }
}
