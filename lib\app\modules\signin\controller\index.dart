import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/error_helper.dart';
import 'package:ironlocker/app/helpers/helpers.dart';
import 'package:ironlocker/app/mixins/analytics_mixin.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/auth.dart';
import 'package:ironlocker/app/services/subscription_service.dart';
import 'package:ironlocker/app/services/user.dart';
import 'package:ironlocker/app/stores/user.dart';
import '../../../helpers/storage.dart';

class SignInController extends GetxController with AnalyticsMixin {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final userService = Get.find<UserService>();

  var loading = false.obs;
  var showPassword = false.obs;
  var canSignIn = false.obs;

  final authService = AuthService();

  @override
  void onInit() {
    super.onInit();
    try {
      trackScreenView('SignInScreen');
    } catch (e) {
      debugPrint('Analytics error in onInit: $e');
    }

    // Add listeners to check if both fields have content
    emailController.addListener(_updateCanSignIn);
    passwordController.addListener(_updateCanSignIn);
    _updateCanSignIn(); // Initial check
  }

  void _updateCanSignIn() {
    canSignIn.value =
        emailController.text.trim().isNotEmpty &&
        passwordController.text.trim().isNotEmpty;
  }

  void togglePasswordVisibility() {
    showPassword.value = !showPassword.value;
  }

  handleSignIn() async {
    // Simple check - if fields are empty, don't proceed
    if (!canSignIn.value) return;

    loading(true);

    final id = await Helpers.getDeviceID();

    final resp = await authService.signin({
      "email": emailController.text,
      "password": passwordController.text,
      "deviceId": id,
    });

    if (resp.hasError) {
      loading(false);
      if (resp.body != null && resp.body?['complete2fa'] != null) {
        switch (resp.body['method']) {
          case "authenticator":
            Get.toNamed(
              Routes.COMPLETE2FA_APP,
              parameters: {
                "method": resp.body['method'],
                "complete2faToken": resp.body['complete2faToken'],
              },
            );
            break;
          case "pin":
            Get.toNamed(
              Routes.COMPLETE2FA_PIN,
              parameters: {
                "method": resp.body['method'],
                "complete2faToken": resp.body['complete2faToken'],
              },
            );
            break;
          case "question":
            Get.toNamed(
              Routes.COMPLETE2FA_QUESTION,
              parameters: {
                "method": resp.body['method'],
                "complete2faToken": resp.body['complete2faToken'],
                "question": resp.body['question'],
              },
            );
            break;
          default:
        }
        return;
      }

      return ErrorHelper.showError(
        resp.body ?? "Something went wrong",
        title: "Sign In Failed",
      );
    }

    final storage = Storage();

    await storage.setItem(key: "accessToken", value: resp.body['accessToken']);
    await storage.setItem(
      key: "refreshToken",
      value: resp.body['refreshToken'],
    );

    UserStore.to.refreshToken(resp.body['refreshToken']);
    UserStore.to.accessToken(resp.body['accessToken']);

    final userResp = await userService.getProfile();

    if (userResp.hasError) {
      loading(false);
      ErrorHelper.showError(
        userResp.body ?? "Failed to load user profile",
        title: "Profile Error",
      );
      return;
    }

    UserStore.to.name(userResp.body['name']);
    UserStore.to.email(userResp.body['email']);
    UserStore.to.id(userResp.body['_id']);
    UserStore.to.twoFactorEnabled(userResp.body['twoFactorEnabled']);
    UserStore.to.sessionId(userResp.body['sessionId']);
    UserStore.to.plan(userResp.body['plan']);

    // Enhanced analytics tracking (with error handling)
    try {
      await analytics.trackLogin(method: 'email');
      await analytics.setUserProperties(
        userId: userResp.body['_id'] ?? '',
        email: emailController.text,
        name: userResp.body['name'],
        twoFactorEnabled: userResp.body['twoFactorEnabled'] ?? false,
      );
    } catch (e) {
      // Don't block login if analytics fails
      debugPrint('Analytics error during login: $e');
    }

    // Identify user to RevenueCat for subscription management
    try {
      await SubscriptionService.to.identifyUser(userResp.body['_id']);
    } catch (e) {
      // Don't block login if RevenueCat identification fails
      debugPrint('RevenueCat identification error during login: $e');
    }

    ErrorHelper.showSuccess(
      "Welcome back! You have been signed in successfully.",
    );

    if (resp.body["hasKeys"]) {
      Get.offAllNamed(Routes.VERIFY_ENCRYPTION_KEY);
    } else {
      await storage.setItem(key: "newUser", value: true);
      Get.offAllNamed(Routes.CREATE_ENCRYPTION_KEY);
    }
  }

  @override
  void onClose() {
    emailController.removeListener(_updateCanSignIn);
    passwordController.removeListener(_updateCanSignIn);
    super.onClose();
  }
}
