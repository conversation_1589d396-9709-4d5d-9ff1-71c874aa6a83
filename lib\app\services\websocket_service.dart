import 'dart:async';
import 'dart:developer';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:ironlocker/app/constants/endpoints.dart';
import 'package:ironlocker/app/helpers/storage.dart';
import 'package:ironlocker/app/stores/user.dart';
import 'package:ironlocker/app/services/user.dart';

class WebSocketService extends GetxService with WidgetsBindingObserver {
  static WebSocketService get to => Get.find();

  io.Socket? _socket;
  final ApiEndpoints _endpoints = ApiEndpoints();
  final Storage _storage = Storage();
  bool _isRefreshing = false;

  // Public getter for checking refresh state
  bool get isRefreshing => _isRefreshing;
  bool _wasConnectedBeforeBackground = false;

  // Connection status
  final RxBool isConnected = false.obs;
  final RxBool isConnecting = false.obs;
  final RxString connectionStatus = 'disconnected'.obs;

  // Store custom event listeners for re-registration
  final Map<String, Function(dynamic)> _customListeners = {};

  // Retry configuration
  int _reconnectAttempts = 0;
  final int _maxReconnectAttempts = 5;
  Timer? _reconnectTimer;
  Timer? _connectionCheckTimer;
  Timer? _connectingTimeoutTimer;

  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    _startConnectionCheck();
  }

  @override
  void onClose() {
    // Remove app lifecycle observer
    WidgetsBinding.instance.removeObserver(this);
    _stopConnectionCheck();
    disconnect();
    super.onClose();
  }

  /// Start periodic connection check
  void _startConnectionCheck() {
    _stopConnectionCheck(); // Stop any existing timer

    _connectionCheckTimer = Timer.periodic(const Duration(seconds: 30), (
      timer,
    ) {
      _checkAndReconnect();
    });
  }

  /// Stop periodic connection check
  void _stopConnectionCheck() {
    _connectionCheckTimer?.cancel();
    _connectionCheckTimer = null;
  }

  /// Start connecting timeout
  void _startConnectingTimeout() {
    _cancelConnectingTimeout();

    _connectingTimeoutTimer = Timer(const Duration(seconds: 15), () {
      if (isConnecting.value && !isConnected.value) {
        isConnecting.value = false;
        connectionStatus.value = 'disconnected';
        _scheduleReconnect();
      }
    });
  }

  /// Cancel connecting timeout
  void _cancelConnectingTimeout() {
    _connectingTimeoutTimer?.cancel();
    _connectingTimeoutTimer = null;
  }

  /// Check connection and reconnect if needed
  void _checkAndReconnect() {
    final actuallyConnected = _socket?.connected ?? false;

    // Fix state mismatch - we think connected but socket is not
    if (isConnected.value && !actuallyConnected) {
      isConnected.value = false;
      isConnecting.value = false;
      connectionStatus.value = 'disconnected';
    }

    // Fix stuck connecting state
    if (isConnecting.value && !actuallyConnected) {
      isConnecting.value = false;
      connectionStatus.value = 'disconnected';
      _cancelConnectingTimeout();
    }

    // Attempt reconnection if needed
    if (UserStore.to.accessToken.value.isNotEmpty &&
        !actuallyConnected &&
        !isConnecting.value &&
        !_isRefreshing) {
      connectIfAuthenticated();
    } else if (actuallyConnected && !isConnected.value) {
      // Fix state mismatch - socket connected but we think it's not
      isConnected.value = true;
      isConnecting.value = false;
      connectionStatus.value = 'connected';
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    log('App lifecycle state changed: $state');

    switch (state) {
      case AppLifecycleState.resumed:
        _handleAppResumed();
        break;
      case AppLifecycleState.paused:
        _handleAppPaused();
        break;
      case AppLifecycleState.inactive:
        _handleAppInactive();
        break;
      case AppLifecycleState.detached:
        _handleAppDetached();
        break;
      case AppLifecycleState.hidden:
        _handleAppHidden();
        break;
    }
  }

  /// Handle app resumed (foreground)
  void _handleAppResumed() {
    if (UserStore.to.accessToken.value.isNotEmpty) {
      _wasConnectedBeforeBackground = false;

      // Small delay to ensure app is fully resumed
      Future.delayed(const Duration(milliseconds: 1000), () {
        final actuallyConnected = _socket?.connected ?? false;

        // Fix state mismatch first
        if (isConnected.value && !actuallyConnected) {
          isConnected.value = false;
          isConnecting.value = false;
          connectionStatus.value = 'disconnected';
        }

        if (!actuallyConnected && !isConnecting.value) {
          connectIfAuthenticated();
        }
      });
    }
  }

  /// Handle app paused (background)
  void _handleAppPaused() {
    _wasConnectedBeforeBackground = isConnected.value;
  }

  /// Handle app inactive
  void _handleAppInactive() {
    // Check if WebSocket is disconnected and try to reconnect
    if (UserStore.to.accessToken.value.isNotEmpty &&
        !isConnected.value &&
        !isConnecting.value) {
      Future.delayed(const Duration(milliseconds: 2000), () {
        if (!isConnected.value && !isConnecting.value) {
          connectIfAuthenticated();
        }
      });
    }
  }

  /// Handle app detached
  void _handleAppDetached() {
    disconnect();
  }

  /// Handle app hidden
  void _handleAppHidden() {
    // Similar to paused state
    _wasConnectedBeforeBackground = isConnected.value;
  }

  /// Connect to WebSocket with authentication
  Future<void> connect() async {
    if (isConnecting.value || isConnected.value) {
      log('WebSocket already connecting or connected - skipping');
      return;
    }

    try {
      // Clean up any existing socket first
      if (_socket != null) {
        try {
          _socket?.clearListeners();
          _socket?.disconnect();
          _socket?.close();
          _socket?.dispose();
        } catch (e) {
          log('Error during socket cleanup: $e');
        }
        _socket = null;

        // Wait for complete cleanup
        await Future.delayed(const Duration(milliseconds: 1000));
      }

      // Get fresh token right before using it
      final accessToken = UserStore.to.accessToken.value;
      if (accessToken.isEmpty) {
        return;
      }

      isConnecting.value = true;
      connectionStatus.value = 'connecting';
      _startConnectingTimeout();

      // Add unique query parameter to force new connection
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final tokenHash = accessToken.hashCode;
      final wsUrl =
          '${_endpoints.websocketUrl}?t=$timestamp&token_refresh=true&token_hash=$tokenHash';

      _socket = io.io(
        wsUrl,
        io.OptionBuilder()
            .setTransports(['websocket'])
            .enableAutoConnect()
            .disableReconnection()
            .setTimeout(10000)
            .enableForceNew() // Force new connection - don't reuse existing
            .setAuth({'token': accessToken})
            .setExtraHeaders({
              'Connection': 'close',
              'Authorization': 'Bearer $accessToken',
            })
            .build(),
      );

      _setupSocketListeners();
    } catch (e) {
      isConnecting.value = false;
      connectionStatus.value = 'error';
      _cancelConnectingTimeout();
      _scheduleReconnect();
    }
  }

  /// Setup socket event listeners
  void _setupSocketListeners() {
    if (_socket == null) return;

    _socket!.onConnect((_) {
      isConnected.value = true;
      isConnecting.value = false;
      connectionStatus.value = 'connected';
      _reconnectAttempts = 0;
      _cancelReconnectTimer();
      _cancelConnectingTimeout();

      // Re-register all custom listeners after connection
      _reregisterCustomListeners();

      // Refetch user profile when WebSocket reconnects
      _refetchUserProfile();
    });

    _socket!.onDisconnect((_) {
      isConnected.value = false;
      isConnecting.value = false;
      connectionStatus.value = 'disconnected';
      _cancelConnectingTimeout();
      _scheduleReconnect();
    });

    _socket!.onConnectError((error) {
      log('❌ WebSocket connection error: $error');
      log('❌ Error type: ${error.runtimeType}');

      isConnected.value = false;
      isConnecting.value = false;
      connectionStatus.value = 'error';
      _cancelConnectingTimeout();

      // Check for auth errors
      final errorString = error.toString().toLowerCase();
      log('🔍 Checking error for auth issues: $errorString');

      if (errorString.contains('token') ||
          errorString.contains('auth') ||
          errorString.contains('401') ||
          errorString.contains('unauthorized') ||
          errorString.contains('expired')) {
        _handleAuthenticationError();
      } else {
        _scheduleReconnect();
      }
    });

    _socket!.on('error', (error) {
      _handleConnectionError(error, 'error');
    });

    _socket!.on('disconnect', (reason) {
      if (reason != null && reason.toString().toLowerCase().contains('error')) {
        _handleConnectionError(reason, 'disconnect');
      }
    });

    // Add token expiration listener
    _socket!.on('token_expired', (data) {
      _handleAuthenticationError();
    });
  }

  /// Re-register all custom listeners after socket reconnection
  void _reregisterCustomListeners() {
    if (_socket == null) return;

    // Re-register all stored custom listeners
    _customListeners.forEach((event, callback) {
      _socket!.on(event, callback);
    });
  }

  /// Refetch user profile when WebSocket reconnects
  Future<void> _refetchUserProfile() async {
    try {
      final userService = Get.find<UserService>();
      final response = await userService.getProfile();

      if (response.hasError) return;

      final profileData = response.body;
      if (profileData != null) {
        UserStore.to.name(profileData['name'] ?? '');
        UserStore.to.email(profileData['email'] ?? '');
        UserStore.to.id(profileData['_id'] ?? '');
        UserStore.to.sessionId(profileData['sessionId'] ?? '');
        UserStore.to.twoFactorEnabled(profileData['twoFactorEnabled'] ?? false);

        if (profileData['plan'] != null) {
          UserStore.to.plan(Map<String, dynamic>.from(profileData['plan']));
        }
      }
    } catch (e) {
      // Silent fail - profile refresh is not critical
    }
  }

  /// Handle connection errors from various Socket.IO events
  void _handleConnectionError(dynamic error, String eventType) {
    log('Connection error from $eventType: $error');

    isConnected.value = false;
    isConnecting.value = false;
    connectionStatus.value = 'error';

    final errorString = error.toString().toLowerCase();
    final originalErrorString = error.toString();

    // Check for token-related errors
    if (originalErrorString.contains('TOKEN_EXPIRED') ||
        originalErrorString.contains('INVALID_TOKEN') ||
        originalErrorString.contains('NO_TOKEN_FOUND') ||
        errorString.contains('token') ||
        errorString.contains('auth') ||
        errorString.contains('expired') ||
        errorString.contains('unauthorized') ||
        errorString.contains('401')) {
      _handleAuthenticationError();
    } else {
      _scheduleReconnect();
    }
  }

  /// Handle authentication errors by refreshing token
  Future<void> _handleAuthenticationError() async {
    if (_isRefreshing) {
      return;
    }

    _isRefreshing = true;

    try {
      final refreshToken = UserStore.to.refreshToken.value;
      if (refreshToken.isEmpty) {
        connectionStatus.value = 'auth_failed';
        return;
      }

      final response = await GetConnect().post(
        "${_endpoints.auth}/refresh-token",
        {"refreshToken": refreshToken},
      );

      if (response.hasError || response.body == null) {
        connectionStatus.value = 'auth_failed';
        return;
      }

      final newAccessToken = response.body['accessToken'];
      final newRefreshToken = response.body['refreshToken'];

      // Validate that we received valid tokens
      if (newAccessToken == null ||
          newRefreshToken == null ||
          newAccessToken.toString().isEmpty ||
          newRefreshToken.toString().isEmpty) {
        connectionStatus.value = 'auth_failed';
        return;
      }

      // Update stored tokens with proper error handling
      try {
        await _storage.setItem(key: "accessToken", value: newAccessToken);
        await _storage.setItem(key: "refreshToken", value: newRefreshToken);

        // Update UserStore after successful storage
        UserStore.to.accessToken(newAccessToken);
        UserStore.to.refreshToken(newRefreshToken);

        // Ensure tokens are properly set before reconnecting
        await Future.delayed(const Duration(milliseconds: 100));

        // Verify tokens were actually updated
        final currentToken = UserStore.to.accessToken.value;

        if (currentToken != newAccessToken) {
          connectionStatus.value = 'auth_failed';
          return;
        }
      } catch (storageError) {
        log('Storage error during token update: $storageError');
        connectionStatus.value = 'auth_failed';
        return;
      }

      // Clean disconnect and wait longer before reconnecting
      disconnect();

      // Ensure socket is completely disposed
      if (_socket != null) {
        _socket?.dispose();
        _socket = null;
      }

      await Future.delayed(const Duration(milliseconds: 2000));

      _reconnectAttempts = 0;

      // Double-check we have the new token before reconnecting
      final verifyToken = UserStore.to.accessToken.value;

      if (verifyToken != newAccessToken) {
        connectionStatus.value = 'auth_failed';
        return;
      }

      connect();
    } catch (e) {
      log('Token refresh failed: $e');
      connectionStatus.value = 'auth_failed';
    } finally {
      _isRefreshing = false;
    }
  }

  /// Schedule reconnection attempt
  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      connectionStatus.value = 'failed';
      return;
    }

    _cancelReconnectTimer();

    final delay = Duration(
      seconds: (2 * (_reconnectAttempts + 1)).clamp(2, 30),
    );

    _reconnectTimer = Timer(delay, () {
      _reconnectAttempts++;
      connect();
    });
  }

  /// Cancel reconnection timer
  void _cancelReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  /// Disconnect from WebSocket
  void disconnect() {
    _cancelReconnectTimer();
    if (_socket != null) {
      _socket?.clearListeners();
      _socket?.disconnect();
      _socket?.close();
      _socket?.dispose();
      _socket = null;
    }
    isConnected.value = false;
    isConnecting.value = false;
    connectionStatus.value = 'disconnected';
    _reconnectAttempts = 0;
  }

  /// Emit event to server
  void emit(String event, [dynamic data]) {
    if (!isConnected.value) return;
    _socket?.emit(event, data);
  }

  /// Listen to server events
  void on(String event, Function(dynamic) callback) {
    // Store the listener for re-registration after reconnections
    _customListeners[event] = callback;
    _socket?.on(event, callback);
  }

  /// Remove event listener
  void off(String event) {
    // Remove from stored listeners
    _customListeners.remove(event);
    _socket?.off(event);
  }

  /// Force reconnection
  Future<void> reconnect() async {
    disconnect();
    await Future.delayed(const Duration(milliseconds: 500));
    _reconnectAttempts = 0;
    connect();
  }

  /// Check if user is authenticated and connect if needed
  Future<void> connectIfAuthenticated() async {
    if (UserStore.to.accessToken.value.isNotEmpty &&
        !isConnected.value &&
        !isConnecting.value) {
      await connect();
    }
  }
}
