import 'dart:async';
import 'dart:developer';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:ironlocker/app/constants/endpoints.dart';
import 'package:ironlocker/app/helpers/storage.dart';
import 'package:ironlocker/app/stores/user.dart';
import 'package:ironlocker/app/services/user.dart';

class WebSocketService extends GetxService with WidgetsBindingObserver {
  static WebSocketService get to => Get.find();

  io.Socket? _socket;
  final ApiEndpoints _endpoints = ApiEndpoints();
  final Storage _storage = Storage();
  bool _isRefreshing = false;

  // Public getter for checking refresh state
  bool get isRefreshing => _isRefreshing;
  bool _wasConnectedBeforeBackground = false;

  // Connection status
  final RxBool isConnected = false.obs;
  final RxBool isConnecting = false.obs;
  final RxString connectionStatus = 'disconnected'.obs;

  // Store custom event listeners for re-registration
  final Map<String, Function(dynamic)> _customListeners = {};

  // Retry configuration
  int _reconnectAttempts = 0;
  final int _maxReconnectAttempts = 5;
  Timer? _reconnectTimer;
  Timer? _connectionCheckTimer;
  Timer? _connectingTimeoutTimer;

  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    _startConnectionCheck();
  }

  @override
  void onClose() {
    // Remove app lifecycle observer
    WidgetsBinding.instance.removeObserver(this);
    _stopConnectionCheck();
    disconnect();
    super.onClose();
  }

  /// Start periodic connection check
  void _startConnectionCheck() {
    _stopConnectionCheck(); // Stop any existing timer

    _connectionCheckTimer = Timer.periodic(const Duration(seconds: 30), (
      timer,
    ) {
      _checkAndReconnect();
    });
  }

  /// Stop periodic connection check
  void _stopConnectionCheck() {
    _connectionCheckTimer?.cancel();
    _connectionCheckTimer = null;
  }

  /// Start connecting timeout
  void _startConnectingTimeout() {
    _cancelConnectingTimeout();

    _connectingTimeoutTimer = Timer(const Duration(seconds: 15), () {
      if (isConnecting.value && !isConnected.value) {
        isConnecting.value = false;
        connectionStatus.value = 'disconnected';
        _scheduleReconnect();
      }
    });
  }

  /// Cancel connecting timeout
  void _cancelConnectingTimeout() {
    _connectingTimeoutTimer?.cancel();
    _connectingTimeoutTimer = null;
  }

  /// Check connection and reconnect if needed
  void _checkAndReconnect() {
    final actuallyConnected = _socket?.connected ?? false;

    // Fix state mismatch - we think connected but socket is not
    if (isConnected.value && !actuallyConnected) {
      isConnected.value = false;
      isConnecting.value = false;
      connectionStatus.value = 'disconnected';
    }

    // Fix stuck connecting state
    if (isConnecting.value && !actuallyConnected) {
      isConnecting.value = false;
      connectionStatus.value = 'disconnected';
      _cancelConnectingTimeout();
    }

    // Attempt reconnection if needed
    if (UserStore.to.accessToken.value.isNotEmpty &&
        !actuallyConnected &&
        !isConnecting.value &&
        !_isRefreshing) {
      connectIfAuthenticated();
    } else if (actuallyConnected && !isConnected.value) {
      // Fix state mismatch - socket connected but we think it's not
      isConnected.value = true;
      isConnecting.value = false;
      connectionStatus.value = 'connected';
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    log('App lifecycle state changed: $state');

    switch (state) {
      case AppLifecycleState.resumed:
        _handleAppResumed();
        break;
      case AppLifecycleState.paused:
        _handleAppPaused();
        break;
      case AppLifecycleState.inactive:
        _handleAppInactive();
        break;
      case AppLifecycleState.detached:
        _handleAppDetached();
        break;
      case AppLifecycleState.hidden:
        _handleAppHidden();
        break;
    }
  }

  /// Handle app resumed (foreground)
  void _handleAppResumed() {
    if (UserStore.to.accessToken.value.isNotEmpty) {
      _wasConnectedBeforeBackground = false;

      // Small delay to ensure app is fully resumed
      Future.delayed(const Duration(milliseconds: 1000), () {
        final actuallyConnected = _socket?.connected ?? false;

        // Fix state mismatch first
        if (isConnected.value && !actuallyConnected) {
          isConnected.value = false;
          isConnecting.value = false;
          connectionStatus.value = 'disconnected';
        }

        if (!actuallyConnected && !isConnecting.value) {
          connectIfAuthenticated();
        }
      });
    }
  }

  /// Handle app paused (background)
  void _handleAppPaused() {
    _wasConnectedBeforeBackground = isConnected.value;
  }

  /// Handle app inactive
  void _handleAppInactive() {
    // Check if WebSocket is disconnected and try to reconnect
    if (UserStore.to.accessToken.value.isNotEmpty &&
        !isConnected.value &&
        !isConnecting.value) {
      Future.delayed(const Duration(milliseconds: 2000), () {
        if (!isConnected.value && !isConnecting.value) {
          connectIfAuthenticated();
        }
      });
    }
  }

  /// Handle app detached
  void _handleAppDetached() {
    disconnect();
  }

  /// Handle app hidden
  void _handleAppHidden() {
    log('App hidden');
    // Similar to paused state
    _wasConnectedBeforeBackground = isConnected.value;
  }

  /// Connect to WebSocket with authentication
  Future<void> connect() async {
    if (isConnecting.value || isConnected.value) {
      log('WebSocket already connecting or connected - skipping');
      return;
    }

    try {
      // Clean up any existing socket first
      if (_socket != null) {
        log('Cleaning up existing socket before new connection');
        try {
          _socket?.clearListeners();
          _socket?.disconnect();
          _socket?.close();
          _socket?.dispose();
        } catch (e) {
          log('Error during socket cleanup: $e');
        }
        _socket = null;

        // Wait for complete cleanup
        await Future.delayed(const Duration(milliseconds: 1000));
      }

      // Get fresh token right before using it
      final accessToken = UserStore.to.accessToken.value;
      if (accessToken.isEmpty) {
        log('No access token available for WebSocket connection');
        return;
      }

      // Connecting with access token
      log('Connecting with access token');

      isConnecting.value = true;
      connectionStatus.value = 'connecting';
      _startConnectingTimeout();

      // Add unique query parameter to force new connection
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final tokenHash = accessToken.hashCode;
      final wsUrl =
          '${_endpoints.websocketUrl}?t=$timestamp&token_refresh=true&token_hash=$tokenHash';

      _socket = io.io(
        wsUrl,
        io.OptionBuilder()
            .setTransports(['websocket'])
            .enableAutoConnect()
            .disableReconnection()
            .setTimeout(10000)
            .enableForceNew() // Force new connection - don't reuse existing
            .setAuth({'token': accessToken})
            .setExtraHeaders({
              'Connection': 'close',
              'Authorization': 'Bearer $accessToken',
            })
            .build(),
      );

      log('Socket.IO instance created');
      _setupSocketListeners();
    } catch (e) {
      isConnecting.value = false;
      connectionStatus.value = 'error';
      _cancelConnectingTimeout();
      _scheduleReconnect();
    }
  }

  /// Setup socket event listeners
  void _setupSocketListeners() {
    if (_socket == null) return;

    _socket!.onConnect((_) {
      log('WebSocket connected successfully');
      isConnected.value = true;
      isConnecting.value = false;
      connectionStatus.value = 'connected';
      _reconnectAttempts = 0;
      _cancelReconnectTimer();
      _cancelConnectingTimeout();

      // Re-register all custom listeners after connection
      _reregisterCustomListeners();

      // Refetch user profile when WebSocket reconnects
      _refetchUserProfile();
    });

    _socket!.onDisconnect((_) {
      isConnected.value = false;
      isConnecting.value = false;
      connectionStatus.value = 'disconnected';
      _cancelConnectingTimeout();
      _scheduleReconnect();
    });

    _socket!.onConnectError((error) {
      log('❌ WebSocket connection error: $error');
      log('❌ Error type: ${error.runtimeType}');

      isConnected.value = false;
      isConnecting.value = false;
      connectionStatus.value = 'error';
      _cancelConnectingTimeout();

      // Check for auth errors
      final errorString = error.toString().toLowerCase();
      log('🔍 Checking error for auth issues: $errorString');

      if (errorString.contains('token') ||
          errorString.contains('auth') ||
          errorString.contains('401') ||
          errorString.contains('unauthorized') ||
          errorString.contains('expired')) {
        log(
          '🔑 Auth error detected from onConnectError, triggering token refresh',
        );
        log('🔑 Current isRefreshing state: $_isRefreshing');
        log(
          '🔑 Current connection state: connected=${isConnected.value}, connecting=${isConnecting.value}',
        );
        _handleAuthenticationError();
      } else {
        log('🔄 Non-auth error, scheduling normal reconnect');
        _scheduleReconnect();
      }
    });

    _socket!.on('error', (error) {
      _handleConnectionError(error, 'error');
    });

    _socket!.on('disconnect', (reason) {
      log('Socket.IO disconnect event with reason: $reason');
      if (reason != null && reason.toString().toLowerCase().contains('error')) {
        _handleConnectionError(reason, 'disconnect');
      }
    });

    // Add token expiration listener
    _socket!.on('token_expired', (data) {
      log('Server sent token_expired event: $data');
      _handleAuthenticationError();
    });
  }

  /// Re-register all custom listeners after socket reconnection
  void _reregisterCustomListeners() {
    if (_socket == null) return;

    log('🎧 Re-registering ${_customListeners.length} custom listeners');

    // Re-register all stored custom listeners
    _customListeners.forEach((event, callback) {
      log('🎧 Re-registering listener for event: $event');
      _socket!.on(event, callback);
    });

    log('🎧 All custom listeners re-registered successfully');
  }

  /// Refetch user profile when WebSocket reconnects
  Future<void> _refetchUserProfile() async {
    try {
      final userService = Get.find<UserService>();
      final response = await userService.getProfile();

      if (response.hasError) return;

      final profileData = response.body;
      if (profileData != null) {
        UserStore.to.name(profileData['name'] ?? '');
        UserStore.to.email(profileData['email'] ?? '');
        UserStore.to.id(profileData['_id'] ?? '');
        UserStore.to.sessionId(profileData['sessionId'] ?? '');
        UserStore.to.twoFactorEnabled(profileData['twoFactorEnabled'] ?? false);

        if (profileData['plan'] != null) {
          UserStore.to.plan(Map<String, dynamic>.from(profileData['plan']));
        }
      }
    } catch (e) {
      // Silent fail - profile refresh is not critical
    }
  }

  /// Handle connection errors from various Socket.IO events
  void _handleConnectionError(dynamic error, String eventType) {
    log('Connection error from $eventType: $error');

    isConnected.value = false;
    isConnecting.value = false;
    connectionStatus.value = 'error';

    final errorString = error.toString().toLowerCase();
    final originalErrorString = error.toString();

    // Check for token-related errors
    if (originalErrorString.contains('TOKEN_EXPIRED') ||
        originalErrorString.contains('INVALID_TOKEN') ||
        originalErrorString.contains('NO_TOKEN_FOUND') ||
        errorString.contains('token') ||
        errorString.contains('auth') ||
        errorString.contains('expired') ||
        errorString.contains('unauthorized') ||
        errorString.contains('401')) {
      log('Auth-related error detected, triggering token refresh');
      _handleAuthenticationError();
    } else {
      log('Non-auth error, scheduling normal reconnect');
      _scheduleReconnect();
    }
  }

  /// Handle authentication errors by refreshing token
  Future<void> _handleAuthenticationError() async {
    final refreshId = DateTime.now().millisecondsSinceEpoch;

    if (_isRefreshing) {
      log('Token refresh already in progress (ID: $refreshId), skipping...');
      return;
    }

    log('Starting token refresh process from WebSocket (ID: $refreshId)...');
    _isRefreshing = true;

    try {
      final refreshToken = UserStore.to.refreshToken.value;
      if (refreshToken.isEmpty) {
        log('No refresh token available');
        connectionStatus.value = 'auth_failed';
        return;
      }

      log('Using refresh token for authentication');

      final response = await GetConnect().post(
        "${_endpoints.auth}/refresh-token",
        {"refreshToken": refreshToken},
      );

      if (response.hasError || response.body == null) {
        log('Token refresh failed: ${response.statusText} - ${response.body}');
        connectionStatus.value = 'auth_failed';
        return;
      }

      // Token refresh successful
      log('Response status: ${response.statusCode}');

      final newAccessToken = response.body['accessToken'];
      final newRefreshToken = response.body['refreshToken'];

      // New tokens extracted from response
      log('New access token received');
      log('New refresh token received');

      // Validate that we received valid tokens
      if (newAccessToken == null ||
          newRefreshToken == null ||
          newAccessToken.toString().isEmpty ||
          newRefreshToken.toString().isEmpty) {
        connectionStatus.value = 'auth_failed';
        return;
      }

      // Update stored tokens with proper error handling
      try {
        log('Storing new tokens to storage');
        await _storage.setItem(key: "accessToken", value: newAccessToken);
        await _storage.setItem(key: "refreshToken", value: newRefreshToken);

        log('Updating UserStore with new tokens');
        // Update UserStore after successful storage
        UserStore.to.accessToken(newAccessToken);
        UserStore.to.refreshToken(newRefreshToken);

        // Ensure tokens are properly set before reconnecting
        await Future.delayed(const Duration(milliseconds: 100));

        // Verify tokens were actually updated
        final currentToken = UserStore.to.accessToken.value;
        log('Verifying token update');

        if (currentToken != newAccessToken) {
          log(
            'Token verification failed! Current token does not match new token',
          );
          connectionStatus.value = 'auth_failed';
          return;
        }

        log('Token update verification successful');
      } catch (storageError) {
        log('Storage error during token update: $storageError');
        connectionStatus.value = 'auth_failed';
        return;
      }

      // Clean disconnect and wait longer before reconnecting
      log('Disconnecting before reconnect with new tokens');
      disconnect();

      // Ensure socket is completely disposed
      if (_socket != null) {
        log('Force disposing existing socket');
        _socket?.dispose();
        _socket = null;
      }

      log('Waiting before reconnecting with fresh tokens');
      await Future.delayed(const Duration(milliseconds: 2000));

      _reconnectAttempts = 0;

      // Double-check we have the new token before reconnecting
      final verifyToken = UserStore.to.accessToken.value;
      log('Final token verification before reconnect');

      if (verifyToken != newAccessToken) {
        log('CRITICAL: Token mismatch before reconnect! Aborting...');
        connectionStatus.value = 'auth_failed';
        return;
      }

      log(
        'Attempting reconnection with verified fresh tokens (ID: $refreshId)',
      );

      connect();
    } catch (e) {
      log('Token refresh failed (ID: $refreshId): $e');
      connectionStatus.value = 'auth_failed';
    } finally {
      log('Token refresh process completed (ID: $refreshId)');
      _isRefreshing = false;
    }
  }

  /// Schedule reconnection attempt
  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      connectionStatus.value = 'failed';
      return;
    }

    _cancelReconnectTimer();

    final delay = Duration(
      seconds: (2 * (_reconnectAttempts + 1)).clamp(2, 30),
    );

    _reconnectTimer = Timer(delay, () {
      _reconnectAttempts++;
      connect();
    });
  }

  /// Cancel reconnection timer
  void _cancelReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  /// Disconnect from WebSocket
  void disconnect() {
    _cancelReconnectTimer();
    if (_socket != null) {
      log('Disconnecting WebSocket...');
      _socket?.clearListeners();
      _socket?.disconnect();
      _socket?.close();
      _socket?.dispose();
      _socket = null;
    }
    isConnected.value = false;
    isConnecting.value = false;
    connectionStatus.value = 'disconnected';
    _reconnectAttempts = 0;
  }

  /// Emit event to server
  void emit(String event, [dynamic data]) {
    if (!isConnected.value) return;
    _socket?.emit(event, data);
  }

  /// Listen to server events
  void on(String event, Function(dynamic) callback) {
    // Store the listener for re-registration after reconnections
    _customListeners[event] = callback;
    _socket?.on(event, callback);
  }

  /// Remove event listener
  void off(String event) {
    // Remove from stored listeners
    _customListeners.remove(event);
    _socket?.off(event);
  }

  /// Force reconnection
  Future<void> reconnect() async {
    disconnect();
    await Future.delayed(const Duration(milliseconds: 500));
    _reconnectAttempts = 0;
    connect();
  }

  /// Check if user is authenticated and connect if needed
  Future<void> connectIfAuthenticated() async {
    if (UserStore.to.accessToken.value.isNotEmpty &&
        !isConnected.value &&
        !isConnecting.value) {
      await connect();
    }
  }
}
