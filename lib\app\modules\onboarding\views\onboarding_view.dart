import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/button.dart';
import 'package:ironlocker/app/constants/images.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/helpers/storage.dart';
import 'package:ironlocker/app/modules/onboarding/controllers/onboarding_controller.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class OnboardingView extends GetView<OnboardingController> {
  const OnboardingView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              IronLockerColors.blue01,
              IronLockerColors.blue02,
              Colors.black,
            ],
            stops: const [0.0, 0.6, 1.0],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
            child: Column(
              children: [
                // Skip button
                Align(
                  alignment: Alignment.topRight,
                  child: Obx(
                    () =>
                        controller.currentPage.value < 3
                            ? TextButton(
                              onPressed: () async {
                                final storage = Storage();
                                await storage.setItem(
                                  key: "hasNavigatedAwayFromOnboading",
                                  value: true,
                                );
                                Get.offAllNamed(Routes.signin);
                              },
                              child: Text(
                                "Skip",
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.8),
                                  fontSize: 16,
                                  fontFamily: 'Jura',
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            )
                            : const SizedBox(height: 48),
                  ),
                ),

                // Main content
                Expanded(
                  child: PageView(
                    controller: controller.pageController,
                    onPageChanged: (page) {
                      controller.currentPage(page);
                    },
                    children: [
                      _buildWelcomePage(),
                      _buildSecurityPage(),
                      _buildAccessPage(),
                      _buildGetStartedPage(),
                    ],
                  ),
                ),

                // Bottom navigation
                _buildBottomNavigation(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomePage() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Logo or main image
        Container(
          width: 280,
          height: 280,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(140),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.2),
              width: 2,
            ),
          ),
          child: Center(
            child: Image.asset(
              Images.secureOnlineStore,
              height: 200,
              width: 200,
              fit: BoxFit.contain,
            ),
          ),
        ),
        const SizedBox(height: 60),

        // Welcome text
        Text(
          "Welcome to",
          style: TextStyle(
            fontSize: 24,
            fontFamily: 'Jura',
            fontWeight: FontWeight.w400,
            color: Colors.white.withValues(alpha: 0.9),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),

        Text(
          "IronLocker",
          style: TextStyle(
            fontSize: 42,
            fontFamily: 'Jura',
            fontWeight: FontWeight.w700,
            color: Colors.white,
            letterSpacing: 1.2,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),

        Text(
          "Your secure digital fortress for\nimportant information",
          style: TextStyle(
            fontSize: 18,
            fontFamily: 'Jura',
            fontWeight: FontWeight.w400,
            color: Colors.white.withValues(alpha: 0.8),
            height: 1.4,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSecurityPage() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Feature icon
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(60),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.2),
              width: 2,
            ),
          ),
          child: Icon(Icons.security, size: 60, color: Colors.white),
        ),
        const SizedBox(height: 40),

        // Image
        Container(
          height: 200,
          width: 200,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Image.asset(Images.keepEverythingSecured, fit: BoxFit.cover),
          ),
        ),
        const SizedBox(height: 40),

        // Title
        Text(
          "KEEP EVERYTHING SECURE",
          style: TextStyle(
            fontSize: 24,
            fontFamily: 'Jura',
            fontWeight: FontWeight.w700,
            color: Colors.white,
            letterSpacing: 1.0,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),

        // Description
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Text(
            "Store bank accounts, payment cards, notes, account credentials, and more with military-grade encryption.",
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'Jura',
              fontWeight: FontWeight.w400,
              color: Colors.white.withValues(alpha: 0.8),
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildAccessPage() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Feature icon
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(60),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.2),
              width: 2,
            ),
          ),
          child: Icon(Icons.cloud_sync, size: 60, color: Colors.white),
        ),
        const SizedBox(height: 40),

        // Image
        Container(
          height: 200,
          width: 200,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Image.asset(Images.accessAnywhere, fit: BoxFit.cover),
          ),
        ),
        const SizedBox(height: 40),

        // Title
        Text(
          "ACCESS ANYWHERE",
          style: TextStyle(
            fontSize: 24,
            fontFamily: 'Jura',
            fontWeight: FontWeight.w700,
            color: Colors.white,
            letterSpacing: 1.0,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),

        // Description
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Text(
            "Access your data anytime, anywhere with IronLocker's secure cloud synchronization across all your devices.",
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'Jura',
              fontWeight: FontWeight.w400,
              color: Colors.white.withValues(alpha: 0.8),
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildGetStartedPage() {
    return SingleChildScrollView(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: MediaQuery.of(Get.context!).size.height - 200,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(height: 20),

            // Success icon
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: IronLockerColors.yellow.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(50),
                border: Border.all(
                  color: IronLockerColors.yellow.withValues(alpha: 0.4),
                  width: 2,
                ),
              ),
              child: Icon(
                Icons.rocket_launch,
                size: 50,
                color: IronLockerColors.yellow,
              ),
            ),
            const SizedBox(height: 30),

            // Image
            Container(
              height: 160,
              width: 160,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Image.asset(Images.getStarted, fit: BoxFit.cover),
              ),
            ),
            const SizedBox(height: 30),

            // Title
            Text(
              "READY TO START",
              style: TextStyle(
                fontSize: 22,
                fontFamily: 'Jura',
                fontWeight: FontWeight.w700,
                color: Colors.white,
                letterSpacing: 1.0,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),

            // Description
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Text(
                "Your digital fortress awaits! Let's get started with IronLocker and secure your important information.",
                style: TextStyle(
                  fontSize: 15,
                  fontFamily: 'Jura',
                  fontWeight: FontWeight.w400,
                  color: Colors.white.withValues(alpha: 0.8),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 30),

            // Get Started Button
            Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 20),
              child: Button(
                text: "Get Started",
                onPress: () async {
                  final storage = Storage();
                  await storage.setItem(
                    key: "hasNavigatedAwayFromOnboading",
                    value: true,
                  );
                  Get.offAllNamed(Routes.signin);
                },
                bgColor: IronLockerColors.yellow,
                fgColor: Colors.black,
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Obx(
      () => SizedBox(
        height: 80,
        child:
            controller.currentPage.value < 3
                ? Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Page indicator
                    SmoothPageIndicator(
                      controller: controller.pageController,
                      count: 4,
                      effect: WormEffect(
                        activeDotColor: Colors.white,
                        dotColor: Colors.white.withValues(alpha: 0.4),
                        dotHeight: 8,
                        dotWidth: 8,
                        spacing: 12,
                      ),
                    ),

                    // Next button
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () {
                            controller.currentPage(
                              controller.currentPage.value + 1,
                            );
                            controller.pageController.animateToPage(
                              controller.currentPage.value,
                              duration: const Duration(milliseconds: 400),
                              curve: Curves.easeInOut,
                            );
                          },
                          borderRadius: BorderRadius.circular(25),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  "Next",
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontFamily: 'Jura',
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Icon(
                                  Icons.arrow_forward,
                                  color: Colors.white,
                                  size: 18,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                )
                : const SizedBox.shrink(),
      ),
    );
  }
}
