import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/button.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import '../controllers/complete2fa_app_controller.dart';

class Complete2faAppView extends GetView<Complete2faAppController> {
  const Complete2faAppView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Get.isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              IronLockerColors.blue01.withValues(alpha: 0.1),
              IronLockerColors.getThemeBackground(Get.isDarkMode),
            ],
            stops: const [0.0, 0.3],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                const SizedBox(height: 40),

                // Header Section
                _buildHeader(),
                const SizedBox(height: 60),

                // Main Content Card
                _buildMainCard(),
                const SizedBox(height: 32),

                // Recovery Code Link
                _buildRecoveryCodeLink(),
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // Security Icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: IronLockerColors.blue02.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(40),
            border: Border.all(
              color: IronLockerColors.blue02.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Icon(Icons.security, size: 40, color: IronLockerColors.blue02),
        ),
        const SizedBox(height: 24),

        // Title
        Text(
          "Two-Factor Authentication",
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.w700,
            fontFamily: 'Jura',
            color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),

        // Subtitle
        Text(
          "Complete your secure login",
          style: TextStyle(
            fontSize: 16,
            fontFamily: 'Jura',
            color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMainCard() {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(maxWidth: 420),
      decoration: BoxDecoration(
        color: IronLockerColors.getThemeCard(Get.isDarkMode),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: IronLockerColors.getThemeBorder(Get.isDarkMode),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            // Authenticator App Icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: IronLockerColors.blue02.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(30),
              ),
              child: Icon(
                Icons.smartphone,
                size: 30,
                color: IronLockerColors.blue02,
              ),
            ),
            const SizedBox(height: 24),

            // Instructions
            Text(
              "Enter the 6-digit code from your authenticator app",
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),

            // OTP Input
            _buildOTPInput(),
            const SizedBox(height: 32),

            // Continue Button
            SizedBox(
              width: double.infinity,
              child: Obx(
                () => Button(
                  text: controller.loading.value ? "Verifying..." : "Continue",
                  loading: controller.loading.value,
                  disabled: controller.loading.value,
                  onPress: controller.complete2fa,
                  bgColor: IronLockerColors.blue02,
                  fgColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecoveryCodeLink() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.help_outline, size: 20, color: Colors.orange),
              const SizedBox(width: 8),
              Text(
                "Can't access your authenticator?",
                style: TextStyle(
                  fontSize: 14,
                  fontFamily: 'Jura',
                  fontWeight: FontWeight.w600,
                  color: Colors.orange[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          TextButton(
            onPressed: () {
              Get.toNamed(
                Routes.COMPLETE2FA_RECOVERY_CODE,
                parameters: {
                  "complete2faToken": Get.parameters['complete2faToken']!,
                },
              );
            },
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            child: Text(
              "Use recovery code instead",
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Jura',
                fontWeight: FontWeight.w600,
                color: Colors.orange[700],
                decoration: TextDecoration.underline,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOTPInput() {
    return StatefulBuilder(
      builder: (context, setState) {
        final List<TextEditingController> controllers = List.generate(
          6,
          (index) => TextEditingController(),
        );
        final List<FocusNode> focusNodes = List.generate(
          6,
          (index) => FocusNode(),
        );

        // Update the main controller when any field changes
        void updateMainController() {
          final code = controllers.map((c) => c.text).join();
          controller.tokenController.text = code;
        }

        return LayoutBuilder(
          builder: (context, constraints) {
            final availableWidth = constraints.maxWidth;
            final fieldWidth = (availableWidth - (5 * 8)) / 6;
            final maxFieldWidth = 45.0;
            final actualFieldWidth =
                fieldWidth > maxFieldWidth ? maxFieldWidth : fieldWidth;

            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(6, (index) {
                return Container(
                  width: actualFieldWidth,
                  margin: EdgeInsets.only(right: index < 5 ? 8 : 0),
                  child: TextFormField(
                    controller: controllers[index],
                    focusNode: focusNodes[index],
                    textAlign: TextAlign.center,
                    keyboardType: TextInputType.number,
                    maxLength: 6, // Allow longer input to detect paste
                    inputFormatters: [
                      _OTPInputFormatter(
                        index: index,
                        controllers: controllers,
                        focusNodes: focusNodes,
                        updateMainController: updateMainController,
                      ),
                    ],
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Jura',
                      color: IronLockerColors.getThemeTextPrimary(
                        Get.isDarkMode,
                      ),
                    ),
                    decoration: InputDecoration(
                      counterText: '',
                      contentPadding: const EdgeInsets.symmetric(vertical: 10),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: IronLockerColors.getThemeBorder(
                            Get.isDarkMode,
                          ),
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: IronLockerColors.getThemeBorder(
                            Get.isDarkMode,
                          ),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: IronLockerColors.blue02,
                          width: 2,
                        ),
                      ),
                      filled: true,
                      fillColor:
                          Get.isDarkMode ? Colors.grey[800] : Colors.grey[50],
                    ),
                    onChanged: (value) {
                      // This will be handled by the input formatter
                    },
                  ),
                );
              }),
            );
          },
        );
      },
    );
  }
}

class _OTPInputFormatter extends TextInputFormatter {
  final int index;
  final List<TextEditingController> controllers;
  final List<FocusNode> focusNodes;
  final VoidCallback updateMainController;

  _OTPInputFormatter({
    required this.index,
    required this.controllers,
    required this.focusNodes,
    required this.updateMainController,
  });

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final newText = newValue.text;

    // Handle paste operation (when length > 1)
    if (newText.length > 1) {
      final pastedCode = newText.replaceAll(RegExp(r'[^0-9]'), '');

      if (pastedCode.length >= 6) {
        // Fill all fields with pasted code
        for (int i = 0; i < 6; i++) {
          controllers[i].text = i < pastedCode.length ? pastedCode[i] : '';
        }
        updateMainController();

        // Unfocus current field and focus last field
        WidgetsBinding.instance.addPostFrameCallback((_) {
          focusNodes[5].unfocus();
        });

        // Return only the digit for current field
        return TextEditingValue(
          text: pastedCode[index],
          selection: TextSelection.collapsed(offset: 1),
        );
      } else if (pastedCode.isNotEmpty) {
        // If pasted text is shorter, just take the first digit
        final digit = pastedCode[0];
        updateMainController();

        // Move to next field
        if (index < 5) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            focusNodes[index + 1].requestFocus();
          });
        }

        return TextEditingValue(
          text: digit,
          selection: TextSelection.collapsed(offset: 1),
        );
      }
    }

    // Handle normal single character input
    if (newText.length == 1 && RegExp(r'[0-9]').hasMatch(newText)) {
      updateMainController();

      // Move to next field
      if (index < 5) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          focusNodes[index + 1].requestFocus();
        });
      } else {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          focusNodes[index].unfocus();
        });
      }

      return TextEditingValue(
        text: newText,
        selection: TextSelection.collapsed(offset: 1),
      );
    }

    // Handle backspace/delete
    if (newText.isEmpty) {
      updateMainController();

      if (index > 0) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          focusNodes[index - 1].requestFocus();
        });
      }

      return const TextEditingValue(
        text: '',
        selection: TextSelection.collapsed(offset: 0),
      );
    }

    // Reject invalid input
    return oldValue;
  }
}
