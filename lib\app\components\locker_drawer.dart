import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/ad_free_widget.dart';
import 'package:ironlocker/app/components/drawer_banner_ad.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/helpers/helpers.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/user.dart';
import 'package:ironlocker/app/services/user_sessions.dart';
import 'package:ironlocker/app/stores/user.dart';
import 'package:ironlocker/app/widgets/websocket_status_widget.dart';
import 'package:ironlocker/app/services/analytics_service.dart';
import '../helpers/dialog.dart';

class LockerDrawer extends StatelessWidget {
  LockerDrawer({super.key, required this.page, this.scaffoldKey});
  final String page;
  final GlobalKey<ScaffoldState>? scaffoldKey;

  final userService = Get.find<UserService>();
  final userSessionService = Get.find<UserSessionsService>();

  handleLogout() async {
    Get.back();
    Get.back();

    // Track logout event
    try {
      await AnalyticsService.to.trackLogout(reason: 'user_initiated');
    } catch (e) {
      // Analytics service might not be available
    }

    DialogHelper.loading();

    final resp = await userService.signout();

    if (resp.hasError) {
      Get.back();
      ToastHelper.error("Something went wrong");
      return;
    }

    await Helpers.clearData();

    Get.offAllNamed(Routes.WELCOME);
  }

  handleLockLocker() async {
    Get.back();
    Get.back();

    DialogHelper.loading();

    final resp = await userSessionService.lockSession(
      UserStore.to.sessionId.value,
    );

    if (resp.hasError) {
      Get.back();
      ToastHelper.error("Something went wrong");
      return;
    }

    Get.offAllNamed(Routes.lockedLocker);
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      width: () {
        final screenWidth = MediaQuery.of(context).size.width;
        final drawerWidth = screenWidth * 0.9;
        return drawerWidth > 380 ? 380.0 : drawerWidth;
      }(),
      child: Container(
        decoration: BoxDecoration(color: const Color(0xffFFFFFF)),
        child: Column(
          children: [
            // WebSocket Status Banner - Top Priority
            _buildStatusBanner(),

            // Header Section
            _buildDrawerHeader(context),

            // Navigation Items
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: SafeArea(
                  child: Column(
                    children: [
                      // Main Navigation Section
                      _buildSectionHeader("Navigation"),
                      _buildNavigationSection([
                        _buildDrawerItem(
                          context,
                          title: "All items",
                          icon: Icons.dashboard_outlined,
                          selected: page == "home",
                          onPress: () {
                            if (scaffoldKey != null) {
                              scaffoldKey!.currentState!.openEndDrawer();
                            }
                            Get.toNamed(Routes.home);
                          },
                        ),
                        _buildDrawerItem(
                          context,
                          title: "Favourites",
                          icon: Icons.star_outline,
                          selected: page == "favourites",
                          onPress: () => Get.toNamed(Routes.favourites),
                        ),
                        _buildDrawerItem(
                          context,
                          title: "Shared lockers",
                          icon: Icons.share_outlined,
                          selected: page == "sharedLockers",
                          onPress: () {
                            if (scaffoldKey != null) {
                              scaffoldKey!.currentState!.openEndDrawer();
                            }
                            Get.toNamed(Routes.SHARED_LOCKERS);
                          },
                        ),
                      ]),

                      // Section Divider
                      _buildSectionDivider(),

                      // Data Types Section
                      _buildSectionHeader("Things you can store"),
                      _buildNavigationSection([
                        _buildDrawerItem(
                          context,
                          title: "Contacts",
                          icon: Icons.contacts_outlined,
                          selected: page == "contacts",
                          onPress: () {
                            if (scaffoldKey != null) {
                              scaffoldKey!.currentState!.openEndDrawer();
                            }
                            Get.toNamed(Routes.contacts);
                          },
                        ),
                        _buildDrawerItem(
                          context,
                          title: "Notes",
                          icon: Icons.sticky_note_2_outlined,
                          selected: page == "notes",
                          onPress: () {
                            if (scaffoldKey != null) {
                              scaffoldKey!.currentState!.openEndDrawer();
                            }
                            Get.toNamed(Routes.notes);
                          },
                        ),
                        _buildDrawerItem(
                          context,
                          title: "Addresses",
                          icon: Icons.place_outlined,
                          selected: page == "addresses",
                          onPress: () {
                            if (scaffoldKey != null) {
                              scaffoldKey!.currentState!.openEndDrawer();
                            }
                            Get.toNamed(Routes.addresses);
                          },
                        ),
                        _buildDrawerItem(
                          context,
                          title: "Accounts",
                          icon: Icons.account_circle_outlined,
                          selected: page == "accounts",
                          onPress: () {
                            if (scaffoldKey != null) {
                              scaffoldKey!.currentState!.openEndDrawer();
                            }
                            Get.toNamed(Routes.accounts);
                          },
                        ),
                        _buildDrawerItem(
                          context,
                          title: "Bank accounts",
                          icon: Icons.account_balance_outlined,
                          selected: page == "bankAccounts",
                          onPress: () {
                            if (scaffoldKey != null) {
                              scaffoldKey!.currentState!.openEndDrawer();
                            }
                            Get.toNamed(Routes.bankAccounts);
                          },
                        ),
                        _buildDrawerItem(
                          context,
                          title: "Payment cards",
                          icon: Icons.credit_card_outlined,
                          selected: page == "paymentCards",
                          onPress: () {
                            if (scaffoldKey != null) {
                              scaffoldKey!.currentState!.openEndDrawer();
                            }
                            Get.toNamed(Routes.paymentCards);
                          },
                        ),
                        _buildDrawerItem(
                          context,
                          title: "Folders",
                          icon: Icons.folder_outlined,
                          selected: page == "folders",
                          onPress: () {
                            if (scaffoldKey != null) {
                              scaffoldKey!.currentState!.openEndDrawer();
                            }
                            Get.toNamed(Routes.folders);
                          },
                        ),
                      ]),

                      // Section Divider
                      _buildSectionDivider(),

                      // Actions Section
                      _buildSectionHeader("Actions"),
                      _buildNavigationSection([
                        _buildDrawerItem(
                          context,
                          title: "Lock your vault",
                          icon: Icons.lock_outlined,
                          onPress:
                              () => DialogHelper.confirmDialog(
                                title:
                                    "Are you sure you want to lock the locker?",
                                onConfirm: handleLockLocker,
                              ),
                        ),
                        _buildDrawerItem(
                          context,
                          title: "Settings",
                          icon: Icons.settings_outlined,
                          selected: page == "settings",
                          onPress: () {
                            if (scaffoldKey != null) {
                              scaffoldKey!.currentState!.openEndDrawer();
                            }
                            Get.toNamed(Routes.settings);
                          },
                        ),
                      ]),

                      // Ad-Free Experience Widget
                      const AdFreeDrawerItem(),

                      // Drawer Banner Ad - placed at bottom
                      DrawerBannerAd(useTestAds: kDebugMode),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerHeader(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenHeight = mediaQuery.size.height;
    final screenWidth = mediaQuery.size.width;
    final scaleFactor = mediaQuery.textScaler.scale(1.0);

    return Container(
      height:
          screenHeight * 0.12, // Reduced height since status banner is separate
      padding: EdgeInsets.symmetric(
        horizontal: screenWidth * 0.05,
        vertical: 16.0, // Fixed vertical padding
      ),
      decoration: BoxDecoration(color: const Color(0xffF5F7FA)),
      child: Center(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              height: 40,
              width: 40,
              decoration: BoxDecoration(
                border: Border.all(color: IronLockerColors.blue02, width: 2),
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.person, size: 18),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Obx(
                    () => Text(
                      UserStore.to.email.value.isNotEmpty
                          ? UserStore.to.name.value
                          : "Secure Storage",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      softWrap: false, // 👈 added
                      style: TextStyle(
                        fontSize: (screenWidth * 0.045) / scaleFactor,
                        color: IronLockerColors.blue02,
                        fontFamily: 'Jura',
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 2),
                  Obx(
                    () => Text(
                      UserStore.to.email.value.isNotEmpty
                          ? UserStore.to.email.value
                          : "Secure Storage",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      softWrap: false,
                      style: TextStyle(
                        fontSize: (screenWidth * 0.035) / scaleFactor,
                        color: IronLockerColors.blue02,
                        fontFamily: 'Jura',
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 10),
            InkWell(
              onTap: () {
                DialogHelper.confirmDialog(
                  title: "Are you sure you want to log out?",
                  onConfirm: handleLogout,
                );
              },
              child: Container(
                height: 33,
                padding: const EdgeInsets.symmetric(horizontal: 10),
                decoration: BoxDecoration(
                  color: const Color(0xfff6e5e8),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.logout,
                      color: Color(0xffFF4545),
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      "Logout",
                      style: TextStyle(
                        color: const Color(0xffFF4545),
                        fontSize: screenWidth * 0.032,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
            fontFamily: 'Jura',
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationSection(List<Widget> children) {
    return Column(children: children);
  }

  Widget _buildSectionDivider() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      height: 1,
      color: IronLockerColors.blue02.withValues(alpha: 0.2),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required String title,
    required IconData icon,
    required VoidCallback onPress,
    bool selected = false,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPress,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Color(0xffF5F7FA),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 18,
                  color:
                      selected ? IronLockerColors.blue02 : Colors.grey.shade600,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: selected ? FontWeight.w600 : FontWeight.w500,
                    color:
                        selected
                            ? IronLockerColors.blue02
                            : Colors.grey.shade600,
                    fontFamily: 'Jura',
                  ),
                ),
              ),
              if (selected)
                Container(
                  width: 4,
                  height: 20,
                  decoration: BoxDecoration(
                    color: IronLockerColors.blue02,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBanner() {
    return SafeArea(
      bottom: false,
      child: Container(
        width: double.infinity,
        color: Colors.black.withValues(alpha: 0.1),
        child: const WebSocketStatusWidget(
          showText: true,
          iconSize: 12.0,
          padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 2.0),
        ),
      ),
    );
  }
}
