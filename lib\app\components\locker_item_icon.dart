import 'package:flutter/material.dart';

class LockerItemIcon extends StatelessWidget {
  const LockerItemIcon({
    super.key,
    required this.itemType,
    this.size,
    this.color,
  });

  final String itemType;
  final double? size;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Icon(_getIconData(), size: size ?? 32, color: color);
  }

  IconData _getIconData() {
    switch (itemType) {
      case "Address":
        return Icons.location_on_outlined;
      case "Contact":
        return Icons.contacts_outlined;
      case "Account":
        return Icons.account_circle_outlined;
      case "PaymentCard":
        return Icons.credit_card_outlined;
      case "Note":
        return Icons.sticky_note_2_outlined;
      case "BankAccount":
        return Icons.account_balance_outlined;
      case "Folder":
        return Icons.folder_outlined;
      default:
        return Icons.help_outline;
    }
  }
}
