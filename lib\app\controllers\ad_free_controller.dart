import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/ad_free_manager.dart';

/// GetX Controller for real-time ad-free status management
/// Provides reactive updates across all widgets when ad-free status changes
class AdFreeController extends GetxController {
  static AdFreeController get to => Get.find<AdFreeController>();

  // Reactive variables
  final _isAdFree = false.obs;
  final _remainingMinutes = 0.obs;
  final _isLoading = false.obs;

  // Getters for reactive access
  bool get isAdFree => _isAdFree.value;
  int get remainingMinutes => _remainingMinutes.value;
  bool get isLoading => _isLoading.value;

  // Reactive getters for widgets to observe
  RxBool get isAdFreeRx => _isAdFree;
  RxInt get remainingMinutesRx => _remainingMinutes;
  RxBool get isLoadingRx => _isLoading;

  Timer? _statusCheckTimer;

  @override
  void onInit() {
    super.onInit();
    _initializeAdFreeStatus();
    _startPeriodicStatusCheck();
  }

  @override
  void onClose() {
    _statusCheckTimer?.cancel();
    super.onClose();
  }

  /// Initialize ad-free status on controller startup
  Future<void> _initializeAdFreeStatus() async {
    await _updateAdFreeStatus();
  }

  /// Start periodic status checking (every 10 seconds for more responsive updates)
  void _startPeriodicStatusCheck() {
    _statusCheckTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _updateAdFreeStatus();
    });
  }

  /// Update ad-free status and notify all observers
  Future<void> _updateAdFreeStatus() async {
    try {
      final isAdFree = await AdFreeManager.instance.isAdFreeActive();
      final remaining = await AdFreeManager.instance.getRemainingAdFreeMinutes();

      // Update reactive variables - this will automatically update all listening widgets
      _isAdFree.value = isAdFree;
      _remainingMinutes.value = remaining;

      debugPrint('🔄 AdFree status updated: $isAdFree, remaining: $remaining minutes');
    } catch (e) {
      debugPrint('❌ Error updating ad-free status: $e');
    }
  }

  /// Show rewarded video and immediately update status on success
  Future<bool> activateAdFreeExperience({int minutes = 30}) async {
    try {
      _isLoading.value = true;

      final success = await AdFreeManager.instance.showRewardedVideoForAdFree(
        minutes: minutes,
        useTestAds: kDebugMode,
      );

      if (success) {
        // Immediately update status without waiting for timer
        await _updateAdFreeStatus();
        debugPrint('✅ Ad-free experience activated immediately');
      }

      return success;
    } catch (e) {
      debugPrint('❌ Error activating ad-free experience: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Force refresh ad-free status (useful for manual refresh)
  Future<void> refreshStatus() async {
    await _updateAdFreeStatus();
  }

  /// Check if ad-free status just changed (useful for triggering actions)
  bool get statusJustChanged {
    // This could be enhanced to track previous state if needed
    return false;
  }

  /// Get formatted remaining time string
  String get formattedRemainingTime {
    if (_remainingMinutes.value < 60) {
      return "${_remainingMinutes.value} minutes";
    } else {
      final hours = _remainingMinutes.value ~/ 60;
      final remainingMins = _remainingMinutes.value % 60;
      if (remainingMins == 0) {
        return "$hours ${hours == 1 ? 'hour' : 'hours'}";
      } else {
        return "$hours ${hours == 1 ? 'hour' : 'hours'} $remainingMins minutes";
      }
    }
  }

  /// Check if ads should be hidden (convenience method for widgets)
  bool get shouldHideAds => _isAdFree.value;

  /// Get ad-free status as stream for advanced use cases
  Stream<bool> get adFreeStatusStream => _isAdFree.stream;

  /// Get remaining minutes as stream for advanced use cases
  Stream<int> get remainingMinutesStream => _remainingMinutes.stream;
}
