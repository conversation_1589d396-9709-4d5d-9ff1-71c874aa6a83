import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:ironlocker/app/components/bottom%20sheets/shared_locker_form.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/components/empty_state.dart';
import 'package:ironlocker/app/components/something_went_wrong.dart';
import 'package:ironlocker/app/modules/shared-lockers/models/shared_locker.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/stores/shared_lockers.dart';
import 'package:ironlocker/app/themes/app_bar.dart';

import '../controllers/shared_lockers_controller.dart';

class SharedLockersView extends GetView<SharedLockersController> {
  const SharedLockersView({super.key});
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        Get.offNamed(Routes.home);
      },
      child: Scaffold(
        backgroundColor:
            Get.isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
        appBar: LockerAppBar(
          title: "Shared Lockers",
          page: "sharedLockers",
          showSearchIcon: false,
          leading: AppbarTheme.buildStyledBackButton(
            onPressed: () {
              Get.offNamed(Routes.home);
            },
            tooltip: 'Back to Home',
          ),
          addLockerItem: () {
            sharedLockerFormSheet(context: context);
          },
        ),
        body: Column(
          children: [
            // Header Info Section
            _buildHeaderInfo(),

            Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  await controller.fetchSharedLockers();
                },
                color: Colors.blue,
                backgroundColor: Get.isDarkMode ? null : Colors.white,
                child: Obx(
                  () =>
                      controller.loading.isTrue
                          ? const Center(
                            child: SizedBox(
                              height: 50,
                              width: 50,
                              child: CircularProgressIndicator(
                                color: Colors.blue,
                              ),
                            ),
                          )
                          : controller.error.isNotEmpty
                          ? SomethingWentWrong(
                            retry: controller.fetchSharedLockers,
                          )
                          : SharedLockersStore.to.sharedLockers.isEmpty
                          ? EmptyStatePresets.noSharedLockers(
                            onCreatePressed: () {
                              sharedLockerFormSheet(context: context);
                            },
                          )
                          : ListView.builder(
                            padding: const EdgeInsets.all(20),
                            itemCount:
                                SharedLockersStore.to.sharedLockers.length,
                            itemBuilder: (BuildContext context, int index) {
                              var sharedLocker = SharedLockerData.fromMap(
                                SharedLockersStore.to.sharedLockers[index],
                              );
                              return _buildSharedLockerCard(
                                context,
                                sharedLocker,
                                index,
                              );
                            },
                          ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderInfo() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.purple.withValues(alpha: 0.1),
            Colors.blue.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.purple.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.people_outline,
              color: Colors.purple,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Shared Lockers",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Get.isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Obx(
                  () => Text(
                    "${SharedLockersStore.to.sharedLockers.length} shared lockers",
                    style: TextStyle(
                      fontSize: 14,
                      color:
                          Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(Icons.add, color: Colors.blue, size: 20),
          ),
        ],
      ),
    );
  }

  Widget _buildSharedLockerCard(
    BuildContext context,
    SharedLockerData sharedLocker,
    int index,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Get.toNamed(
              Routes.SHARED_LOCKER,
              parameters: {"id": sharedLocker.id},
            );
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                // Icon
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.purple.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.purple.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: const Icon(
                    Icons.folder_shared_outlined,
                    color: Colors.purple,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),

                // Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        sharedLocker.name,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Get.isDarkMode ? Colors.white : Colors.black87,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        "${sharedLocker.members.length} members",
                        style: TextStyle(
                          fontSize: 14,
                          color:
                              Get.isDarkMode
                                  ? Colors.grey[400]
                                  : Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.green.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              "Active",
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: Colors.green[700],
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            "Created ${_formatDate(sharedLocker.createdAt)}",
                            style: TextStyle(
                              fontSize: 12,
                              color:
                                  Get.isDarkMode
                                      ? Colors.grey[500]
                                      : Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Arrow
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return "today";
    } else if (difference.inDays == 1) {
      return "yesterday";
    } else if (difference.inDays < 7) {
      return "${difference.inDays} days ago";
    } else if (difference.inDays < 30) {
      return "${(difference.inDays / 7).floor()} weeks ago";
    } else {
      return "${(difference.inDays / 30).floor()} months ago";
    }
  }
}
