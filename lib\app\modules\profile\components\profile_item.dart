import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';

class ProfileItem extends StatelessWidget {
  const ProfileItem({
    super.key,
    required this.value,
    required this.onEdit,
    this.icon,
    this.label,
  });

  final String value;
  final Function() onEdit;
  final IconData? icon;
  final String? label;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onEdit,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: IronLockerColors.getThemeCard(Get.isDarkMode),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: IronLockerColors.getThemeBorder(Get.isDarkMode),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: IronLockerColors.blue01.withValues(
                  alpha: Get.isDarkMode ? 0.15 : 0.05,
                ),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              if (icon != null) ...[
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: IronLockerColors.blue02.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(icon!, size: 20, color: IronLockerColors.blue02),
                ),
                const SizedBox(width: 16),
              ],
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (label != null) ...[
                      Text(
                        label!,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          fontFamily: 'Jura',
                          color: IronLockerColors.getThemeTextSecondary(
                            Get.isDarkMode,
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                    ],
                    Text(
                      value,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Jura',
                        color: IronLockerColors.getThemeTextPrimary(
                          Get.isDarkMode,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.edit_outlined,
                size: 20,
                color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
