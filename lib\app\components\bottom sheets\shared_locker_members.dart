import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/bottom%20sheets/shared_locker_member.dart';
import 'package:ironlocker/app/components/bottom%20sheets/shared_locker_member_action.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/modules/shared-locker/controllers/shared_locker_controller.dart';
import 'package:ironlocker/app/modules/shared-lockers/models/shared_locker.dart';
import 'package:ironlocker/app/stores/user.dart';

void sharedLockerMembersSheet({
  required BuildContext context,
  required Rx<SharedLockerData> sharedLocker,
  required String encryptionKey,
}) {
  var controller = Get.find<SharedLockerController>();

  showModalBottomSheet(
    context: context,
    elevation: 0,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder:
        (context) => Container(
          height: MediaQuery.of(context).size.height * 0.85,
          decoration: BoxDecoration(
            color: IronLockerColors.getThemeBackground(Get.isDarkMode),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Column(
            children: [
              // Header Section
              _buildMembersHeader(context, sharedLocker, encryptionKey),

              // Members List
              Expanded(child: _buildMembersList(context, controller)),
            ],
          ),
        ),
  );
}

Widget _buildMembersHeader(
  BuildContext context,
  Rx<SharedLockerData> sharedLocker,
  String encryptionKey,
) {
  return Container(
    padding: const EdgeInsets.fromLTRB(24, 20, 24, 16),
    decoration: BoxDecoration(
      gradient: IronLockerColors.getPrimaryGradient(opacity: 0.1),
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(24),
        topRight: Radius.circular(24),
      ),
      border: Border(
        bottom: BorderSide(
          color: IronLockerColors.blue02.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
    ),
    child: Column(
      children: [
        // Handle bar
        Container(
          width: 40,
          height: 4,
          decoration: BoxDecoration(
            color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(height: 20),

        // Header content
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: IronLockerColors.blue02.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: IronLockerColors.blue02.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Icon(
                Icons.group_outlined,
                color: IronLockerColors.blue02,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Shared Locker Members',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: IronLockerColors.getThemeTextPrimary(
                        Get.isDarkMode,
                      ),
                      fontFamily: 'Jura',
                    ),
                  ),
                  const SizedBox(height: 4),
                  Obx(
                    () => Text(
                      '${Get.find<SharedLockerController>().sharedLockerMembers.length} member${Get.find<SharedLockerController>().sharedLockerMembers.length == 1 ? '' : 's'}',
                      style: TextStyle(
                        fontSize: 14,
                        color: IronLockerColors.getThemeTextSecondary(
                          Get.isDarkMode,
                        ),
                        fontFamily: 'Jura',
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Add Member Button
            Container(
              decoration: BoxDecoration(
                color: IronLockerColors.blue02,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: IronLockerColors.blue02.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    sharedLockerMemberSheet(
                      context: context,
                      sharedLockerId: sharedLocker.value.id,
                      encryptionKey: encryptionKey,
                    );
                  },
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    child: const Icon(
                      Icons.person_add_outlined,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    ),
  );
}

Widget _buildMembersList(
  BuildContext context,
  SharedLockerController controller,
) {
  return Obx(() {
    if (controller.sharedLockerMembers.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(24),
      itemCount: controller.sharedLockerMembers.length,
      itemBuilder: (context, index) {
        final member = controller.sharedLockerMembers[index];
        final isCreator = UserStore.to.id.value == member.id;

        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: _buildMemberCard(context, member, isCreator),
        );
      },
    );
  });
}

Widget _buildEmptyState(BuildContext context) {
  return Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: IronLockerColors.blue02.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: IronLockerColors.blue02.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Icon(
            Icons.group_outlined,
            size: 48,
            color: IronLockerColors.blue02.withValues(alpha: 0.6),
          ),
        ),
        const SizedBox(height: 24),
        Text(
          'No Members Yet',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
            fontFamily: 'Jura',
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Add members to start sharing this locker',
          style: TextStyle(
            fontSize: 14,
            color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
            fontFamily: 'Jura',
          ),
          textAlign: TextAlign.center,
        ),
      ],
    ),
  );
}

Widget _buildMemberCard(BuildContext context, member, bool isCreator) {
  return Container(
    decoration: BoxDecoration(
      color: IronLockerColors.getThemeCard(Get.isDarkMode),
      borderRadius: BorderRadius.circular(16),
      border: Border.all(
        color: IronLockerColors.getThemeBorder(Get.isDarkMode),
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.2 : 0.05),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Material(
      color: Colors.transparent,
      child: InkWell(
        onTap:
            isCreator
                ? null
                : () {
                  sharedLockerMemberActionSheet(
                    context: context,
                    member: member,
                  );
                },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              // Avatar
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color:
                      isCreator
                          ? IronLockerColors.yellow
                          : IronLockerColors.blue02,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: (isCreator
                              ? IronLockerColors.yellow
                              : IronLockerColors.blue02)
                          .withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Icon(
                  isCreator
                      ? Icons.admin_panel_settings_outlined
                      : Icons.person_outlined,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),

              // Member Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            member.name,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: IronLockerColors.getThemeTextPrimary(
                                Get.isDarkMode,
                              ),
                              fontFamily: 'Jura',
                            ),
                          ),
                        ),
                        if (isCreator)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: IronLockerColors.yellow.withValues(
                                alpha: 0.15,
                              ),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: IronLockerColors.yellow.withValues(
                                  alpha: 0.3,
                                ),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              'CREATOR',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                                color: IronLockerColors.yellow01,
                                fontFamily: 'Jura',
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      member.email,
                      style: TextStyle(
                        fontSize: 14,
                        color: IronLockerColors.getThemeTextSecondary(
                          Get.isDarkMode,
                        ),
                        fontFamily: 'Jura',
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildPermissionBadge(member.permission),
                  ],
                ),
              ),

              // Action Arrow (only for non-creators)
              if (!isCreator)
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: IronLockerColors.getThemeCard(Get.isDarkMode),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: IronLockerColors.getThemeBorder(Get.isDarkMode),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.more_vert,
                    size: 16,
                    color: IronLockerColors.getThemeTextSecondary(
                      Get.isDarkMode,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    ),
  );
}

Widget _buildPermissionBadge(String permission) {
  Color badgeColor;
  Color textColor;
  IconData icon;

  switch (permission.toUpperCase()) {
    case 'READ':
      badgeColor = IronLockerColors.info.withValues(alpha: 0.15);
      textColor = IronLockerColors.info;
      icon = Icons.visibility_outlined;
      break;
    case 'WRITE':
      badgeColor = IronLockerColors.success.withValues(alpha: 0.15);
      textColor = IronLockerColors.success;
      icon = Icons.edit_outlined;
      break;
    case 'ADMIN':
      badgeColor = IronLockerColors.warning.withValues(alpha: 0.15);
      textColor = IronLockerColors.warning;
      icon = Icons.admin_panel_settings_outlined;
      break;
    default:
      badgeColor = IronLockerColors.blue02.withValues(alpha: 0.15);
      textColor = IronLockerColors.blue02;
      icon = Icons.person_outlined;
  }

  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
    decoration: BoxDecoration(
      color: badgeColor,
      borderRadius: BorderRadius.circular(12),
      border: Border.all(color: textColor.withValues(alpha: 0.3), width: 1),
    ),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 14, color: textColor),
        const SizedBox(width: 6),
        Text(
          permission.toUpperCase(),
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: textColor,
            fontFamily: 'Jura',
          ),
        ),
      ],
    ),
  );
}
