import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/storage.dart';
import 'package:ironlocker/app/routes/app_pages.dart';

class WelcomeController extends GetxController {
  final storage = Storage();

  @override
  void onInit() {
    super.onInit();
    Future.delayed(const Duration(seconds: 3), () {
      final hasNavigatedAwayFromOnboading = storage.getItem(
        "hasNavigatedAwayFromOnboading",
      );

      final refreshToken = storage.getItem("refreshToken");

      if (refreshToken == null) {
        if (hasNavigatedAwayFromOnboading == null) {
          return Get.offNamed(Routes.ONBOARDING);
        } else {
          return Get.offNamed(Routes.signin);
        }
      } else if (refreshToken != null &&
          storage.getItem("newUser") == null &&
          storage.getItem("encryptionKeyVerified") == null) {
        return Get.offNamed(Routes.VERIFY_ENCRYPTION_KEY);
      } else if (refreshToken != null && storage.getItem("newUser") != null) {
        return Get.offNamed(Routes.CREATE_ENCRYPTION_KEY);
      } else if (storage.getItem("biometricEnabled") != null &&
          storage.getItem("biometricEnabled")) {
        return Get.offNamed(Routes.LOCAL_AUTH);
      } else {
        return Get.offNamed(Routes.LOADING);
      }
    });
  }
}
