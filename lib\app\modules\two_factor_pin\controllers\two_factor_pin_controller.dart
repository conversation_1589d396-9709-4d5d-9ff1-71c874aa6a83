import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/user.dart';
import 'package:ironlocker/app/stores/user.dart';

class TwoFactorPinController extends GetxController {
  var pinController = TextEditingController();
  var confirmPinController = TextEditingController();

  var pinMatch = false.obs;

  final userService = Get.find<UserService>();

  @override
  onInit() {
    super.onInit();

    pinController.addListener(() {
      if (pinController.text.length == 4 &&
          pinController.text == confirmPinController.text) {
        pinMatch(true);
      } else {
        pinMatch(false);
      }
    });
    confirmPinController.addListener(() {
      if (confirmPinController.text.length == 4 &&
          pinController.text == confirmPinController.text) {
        pinMatch(true);
      } else {
        pinMatch(false);
      }
    });
  }

  enable2faPin() async {
    if (pinController.text.length < 4) return;

    DialogHelper.loading();

    final resp = await userService.enable2fa({
      "twoFaMethod": "pin",
      "password": Get.parameters['password'],
      "pin": pinController.text
    });

    if (resp.hasError) {
      Get.back();
      ToastHelper.error(
        resp.body != null ? resp.body['error'] : "Something went wrong",
      );
      return;
    }

    final recoveryCodes = resp.body['recoveryCodes'];

    UserStore.to.twoFactorEnabled(true);
    ToastHelper.success("2fa enabled");

    Get.offNamed(Routes.RECOVERY_CODES,
        parameters: {"recoveryCodes": recoveryCodes.join(" ")});
  }

  @override
  void onClose() {
    super.onClose();
    pinController.dispose();
    confirmPinController.dispose();
  }
}
