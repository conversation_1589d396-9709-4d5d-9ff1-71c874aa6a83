import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/button.dart';
import 'package:ironlocker/app/components/banner_add.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/constants/sizes.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/stores/user.dart';
import 'package:ironlocker/app/themes/app_bar.dart';
import '../components/profile_item.dart';
import '../controllers/profile_controller.dart';

class ProfileView extends GetView<ProfileController> {
  const ProfileView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: IronLockerColors.getThemeBackground(Get.isDarkMode),
      appBar: LockerAppBar(
        title: "Profile",
        leading: AppbarTheme.buildStyledBackButton(
          onPressed: () => Get.back(),
          tooltip: 'Back',
        ),
        showSearchIcon: false,
        showAddLockerItemButton: false,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Profile Header Section
                  _buildProfileHeader(context),

                  // Profile Content
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Center(
                      child: ConstrainedBox(
                        constraints: const BoxConstraints(maxWidth: 420),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Personal Information Section
                            _buildSectionHeader("Personal Information"),
                            const SizedBox(height: 16),
                            _buildProfileCard(
                              children: [
                                Obx(
                                  () => _buildProfileField(
                                    icon: Icons.person_outline,
                                    label: "Full Name",
                                    value: UserStore.to.name.value,
                                    onEdit: () {
                                      DialogHelper.updateName(
                                        controller: controller.nameController,
                                        onUpdate: controller.updateName,
                                      );
                                    },
                                  ),
                                ),
                                _buildDivider(),
                                _buildProfileField(
                                  icon: Icons.email_outlined,
                                  label: "Email Address",
                                  value: UserStore.to.email.value,
                                  onEdit: () {
                                    DialogHelper.updateEmail(
                                      emailController:
                                          controller.emailController,
                                      currentPasswordController:
                                          controller.currentPasswordController,
                                      onUpdate: controller.updateEmail,
                                    );
                                  },
                                ),
                              ],
                            ),

                            const SizedBox(height: 32),

                            // Danger Zone Section
                            _buildSectionHeader("Danger Zone"),
                            const SizedBox(height: 16),
                            _buildDangerCard(),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // AdMob Banner Ad at the bottom
          Container(
            color: Get.isDarkMode ? Colors.grey[900] : Colors.grey[50],
            child: SafeArea(
              top: false,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: BannerAdWidget(
                  useTestAds: kDebugMode, // Use test ads in debug mode
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileHeader(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 40),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            IronLockerColors.blue01.withValues(alpha: 0.1),
            IronLockerColors.blue02.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(30),
          bottomRight: Radius.circular(30),
        ),
      ),
      child: Column(
        children: [
          // Profile Avatar
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: IronLockerColors.blue02,
              borderRadius: BorderRadius.circular(50),
              boxShadow: [
                BoxShadow(
                  color: IronLockerColors.blue02.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: const Icon(Icons.person, size: 50, color: Colors.white),
          ),
          const SizedBox(height: 16),
          // User Name
          Obx(
            () => Text(
              UserStore.to.name.value.isNotEmpty
                  ? UserStore.to.name.value
                  : "User",
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w700,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
              ),
            ),
          ),
          const SizedBox(height: 4),
          // User Email
          Text(
            UserStore.to.email.value,
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'Jura',
              color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          fontFamily: 'Jura',
          color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
        ),
      ),
    );
  }

  Widget _buildProfileCard({required List<Widget> children}) {
    return Container(
      decoration: BoxDecoration(
        color: IronLockerColors.getThemeCard(Get.isDarkMode),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: IronLockerColors.blue01.withValues(
              alpha: Get.isDarkMode ? 0.2 : 0.1,
            ),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(children: children),
    );
  }

  Widget _buildProfileField({
    required IconData icon,
    required String label,
    required String value,
    required VoidCallback onEdit,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onEdit,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(icon, size: 20, color: Colors.blue),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color:
                            Get.isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      value,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Get.isDarkMode ? Colors.white : Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.edit_outlined,
                size: 20,
                color: Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      thickness: 1,
      color: Get.isDarkMode ? Colors.grey[700] : Colors.grey[200],
    );
  }

  Widget _buildDangerCard() {
    return Container(
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3), width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: IronLockerColors.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.warning_outlined,
                    size: 20,
                    color: IronLockerColors.error,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  "Request Account Deletion",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Jura',
                    color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              "Request permanent deletion of your account and all associated data. This action cannot be undone.",
              style: TextStyle(
                fontSize: 14,
                color: Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                height: 1.4,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  controller.requestAccountDeletion();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: IronLockerColors.error,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  "Request Account Deletion",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Jura',
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
