import 'package:get/get.dart';
import 'package:ironlocker/app/stores/user_sessions.dart';

class SessionDetailController extends GetxController {
  var userSessionsStore = Get.find<UserSessionsStore>();
  var loading = true.obs;
  var sessionData = RxMap<dynamic, dynamic>();

  @override
  void onInit() async {
    sessionData.value = userSessionsStore.getSession(Get.arguments["id"]!);
    loading(false);
    super.onInit();
  }
}
