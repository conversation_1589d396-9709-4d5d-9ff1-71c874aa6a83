import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:ironlocker/app/controllers/ad_free_controller.dart';
import 'package:ironlocker/app/helpers/ad_helper.dart';
import 'package:ironlocker/app/helpers/ad_manager.dart';
import 'package:ironlocker/app/stores/user.dart';

/// Compact banner ad widget specifically designed for drawer menu placement
class DrawerBannerAd extends StatefulWidget {
  final bool useTestAds;

  const DrawerBannerAd({super.key, this.useTestAds = false});

  String get adUnitId =>
      useTestAds ? AdHelper.testBannerAdUnitId : AdHelper.bannerAdUnitId;

  @override
  State<DrawerBannerAd> createState() => _DrawerBannerAdState();
}

class _DrawerBannerAdState extends State<DrawerBannerAd> {
  BannerAd? _bannerAd;
  bool _isLoading = true;
  bool _hasError = false;
  int _retryCount = 0;
  static const int _maxRetries = 3; // Fewer retries for drawer ads
  bool _isDisposed = false;

  @override
  Widget build(BuildContext context) {
    // Use GetX Obx for real-time ad-free status updates
    return Obx(() {
      // Hide completely when user has premium or lifetime plan
      if (UserStore.to.plan["name"] == "premium" ||
          UserStore.to.plan["name"] == "lifetime") {
        return const SizedBox.shrink();
      }

      // Hide completely when ad-free is active - no loading, no error states, nothing
      if (AdFreeController.to.isAdFree) {
        return const SizedBox.shrink();
      }

      return _buildAdContent();
    });
  }

  Widget _buildAdContent() {
    if (_hasError) {
      // Minimal error placeholder for drawer
      return Container(
        width: double.infinity,
        height: 60,
        margin: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!, width: 0.5),
        ),
        child: InkWell(
          onTap: _retryLoadAd,
          borderRadius: BorderRadius.circular(8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.refresh, size: 16, color: Colors.grey[600]),
              const SizedBox(height: 2),
              Text(
                'Tap to retry',
                style: TextStyle(fontSize: 10, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      );
    }

    if (_isLoading) {
      // Compact loading placeholder
      return Container(
        width: double.infinity,
        height: 60,
        margin: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[400]!),
            ),
          ),
        ),
      );
    }

    if (_bannerAd == null) {
      return const SizedBox.shrink();
    }

    // Compact ad display for drawer
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: SizedBox(
          width: _bannerAd!.size.width.toDouble(),
          height: 60, // Fixed compact height for drawer
          child: AdWidget(ad: _bannerAd!),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    // Ensure ads are muted by setting audio session
    _configureSilentAds();
    _checkAndLoadAd();
    // No need for periodic checking - GetX handles real-time updates
  }

  void _checkAndLoadAd() async {
    // Check if user has premium or lifetime plan
    if (UserStore.to.plan["name"] == "premium" ||
        UserStore.to.plan["name"] == "lifetime") {
      debugPrint(
        '🚫 Drawer banner ad completely hidden - user has premium/lifetime plan',
      );
      // No need to set state - GetX Obx handles the UI updates
      return;
    }

    // Check if user has ad-free experience active using GetX controller
    if (AdFreeController.to.isAdFree) {
      debugPrint(
        '🚫 Drawer banner ad completely hidden - user has ad-free experience',
      );
      // No need to set state - GetX Obx handles the UI updates
      return;
    }

    _loadAd();
  }

  /// Configure ads to be silent
  void _configureSilentAds() {
    // Banner ads are typically silent by default, but we can ensure this
    // by configuring the audio session if needed
    debugPrint('Configuring drawer banner ads to be silent');
  }

  void _loadAd() {
    if (_isDisposed) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      debugPrint(
        'Loading drawer banner ad (attempt ${_retryCount + 1}/$_maxRetries)',
      );

      // Create silent banner ad for drawer placement
      final bannerAd = BannerAd(
        size: AdSize.mediumRectangle, // Better size for drawer
        adUnitId: widget.adUnitId,
        request:
            AdManager.instance
                .getOptimizedAdRequest(), // Banner ads are silent by default
        listener: BannerAdListener(
          onAdLoaded: (ad) {
            if (_isDisposed) {
              ad.dispose();
              return;
            }

            debugPrint('Drawer banner ad loaded successfully');

            setState(() {
              _bannerAd = ad as BannerAd;
              _isLoading = false;
              _hasError = false;
              _retryCount = 0;
            });
          },
          onAdFailedToLoad: (ad, error) {
            ad.dispose();

            if (_isDisposed) return;

            debugPrint('Drawer banner ad failed to load: ${error.message}');

            _retryCount++;

            // Report error to Crashlytics
            AdManager.reportAdError(
              adType: 'drawer_banner',
              errorMessage: error.message,
              errorCode: error.code,
              retryAttempt: _retryCount,
              additionalData: {
                'ad_unit_id': widget.adUnitId,
                'placement': 'drawer_menu',
                'use_test_ads': widget.useTestAds,
              },
            );

            // Simplified retry logic for drawer ads
            bool shouldRetry =
                _retryCount < _maxRetries &&
                (error.code == 0 || // No fill
                    error.code == 2 || // Network error
                    error.message.contains('Network error'));

            if (shouldRetry) {
              debugPrint('Retrying drawer banner ad in 3 seconds...');
              Future.delayed(const Duration(seconds: 3), () {
                if (!_isDisposed) _loadAd();
              });
            } else {
              setState(() {
                _isLoading = false;
                _hasError = true;
              });
            }
          },
        ),
      );

      bannerAd.load();
    } catch (e) {
      debugPrint('Error initializing drawer banner ad: $e');

      _retryCount++;

      // Report initialization error
      AdManager.reportAdError(
        adType: 'drawer_banner_init',
        errorMessage: e.toString(),
        errorCode: -1,
        retryAttempt: _retryCount,
        additionalData: {
          'ad_unit_id': widget.adUnitId,
          'error_type': 'initialization',
          'placement': 'drawer_menu',
        },
      );

      if (_retryCount < _maxRetries) {
        Future.delayed(const Duration(seconds: 3), () {
          if (!_isDisposed) _loadAd();
        });
      } else {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  void _retryLoadAd() {
    _retryCount = 0; // Reset retry count for manual retry
    _loadAd();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _bannerAd?.dispose();
    super.dispose();
  }
}
