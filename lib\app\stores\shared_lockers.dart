import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/helpers.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/modules/shared-lockers/models/shared_locker.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/shared_locker.dart';

class SharedLockersStore extends GetxController {
  var sharedLockers = [].obs;

  static SharedLockersStore get to => Get.find();
  final sharedLockersService = Get.find<SharedLockerService>();

  updateSharedLockers(List items) {
    sharedLockers(items);
  }

  Future<bool> fetchSharedLockers() async {
    final resp = await sharedLockersService.getSharedLockers();

    if (resp.hasError) {
      if (resp.body?["error"] == "SESSION_LOCKED") {
        ToastHelper.error("Session is locked");
        Get.offAllNamed(Routes.lockedLocker);
      } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
          resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
        await Helpers.clearData();
        Get.offAllNamed(Routes.signin);
      } else {
        ToastHelper.error("Something went wrong");
      }
      return false;
    }
    sharedLockers(resp.body["data"]);
    return true;
  }

  removeSharedLocker(String sharedLockerId) {
    sharedLockers =
        sharedLockers
            .where((item) => item["_id"] != sharedLockerId)
            .toList()
            .obs;
  }

  SharedLockerData getSharedLocker(String sharedLockerId) =>
      SharedLockerData.fromMap(
        sharedLockers.firstWhere((item) => item["_id"] == sharedLockerId),
      );
}
