import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/banner_add.dart';
import 'package:ironlocker/app/components/empty_state.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/components/locker_drawer.dart';
import 'package:ironlocker/app/components/locker_items.dart';
import 'package:ironlocker/app/constants/colors.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/modules/home/<USER>/home_controller.dart';
import 'package:ironlocker/app/stores/locker_items.dart';

class HomePage extends GetView<HomeController> {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

    return Obx(
      () => PopScope(
        onPopInvoked: (didPop) {
          if (scaffoldKey.currentState!.isDrawerOpen) {
            scaffoldKey.currentState!.openEndDrawer();
          } else if (controller.selectedItems.isNotEmpty) {
            controller.emptySelectedItems();
          } else {
            DialogHelper.confirmDialog(
              title: "Are you sure you want to exit the app?",
              onConfirm: controller.closeApp,
            );
          }
        },
        canPop: false,
        child: Scaffold(
          key: scaffoldKey,
          appBar: LockerAppBar(
            title: "Locker items",
            searchController: controller.searchController,
            showAddLockerItemButton: false, // Remove add button from app bar
            clearSearchText: controller.clearSearchText,
            selectionMode: controller.selectedItems.isNotEmpty,
            selectedItems: controller.selectedItems.length,
            selectAllItems: controller.selectAllItems,
            showNotificationsIcon: true,
            exitSelectionMode: () {
              controller.emptySelectedItems();
            },
            unmarkFavourite:
                () => DialogHelper.confirmDialog(
                  title:
                      "Are you sure you want to unmark these items as favourites?",
                  onConfirm: controller.unmarkFavourite,
                ),
            markFavourite:
                () => DialogHelper.confirmDialog(
                  title:
                      "Are you sure you want to mark these items as favourites?",
                  onConfirm: controller.markFavourite,
                ),
            deleteItems:
                () => DialogHelper.confirmDialog(
                  title: "Are you sure you want to delete these items?",
                  confirmBtnBgColor: Colors.redAccent,
                  onConfirm: controller.deleteItems,
                ),
          ),
          drawer: LockerDrawer(page: "home", scaffoldKey: scaffoldKey),
          body: Column(
            children: [
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async {
                    await LockerItemsStore.to.fetchItems();
                  },
                  color: AppColors.primaryColor,
                  backgroundColor: Get.isDarkMode ? null : Colors.white,
                  child: LockerItems(
                    selectedItems: controller.selectedItems,
                    items:
                        controller.searching.isTrue
                            ? controller.searchedItems
                            : LockerItemsStore.to.lockerItems,
                    onSelected: controller.addItemToSelectedItems,
                    onUnSelected: controller.removeItemFromSelectedItems,
                  ).withEmptyState(
                    isEmpty:
                        controller.searching.isTrue
                            ? controller.searchedItems.isEmpty
                            : LockerItemsStore.to.lockerItems.isEmpty,
                    emptyState:
                        controller.searching.isTrue
                            ? EmptyStatePresets.noSearchResults(
                              searchTerm: controller.searchController.text,
                            )
                            : EmptyState(
                              icon: Icons.lock_outline,
                              illustration:
                                  EmptyStateIllustrations.lockerIllustration(
                                    color: IronLockerColors.blue02,
                                    size: 140,
                                  ),
                              title: 'Your Secure Vault',
                              description:
                                  'Your encrypted data lives here. Add your first item to get started with secure storage.',
                              actionText: 'Add First Item',
                              onActionPressed:
                                  () => HomeController.addLockerItem(context),
                              color: IronLockerColors.blue02,
                            ),
                  ),
                ),
              ),
              // Footer with Add Button
              _buildFooter(context, controller),
              // AdMob Banner Ad at the bottom
              Container(
                decoration: BoxDecoration(
                  color: Get.isDarkMode ? Colors.grey[850] : Colors.grey[100],
                  border: Border(
                    top: BorderSide(
                      color:
                          Get.isDarkMode
                              ? Colors.grey[700]!
                              : Colors.grey[300]!,
                      width: 0.5,
                    ),
                  ),
                ),
                child: SafeArea(
                  top: false,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2.0),
                    child: BannerAdWidget(
                      useTestAds: kDebugMode, // Use test ads in debug mode
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFooter(BuildContext context, HomeController controller) {
    // Hide footer when in selection mode
    if (controller.selectedItems.isNotEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[900] : const Color(0xffF5F7FA),
        border: Border(
          top: BorderSide(
            color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
            width: 0.5,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Center(
          child: SizedBox(
            width: 56,
            height: 56,
            child: ElevatedButton(
              onPressed: () => HomeController.addLockerItem(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: IronLockerColors.blue02,
                foregroundColor: Colors.white,
                elevation: 4,
                padding: EdgeInsets.zero,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              child: const Icon(Icons.add, size: 28),
            ),
          ),
        ),
      ),
    );
  }
}
