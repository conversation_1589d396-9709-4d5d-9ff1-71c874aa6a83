import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/bottom%20sheets/add_locker_item.dart';
import 'package:ironlocker/app/components/bottom%20sheets/address_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/contact_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/folder_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/account_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/note_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/pcard_form.dart';
import 'package:ironlocker/app/helpers/decryption.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/get_folder_items.dart';
import 'package:ironlocker/app/helpers/helpers.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/locker.dart';
import 'package:ironlocker/app/stores/locker_items.dart';

class FolderController extends GetxController {
  final searchedItems = [].obs;
  final selectedItems = [].obs;
  var folders = RxList();
  final searching = false.obs;
  final searchController = TextEditingController();
  final folderItems = [].obs;
  final folderName = "".obs;
  final folderId = "".obs;
  var error = "".obs;
  final loading = true.obs;

  final lockerService = Get.find<LockerService>();

  @override
  void onInit() async {
    searchController.addListener(() {
      if (searchController.text.isEmpty) {
        searching(false);
        return;
      }

      if (searching.isFalse) {
        searching(true);
      }

      searchedItems.value =
          folderItems.where((item) {
            String itemName = item['name'];

            return itemName.toLowerCase().contains(
              searchController.text.toLowerCase(),
            );
          }).toList();
    });

    folderId(Get.parameters["id"]);
    folderName(Get.parameters["folderName"]);

    ever(folderItems, (items) {
      folders =
          folderItems
              .where((item) => item["itemType"] == "Folder")
              .toList()
              .obs;
    });

    await fetchFolderItems();
    loading(false);
    super.onInit();
  }

  clearSearchText() {
    searchController.clear();
  }

  Future fetchFolderItems() async {
    if (error.isNotEmpty) {
      loading(true);
    }
    error("");
    var resp = await lockerService.getFolderItems(Get.parameters["id"]!);

    if (resp.hasError) {
      if (resp.body?["error"] == "SESSION_LOCKED") {
        Get.offAllNamed(Routes.lockedLocker);
      } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
          resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
        await Helpers.clearData();
        Get.offAllNamed(Routes.signin);
      } else {
        if (loading.isTrue) {
          error("Something went wrong");
        } else {
          ToastHelper.error("Something went wrong");
        }
      }
      loading(false);
      return;
    }

    var data = await DecryptionHelper.decryptDynamicData(resp.body["items"]);

    folderItems(data);
    // folderName(resp.body["name"]);
    loading(false);
  }

  addFolderItem(Map item) {
    folderItems.value = [item, ...folderItems];
  }

  addLockerItem(BuildContext context) {
    var folderId = Get.parameters["id"];
    addLockerItemSheet(
      context: context,
      onPress: (itemType) {
        Navigator.of(context).pop();

        switch (itemType) {
          case "Note":
            noteFormSheet(
              context: context,
              folderId: folderId,
              onCreated: addFolderItem,
            );
            break;
          case "Contact":
            contactFormSheet(
              context: context,
              folderId: folderId,
              onCreated: addFolderItem,
            );
            break;
          case "Address":
            addressFormSheet(
              context: context,
              folderId: folderId,
              onCreated: addFolderItem,
            );
            break;
          case "Account":
            accountFormSheet(
              context: context,
              folders: folders,
              folderId: folderId,
              onCreated: addFolderItem,
            );
            break;
          case "Folder":
            folderFormSheet(
              context: context,
              folderId: folderId,
              onCreated: addFolderItem,
            );
            break;
          case "PaymentCard":
            paymentCardFormSheet(
              context: context,
              folderId: folderId,
              onCreated: addFolderItem,
            );
            break;
          default:
            (context: context, folderId: folderId, onCreated: addFolderItem);
        }
      },
    );
  }

  emptySelectedItems() {
    selectedItems([]);
  }

  addItemToSelectedItems(item) {
    selectedItems.add(item);
  }

  removeItemFromSelectedItems(id) {
    selectedItems.removeWhere((item) => item == id);
  }

  selectAllItems() {
    var items =
        LockerItemsStore.to.lockerItems
            .where((item) => item['folder'] == Get.parameters['id'])
            .map((lockerItem) => lockerItem['_id'])
            .toList();
    selectedItems(items);
  }

  deleteItems() async {
    Get.back();
    DialogHelper.loading();

    final resp = await lockerService.deleteItems(items: selectedItems);

    Get.back();

    if (resp.body?["error"] == "SESSION_LOCKED") {
      Get.offNamed(Routes.lockedLocker);
      return;
    } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
        resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
      await Helpers.clearData();
      Get.offAllNamed(Routes.signin);
      return;
    }

    if (resp.hasError) {
      ToastHelper.error("Something went wrong");
      return;
    }

    List itemsToDelete = [...selectedItems];

    for (final item in selectedItems) {
      final folderItems = getFolderItems(
        items: LockerItemsStore.to.lockerItems,
        folderId: item,
      );

      itemsToDelete.addAll(folderItems);
    }

    LockerItemsStore.to.removeLockerItems(itemsToDelete);

    selectedItems([]);
    ToastHelper.success("Items deleted");
  }

  markFavourite() async {
    Get.back();
    DialogHelper.loading();

    final resp = await lockerService.markFavourite(items: selectedItems);

    Get.back();

    if (resp.body?["error"] == "SESSION_LOCKED") {
      Get.offNamed(Routes.lockedLocker);
      return;
    } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
        resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
      await Helpers.clearData();
      Get.offAllNamed(Routes.signin);
      return;
    }

    if (resp.hasError) {
      ToastHelper.error("Something went wrong");
      return;
    }

    LockerItemsStore.to.markFavourite(selectedItems);

    emptySelectedItems();
  }

  unmarkFavourite() async {
    Get.back();
    DialogHelper.loading();

    final resp = await lockerService.unmarkFavourite(items: selectedItems);

    Get.back();

    if (resp.body?["error"] == "SESSION_LOCKED") {
      Get.offNamed(Routes.lockedLocker);
      return;
    } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
        resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
      await Helpers.clearData();
      Get.offAllNamed(Routes.signin);
      return;
    }

    if (resp.hasError) {
      ToastHelper.error("Something went wrong");
      return;
    }

    LockerItemsStore.to.unmarkFavourite(selectedItems);

    emptySelectedItems();
  }
}
