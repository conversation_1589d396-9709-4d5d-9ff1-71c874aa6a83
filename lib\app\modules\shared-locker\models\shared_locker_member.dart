class SharedLockerMember {
  final String membershipId;
  final String id;
  final String name;
  final String email;
  final String permission;

  SharedLockerMember(
      {required this.membershipId,
      required this.id,
      required this.name,
      required this.email,
      required this.permission});

  factory SharedLockerMember.fromJson(Map<String, dynamic> json) {
    return SharedLockerMember(
      membershipId: json['membershipId'],
      id: json['_id'],
      name: json['name'],
      email: json['email'],
      permission: json['permission'],
    );
  }
}
