import 'package:flutter/material.dart';

class Loading extends StatelessWidget {
  const Loading({super.key});

  @override
  Widget build(BuildContext context) {
    return const Stack(
      children: [
        Opacity(
          opacity: 0.3,
          child: <PERSON><PERSON><PERSON><PERSON><PERSON>(dismissible: false, color: Colors.grey),
        ),
        Center(
          child: Si<PERSON>B<PERSON>(
            height: 40,
            width: 40,
            child: CircularProgressIndicator(),
          ),
        ),
      ],
    );
  }
}
