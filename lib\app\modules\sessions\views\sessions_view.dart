import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/banner_add.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/constants/colors.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/date_formatter.dart';
import 'package:ironlocker/app/modules/sessions/models/session.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/stores/user.dart';
import 'package:ironlocker/app/stores/user_sessions.dart';
import 'package:ironlocker/app/themes/app_bar.dart';

import '../controllers/sessions_controller.dart';

class SessionsView extends GetView<SessionsController> {
  const SessionsView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Get.isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
      appBar: LockerAppBar(
        title: "Active Sessions",
        leading: AppbarTheme.buildStyledBackButton(
          onPressed: () => Get.back(),
          tooltip: 'Back',
        ),
        showSearchIcon: false,
        showAddLockerItemButton: false,
        popupMenuButton: PopupMenuButton(
          iconColor: Colors.white,
          itemBuilder:
              (context) => [
                PopupMenuItem(
                  height: 35,
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  onTap:
                      () => DialogHelper.confirmDialog(
                        title:
                            "Are you sure you want to terminate all sessions?",
                        confirmBtnBgColor: Colors.redAccent,
                        onConfirm: controller.terminateAllSessions,
                      ),
                  child: Row(
                    children: [
                      const Icon(Icons.logout, size: 16, color: Colors.red),
                      const SizedBox(width: 8),
                      const Text("Terminate all"),
                    ],
                  ),
                ),
              ],
        ),
      ),
      body: Column(
        children: [
          // Header Info Section
          _buildHeaderInfo(),

          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                await controller.fetchSessions();
              },
              color: AppColors.primaryColor,
              backgroundColor: Get.isDarkMode ? null : Colors.white,
              child: Obx(
                () =>
                    controller.loading.isTrue
                        ? const Center(
                          child: SizedBox(
                            height: 50,
                            width: 50,
                            child: CircularProgressIndicator(
                              color: AppColors.primaryColor,
                            ),
                          ),
                        )
                        : UserSessionsStore.to.userSessions.isEmpty
                        ? _buildEmptyState()
                        : ListView.builder(
                          padding: const EdgeInsets.all(20),
                          itemCount: UserSessionsStore.to.userSessions.length,
                          itemBuilder: (BuildContext context, int index) {
                            var session = SessionData.fromMap(
                              UserSessionsStore.to.userSessions[index],
                            );
                            return _buildSessionCard(context, session, index);
                          },
                        ),
              ),
            ),
          ),

          // AdMob Banner Ad at the bottom
          Container(
            color: Get.isDarkMode ? Colors.grey[900] : Colors.grey[50],
            child: SafeArea(
              top: false,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: BannerAdWidget(useTestAds: kDebugMode),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderInfo() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.withValues(alpha: 0.1),
            Colors.purple.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(Icons.devices, color: Colors.blue, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Session Management",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Get.isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Obx(
                  () => Text(
                    "${UserSessionsStore.to.userSessions.length} active sessions",
                    style: TextStyle(
                      fontSize: 14,
                      color:
                          Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(50),
              ),
              child: const Icon(
                Icons.devices_other,
                size: 48,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              "No Active Sessions",
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Get.isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              "You don't have any active sessions at the moment.",
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSessionCard(
    BuildContext context,
    SessionData session,
    int index,
  ) {
    final isCurrentSession = session.id == UserStore.to.sessionId.value;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color:
              isCurrentSession
                  ? Colors.blue.withValues(alpha: 0.5)
                  : (Get.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!),
          width: isCurrentSession ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap:
              () => Get.toNamed(
                Routes.SESSION_DETAIL,
                arguments: {"id": session.id},
              ),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Row
                Row(
                  children: [
                    // Country Flag or Location Icon
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child:
                          session.countryFlag != null
                              ? ClipRRect(
                                borderRadius: BorderRadius.circular(10),
                                child: Image.network(
                                  session.countryFlag!,
                                  width: 40,
                                  height: 40,
                                  fit: BoxFit.cover,
                                  errorBuilder:
                                      (context, error, stackTrace) =>
                                          const Icon(
                                            Icons.location_on,
                                            color: Colors.blue,
                                          ),
                                ),
                              )
                              : const Icon(
                                Icons.location_on,
                                color: Colors.blue,
                              ),
                    ),
                    const SizedBox(width: 16),

                    // Location Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "${session.city ?? 'Unknown'}, ${session.country ?? 'Unknown'}",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color:
                                  Get.isDarkMode
                                      ? Colors.white
                                      : Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            session.ipAddress,
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  Get.isDarkMode
                                      ? Colors.grey[400]
                                      : Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Status Badge
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(session).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        _getStatusText(session, isCurrentSession),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: _getStatusColor(session),
                        ),
                      ),
                    ),

                    // Menu Button
                    PopupMenuButton(
                      icon: Icon(
                        Icons.more_vert,
                        color:
                            Get.isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[600],
                      ),
                      itemBuilder:
                          (context) => [
                            _buildMenuEntry(
                              text: "View Details",
                              icon: Icons.visibility_outlined,
                              onPress:
                                  () => Get.toNamed(
                                    Routes.SESSION_DETAIL,
                                    arguments: {"id": session.id},
                                  ),
                            ),
                            if (!session.locked && !isCurrentSession)
                              _buildMenuEntry(
                                text: "Lock Session",
                                icon: Icons.lock_outline,
                                onPress:
                                    () => DialogHelper.confirmDialog(
                                      title:
                                          "Are you sure you want to lock this session?",
                                      onConfirm:
                                          () => controller.lockSession(
                                            session.id,
                                          ),
                                    ),
                              ),
                            if (!isCurrentSession)
                              _buildMenuEntry(
                                text: "Terminate",
                                icon: Icons.logout,
                                onPress:
                                    () => DialogHelper.confirmDialog(
                                      title:
                                          "Are you sure you want to terminate this session?",
                                      confirmBtnBgColor: Colors.redAccent,
                                      onConfirm:
                                          () => controller.terminateSession(
                                            session.id,
                                          ),
                                    ),
                              ),
                          ],
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Additional Info Row
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoChip(
                        icon: Icons.access_time,
                        label: "Last Active",
                        value: DateFormatter.format(
                          session.lastActive.toLocal(),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildInfoChip(
                        icon: Icons.schedule,
                        label: "Created",
                        value: DateFormatter.format(
                          session.createdAt.toLocal(),
                        ),
                      ),
                    ),
                  ],
                ),

                if (isCurrentSession) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.info_outline,
                          color: Colors.blue,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          "This is your current active session",
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[800] : Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 14,
                color: Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color: Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Get.isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(SessionData session) {
    if (session.locked) return Colors.red;
    if (session.id == UserStore.to.sessionId.value) return Colors.blue;
    return Colors.green;
  }

  String _getStatusText(SessionData session, bool isCurrentSession) {
    if (session.locked) return "Locked";
    if (isCurrentSession) return "Current";
    return "Active";
  }

  PopupMenuEntry _buildMenuEntry({
    required String text,
    required IconData icon,
    required VoidCallback onPress,
    bool hide = false,
  }) {
    return hide
        ? const PopupMenuItem(height: 0, child: SizedBox())
        : PopupMenuItem(
          height: 35,
          padding: const EdgeInsets.symmetric(horizontal: 10),
          onTap: onPress,
          child: Row(
            children: [
              Icon(icon, size: 18),
              const SizedBox(width: 10),
              Text(text),
            ],
          ),
        );
  }
}
