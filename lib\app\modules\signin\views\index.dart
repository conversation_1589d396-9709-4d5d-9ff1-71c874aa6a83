import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/modules/signin/controller/index.dart';
import 'package:url_launcher/url_launcher.dart';

class SignInPage extends GetView<SignInController> {
  const SignInPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(28),
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 480),
              child: _buildForm(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: controller.formKey,
      child: Builder(
        builder:
            (context) => Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Gap(16),

                // IronLocker Logo
                Container(
                  width: double.infinity,
                  alignment: Alignment.center,
                  child: Image.asset(
                    'assets/images/blue_full_logo.png',
                    width: 180,
                    height: 40,
                    fit: BoxFit.contain,
                  ),
                ),
                const Gap(24),
                // Subtitle
                Text(
                  "Enter your credentials to continue",
                  style: TextStyle(
                    fontSize: _getResponsiveSubtitleSize(context),
                    fontFamily: 'Jura',
                    color:
                        Get.isDarkMode
                            ? Colors.grey.shade300
                            : Colors.grey.shade600,
                  ),
                  textAlign: TextAlign.center,
                ),
                const Gap(32),

                // Email Field
                _buildFormField(
                  label: "Email Address",
                  hintText: "Enter your email address",
                  keyboardType: TextInputType.emailAddress,
                  controller: controller.emailController,
                ),
                const Gap(20),

                // Password Field
                Obx(
                  () => _buildFormField(
                    label: "Master Password",
                    hintText: "Enter your master password",
                    obscureText: !controller.showPassword.value,
                    controller: controller.passwordController,
                    showPasswordToggle: true,
                    onTogglePassword: controller.togglePasswordVisibility,
                  ),
                ),
                const Gap(24),
                // Forgot Password Link
                InkWell(
                  onTap: () async {
                    final Uri url = Uri.parse(
                      'https://www.ironlocker.app/forgot-password',
                    );
                    if (await canLaunchUrl(url)) {
                      await launchUrl(
                        url,
                        mode: LaunchMode.externalApplication,
                      );
                    }
                  },
                  child: Row(
                    children: [
                      Text(
                        "Forgot Password?",
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Jura',
                          color:
                              Get.isDarkMode
                                  ? Colors.grey.shade400
                                  : Colors.grey.shade600,
                        ),
                      ),
                      Gap(5),
                      Text(
                        "Reset",
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Jura',
                          color: IronLockerColors.blue02,
                        ),
                      ),
                    ],
                  ),
                ),
                const Gap(24),

                // Sign In Button
                Obx(
                  () => _buildPrimaryButton(
                    text:
                        controller.loading.value ? "Signing in..." : "Sign In",
                    loading: controller.loading.value,
                    onPressed:
                        controller.canSignIn.value
                            ? controller.handleSignIn
                            : () {},
                    enabled: controller.canSignIn.value,
                  ),
                ),
                const Gap(24),

                InkWell(
                  onTap: () async {
                    final Uri url = Uri.parse(
                      'https://www.ironlocker.app/signup',
                    );
                    if (await canLaunchUrl(url)) {
                      await launchUrl(
                        url,
                        mode: LaunchMode.externalApplication,
                      );
                    }
                  },
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF5F7FA),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          "Don't have an account?",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Jura',
                            color:
                                Get.isDarkMode
                                    ? Colors.white
                                    : Colors.grey.shade700,
                          ),
                        ),
                        Gap(5),
                        Text(
                          "Sign Up",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Jura',
                            color: IronLockerColors.secondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const Gap(40),
              ],
            ),
      ),
    );
  }

  // Helper method to build simple form field
  Widget _buildFormField({
    required String label,
    required String hintText,
    required TextEditingController controller,
    TextInputType? keyboardType,
    bool obscureText = false,
    bool showPasswordToggle = false,
    VoidCallback? onTogglePassword,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Text(
          label.toUpperCase(),
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            fontFamily: 'Jura',
            color: Get.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
          ),
        ),
        const Gap(8),

        // Input Field
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          obscureText: obscureText,
          style: TextStyle(
            fontSize: 14,
            fontFamily: 'Jura',
            fontWeight: FontWeight.w400,
            color: Get.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
          ),
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: TextStyle(
              color:
                  Get.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade500,
              fontFamily: 'Jura',
              fontSize: 14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color:
                    Get.isDarkMode
                        ? Colors.grey.shade600
                        : Colors.grey.shade300,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color:
                    Get.isDarkMode
                        ? Colors.grey.shade600
                        : Colors.grey.shade300,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: IronLockerColors.blue02, width: 2),
            ),
            filled: true,
            fillColor: Get.isDarkMode ? Colors.grey.shade800 : Colors.white,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
            suffixIcon:
                showPasswordToggle
                    ? Container(
                      width: 40,
                      height: 40,
                      margin: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: IronLockerColors.blue02.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color: IronLockerColors.blue02.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: onTogglePassword,
                          borderRadius: BorderRadius.circular(6),
                          child: Icon(
                            obscureText
                                ? Icons.visibility_outlined
                                : Icons.visibility_off_outlined,
                            color: IronLockerColors.blue02,
                            size: 18,
                          ),
                        ),
                      ),
                    )
                    : null,
          ),
        ),
      ],
    );
  }

  // Helper method to build primary button
  Widget _buildPrimaryButton({
    required String text,
    required VoidCallback onPressed,
    bool loading = false,
    bool enabled = true,
  }) {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        color:
            enabled && !loading
                ? IronLockerColors.blue02
                : IronLockerColors.blue02.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: enabled && !loading ? onPressed : null,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            child:
                loading
                    ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        ),
                        const Gap(12),
                        Text(
                          text,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w700,
                            fontFamily: 'Jura',
                            color: Colors.white,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ],
                    )
                    : Center(
                      child: Text(
                        text,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w700,
                          fontFamily: 'Jura',
                          color: Colors.white,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
          ),
        ),
      ),
    );
  }

  // Helper method to get responsive subtitle size
  double _getResponsiveSubtitleSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 360) {
      // Very small phones
      return 13;
    } else if (screenWidth < 400) {
      // Small phones
      return 14;
    } else if (screenWidth < 600) {
      // Regular phones
      return 15;
    } else if (screenWidth < 900) {
      // Large phones / small tablets
      return 16;
    } else {
      // Tablets and larger
      return 17;
    }
  }
}
