import 'package:ironlocker/app/modules/shared-locker/models/shared_locker_member.dart';

class SharedLockerData {
  final String id;
  final String name;
  final String creator;
  final List<SharedLockerMember> members;
  final DateTime createdAt;

  SharedLockerData({
    required this.id,
    required this.name,
    required this.creator,
    required this.members,
    required this.createdAt,
  });

  factory SharedLockerData.fromMap(Map<dynamic, dynamic> map) {
    var membersList = map['members'] as List;
    List<SharedLockerMember> members = membersList
        .map((member) => SharedLockerMember.fromJson(member))
        .toList();
    return SharedLockerData(
      id: map['_id'],
      name: map['name'],
      creator: map['creator'],
      members: members,
      createdAt: DateTime.parse(map['createdAt']),
    );
  }
}
