class Baccount {
  final String id;
  final String name;
  final String? folder;
  final String itemType;
  final String? accountHolderName;
  final String? accountNumber;
  final String? bankName;
  final String? branchName;
  final String? accountType;
  final String? ibanNumber;
  final String? swiftCode;
  final String? pin;
  final String? notes;
  final bool favourite;

  Baccount(
      {required this.id,
      required this.name,
      required this.favourite,
      required this.itemType,
      this.accountHolderName,
      this.folder,
      this.accountNumber,
      this.branchName,
      this.bankName,
      this.accountType,
      this.notes,
      this.ibanNumber,
      this.swiftCode,
      this.pin});

  static Baccount fromJSON(data) {
    return Baccount(
      id: data['_id'],
      name: data['name'],
      folder: data['folder'],
      itemType: data['itemType'],
      accountHolderName: data['accountHolderName'],
      accountNumber: data['accountNumber'],
      bankName: data['bankName'],
      branchName: data['branchName'],
      accountType: data['accountType'],
      ibanNumber: data['ibanNumber'],
      swiftCode: data['swiftCode'],
      pin: data['pin'],
      notes: data['notes'],
      favourite: data['favourite'],
    );
  }
}
