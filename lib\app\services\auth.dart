import 'package:get/get.dart';
import 'package:ironlocker/app/constants/endpoints.dart';
import 'package:ironlocker/app/helpers/device_info.dart';

class AuthService extends GetConnect {
  final endPoints = ApiEndpoints();
  AuthService() {
    httpClient.timeout = const Duration(seconds: 30);
    httpClient.addRequestModifier<dynamic>((request) async {
      var device = await DeviceInfo.device();
      request.headers['User-Agent'] = device;
      return request;
    });
  }

  Future<Response> signin(dynamic body) {
    return post("${endPoints.auth}/signin", body);
  }

  Future<Response> complete2fa(Map body) {
    return post("${endPoints.auth}/complete-2fa", body);
  }

  Future<Response> signup(dynamic body) {
    return post("${endPoints.auth}/signup", body);
  }

  Future<Response> forgotPassword(dynamic body) {
    return post("${endPoints.auth}/forgot-password", body);
  }
}
