import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:ironlocker/app/controllers/ad_free_controller.dart';
import 'package:ironlocker/app/helpers/ad_free_manager.dart';
import 'package:ironlocker/app/helpers/theme.dart';
import 'package:ironlocker/app/services/analytics_service.dart';
import 'package:ironlocker/app/services/locker.dart';
import 'package:ironlocker/app/services/notifications.dart';
import 'package:ironlocker/app/services/shared_locker.dart';
import 'package:ironlocker/app/services/shared_locker_member.dart';
import 'package:ironlocker/app/services/subscription_service.dart';
import 'package:ironlocker/app/services/user.dart';
import 'package:ironlocker/app/services/user_sessions.dart';
import 'package:ironlocker/app/services/websocket_service.dart';
import 'package:ironlocker/app/controllers/websocket_controller.dart';
import 'package:ironlocker/app/stores/locker_items.dart';
import 'package:ironlocker/app/stores/secure_storage.dart';
import 'package:ironlocker/app/stores/shared_lockers.dart';
import 'package:ironlocker/app/stores/user.dart';
import 'package:ironlocker/app/stores/user_sessions.dart';

Future initServices() async {
  await GetStorage.init();
  Get.put(ThemeController());
  await Get.putAsync<SecureStorageService>(() async => SecureStorageService());

  // Initialize UserStore before Analytics Service
  Get.put(UserStore());

  // Initialize Analytics Service after UserStore
  await Get.putAsync<AnalyticsService>(() async => AnalyticsService());

  Get.put(LockerService());
  Get.put(LockerItemsStore());
  Get.put(UserService());
  Get.put(UserSessionsService());
  Get.put(SharedLockerService());
  Get.put(SharedLockerMemberService());
  Get.put(NotificationsService());
  Get.put(UserSessionsStore());
  Get.put(SharedLockersStore());

  // Initialize Ad-Free Manager for rewarded video ads
  await Get.putAsync<AdFreeManager>(() async => AdFreeManager());

  // Initialize Ad-Free Controller for real-time reactive updates
  Get.put(AdFreeController());

  // Initialize Subscription Service for RevenueCat
  await Get.putAsync<SubscriptionService>(() async => SubscriptionService());

  // Initialize WebSocket Service for real-time communication
  Get.put(WebSocketService());

  // Initialize WebSocket Controller for managing connections
  Get.put(WebSocketController());
}
