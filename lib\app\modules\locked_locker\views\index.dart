import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/constants/colors.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import '../../../components/button.dart';
import '../../../components/input_field.dart';
import '../../../constants/sizes.dart';
import '../controller/index.dart';

class LockedLockerPage extends GetView<LockedLockerController> {
  const LockedLockerPage({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();

    return PopScope(
      canPop: true,
      child: Scaffold(
        body: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(AppSizes.pagePadding),
              child: Align(
                alignment: Alignment.topCenter,
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 420),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(7),
                  ),
                  padding: const EdgeInsets.all(10),
                  child: Form(
                    key: formKey,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Text(
                          "Locker is locked",
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        const Gap(10),
                        const Text(
                          "Enter your password to unlock",
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        const Gap(15),
                        InputField(
                          hintText: "Password",
                          obscureText: true,
                          controller: controller.passwordController,
                          validator: (val) {
                            if (val!.isEmpty) {
                              return "Your password is required";
                            }
                            return null;
                          },
                        ),
                        const Gap(10),
                        Obx(
                          () => Text(
                            "Signed in as ${controller.email}",
                            textAlign: TextAlign.center,
                            style: const TextStyle(fontSize: 15),
                          ),
                        ),
                        const Gap(10),
                        Row(
                          children: [
                            Expanded(
                              child: Obx(
                                () => Button(
                                  text: "Unlock",
                                  onPress:
                                      () => controller.handleUnlock(formKey),
                                  loading: controller.loading.value,
                                ),
                              ),
                            ),
                            const Gap(10),
                            Expanded(
                              child: Obx(
                                () => IgnorePointer(
                                  ignoring: controller.loading.isTrue,
                                  child: Button(
                                    text: "Log out",
                                    onPress:
                                        () => DialogHelper.confirmDialog(
                                          title:
                                              "Are you sure you want to log out?",
                                          onConfirm: controller.handleLogout,
                                        ),
                                    bgColor: Colors.white,
                                    fgColor: AppColors.primaryColor,
                                    borderColor: const Color(0xFF448AFF),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
