import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/locker_item_icon.dart';
import 'package:ironlocker/app/components/bottom%20sheets/address_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/baccount_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/contact_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/folder_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/account_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/note_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/pcard_form.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/helpers/get_folder_items.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/locker.dart';
import 'package:ironlocker/app/stores/locker_items.dart';

import '../helpers/dialog.dart';
import 'bottom sheets/address_details.dart';
import 'bottom sheets/baccount_details.dart';
import 'bottom sheets/contact_details.dart';
import 'bottom sheets/account_details.dart';
import 'bottom sheets/note_details.dart';
import 'bottom sheets/pcard_details.dart';

class LockerItem extends StatefulWidget {
  const LockerItem({
    super.key,
    this.selectionMode = false,
    this.selected = false,
    this.showOptions = true,
    this.onPress,
    this.onSelected,
    this.onUnSelected,
    this.encryptionKey,
    required this.id,
    required this.title,
    required this.itemType,
    required this.lockerItem,
    this.favourite = false,
  });
  final bool selectionMode;
  final bool selected;
  final String id;
  final String title;
  final String itemType;
  final Map<dynamic, dynamic> lockerItem;
  final bool favourite;
  final bool showOptions;
  final VoidCallback? onSelected;
  final VoidCallback? onUnSelected;
  final VoidCallback? onPress;
  final String? encryptionKey;

  @override
  State<LockerItem> createState() => _LockerItemState();
}

class _LockerItemState extends State<LockerItem> {
  final lockerService = Get.find<LockerService>();

  viewItem() {
    switch (widget.itemType) {
      case "Note":
        noteDetails(context: context, note: widget.lockerItem);
        break;
      case "Contact":
        contactDetails(context: context, contact: widget.lockerItem);
        break;
      case "Address":
        addressDetails(context: context, address: widget.lockerItem);
        break;
      case "Account":
        accountDetails(context: context, account: widget.lockerItem);
        break;
      case "Folder":
        if (widget.lockerItem["folder"] != null) {
          Get.offNamed(
            widget.lockerItem["sharedLocker"] != null
                ? Routes.SHARED_FOLDER
                : Routes.folder,
            parameters: {
              "id": widget.lockerItem["_id"],
              "sharedLocker": widget.lockerItem["sharedLocker"] ?? "",
              "folderName": widget.lockerItem["name"],
              "parent": widget.lockerItem["folder"],
            },
          );
          return;
        }

        Get.toNamed(
          widget.lockerItem["sharedLocker"] != null
              ? Routes.SHARED_FOLDER
              : Routes.folder,
          parameters: {
            "id": widget.lockerItem["_id"],
            "sharedLocker": widget.lockerItem["sharedLocker"] ?? "",
            "folderName": widget.lockerItem["name"],
          },
        );
        break;
      case "PaymentCard":
        paymentCardDetails(context: context, paymentCard: widget.lockerItem);
        break;
      default:
        bankAccountDetails(context: context, bankAccount: widget.lockerItem);
    }
  }

  editItem() {
    switch (widget.itemType) {
      case "Note":
        noteFormSheet(
          context: context,
          updateNote: widget.lockerItem,
          encryptionKey: widget.encryptionKey,
        );
        break;
      case "Contact":
        contactFormSheet(
          context: context,
          updateContact: widget.lockerItem,
          encryptionKey: widget.encryptionKey,
        );
        break;
      case "Address":
        addressFormSheet(
          context: context,
          updateAddress: widget.lockerItem,
          encryptionKey: widget.encryptionKey,
        );
        break;
      case "Account":
        accountFormSheet(
          context: context,
          updateAccount: widget.lockerItem,
          encryptionKey: widget.encryptionKey,
        );
        break;
      case "Folder":
        folderFormSheet(
          context: context,
          updateFolder: widget.lockerItem,
          encryptionKey: widget.encryptionKey,
        );
        break;
      case "PaymentCard":
        paymentCardFormSheet(
          context: context,
          updatePaymentCard: widget.lockerItem,
          encryptionKey: widget.encryptionKey,
        );
        break;
      default:
        bankAccountFormSheet(
          context: context,
          updateBankAccount: widget.lockerItem,
          encryptionKey: widget.encryptionKey,
        );
    }
  }

  deleteItem() async {
    Get.back();

    DialogHelper.loading();

    final resp = await lockerService.deleteItem(itemId: widget.id);

    if (resp.hasError) {
      Get.back();
      ToastHelper.error("Something went wrong");
      return;
    }

    List itemsToDelete = [widget.id];

    final itemToDelete = LockerItemsStore.to.lockerItems.firstWhere(
      (item) => item['_id'] == widget.id,
    );

    if (itemToDelete['itemType'] == "Folder") {
      final folderItems = getFolderItems(
        items: LockerItemsStore.to.lockerItems,
        folderId: widget.id,
      );

      itemsToDelete.addAll(folderItems);
    }

    LockerItemsStore.to.removeLockerItems(itemsToDelete);

    ToastHelper.success("Item deleted");
    Get.back();
  }

  markFavourite() async {
    Get.back();
    DialogHelper.loading();

    final resp = await lockerService.markFavourite(items: [widget.id]);

    Get.back();

    if (resp.hasError) {
      Get.back();
      ToastHelper.error("Something went wrong");
      return;
    }

    LockerItemsStore.to.markFavourite([widget.id]);
  }

  unmarkFavourite() async {
    Get.back();
    DialogHelper.loading();

    final resp = await lockerService.unmarkFavourite(items: [widget.id]);

    Get.back();

    if (resp.hasError) {
      Get.back();
      ToastHelper.error("Something went wrong");
      return;
    }

    LockerItemsStore.to.unmarkFavourite([widget.id]);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onLongPress: () {
        if (widget.selectionMode ||
            widget.onSelected == null ||
            widget.lockerItem["sharedLocker"] != null) {
          return;
        }
        widget.onSelected!();
      },
      onTap:
          widget.onPress ??
          () {
            if (!widget.selectionMode ||
                widget.onSelected == null ||
                widget.onUnSelected == null) {
              return;
            }

            widget.selected ? widget.onUnSelected!() : widget.onSelected!();
          },
      child: Container(
        constraints: const BoxConstraints(minHeight: 160, maxHeight: 200),
        decoration: BoxDecoration(
          color: Get.isDarkMode ? Colors.grey[850] : Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color:
                widget.selected
                    ? Colors.blue
                    : (Get.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!),
            width: widget.selected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
              blurRadius: widget.selected ? 12 : 8,
              offset: const Offset(0, 4),
              spreadRadius: widget.selected ? 2 : 0,
            ),
          ],
        ),
        child: Stack(
          children: [
            // Main content
            Positioned.fill(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Icon container
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: IronLockerColors.blue02.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: IronLockerColors.blue02.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: LockerItemIcon(
                        itemType: widget.itemType,
                        size: widget.showOptions ? 28 : 20,
                        color: IronLockerColors.blue02,
                      ),
                    ),
                    const SizedBox(height: 12),
                    // Title
                    Text(
                      widget.title,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: widget.showOptions ? 14 : 12,
                        fontWeight: FontWeight.w600,
                        color: Get.isDarkMode ? Colors.white : Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    // Item type badge
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: IronLockerColors.blue02.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        widget.itemType,
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: IronLockerColors.blue02,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Selection checkbox
            if (widget.selectionMode)
              Positioned(
                top: 8,
                left: 8,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(6),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Transform.scale(
                    scale: 0.9,
                    child: Checkbox(
                      value: widget.selected,
                      activeColor: Colors.blue,
                      onChanged: (val) {
                        if (widget.onSelected == null ||
                            widget.onUnSelected == null) {
                          return;
                        }
                        widget.selected
                            ? widget.onUnSelected!()
                            : widget.onSelected!();
                      },
                    ),
                  ),
                ),
              ),

            // Favourite button
            if (!widget.selectionMode &&
                widget.showOptions &&
                widget.lockerItem["sharedLocker"] == null)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  decoration: BoxDecoration(
                    color:
                        widget.favourite
                            ? Colors.amber.withValues(alpha: 0.1)
                            : (Get.isDarkMode
                                ? Colors.grey[800]
                                : Colors.white),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color:
                          widget.favourite
                              ? Colors.amber.withValues(alpha: 0.3)
                              : (Get.isDarkMode
                                  ? Colors.grey[600]!
                                  : Colors.grey[300]!),
                      width: 1,
                    ),
                  ),
                  child: IconButton(
                    onPressed:
                        () => DialogHelper.confirmDialog(
                          title:
                              "Are you sure you want to ${widget.favourite ? 'unmark' : 'mark'} this item as favourite?",
                          onConfirm:
                              widget.favourite
                                  ? unmarkFavourite
                                  : markFavourite,
                        ),
                    icon: Icon(
                      widget.favourite ? Icons.star : Icons.star_outline,
                      size: 18,
                      color:
                          widget.favourite
                              ? Colors.amber[700]
                              : (Get.isDarkMode
                                  ? Colors.grey[400]
                                  : Colors.grey[600]),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                    padding: EdgeInsets.zero,
                  ),
                ),
              ),

            // Menu button
            if (!widget.selectionMode && widget.showOptions)
              Positioned(
                bottom: 8,
                right: 8,
                child: Container(
                  decoration: BoxDecoration(
                    color: Get.isDarkMode ? Colors.grey[800] : Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color:
                          Get.isDarkMode
                              ? Colors.grey[600]!
                              : Colors.grey[300]!,
                      width: 1,
                    ),
                  ),
                  child: PopupMenuButton(
                    icon: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color:
                            Get.isDarkMode
                                ? Colors.grey[800]?.withValues(alpha: 0.6)
                                : Colors.grey[50],
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color:
                              Get.isDarkMode
                                  ? Colors.grey[700]!
                                  : Colors.grey[300]!,
                          width: 0.5,
                        ),
                      ),
                      child: Icon(
                        Icons.more_vert_rounded,
                        size: 14,
                        color:
                            Get.isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[600],
                      ),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 28,
                      minHeight: 28,
                    ),
                    padding: EdgeInsets.zero,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 4,
                    shadowColor: Colors.black.withValues(alpha: 0.15),
                    color:
                        Get.isDarkMode ? const Color(0xFF2A2A2A) : Colors.white,
                    surfaceTintColor: Colors.transparent,
                    itemBuilder:
                        (context) => [
                          _buildMenuEntry(
                            text: "View Details",
                            icon: Icons.visibility_rounded,
                            onPress: viewItem,
                            color: Colors.blue,
                          ),
                          _buildMenuDivider(),
                          _buildMenuEntry(
                            text: "Edit Item",
                            icon: Icons.edit_rounded,
                            onPress: editItem,
                            color: Colors.orange,
                          ),
                          _buildMenuDivider(),
                          _buildMenuEntry(
                            text: "Delete Item",
                            icon: Icons.delete_rounded,
                            onPress:
                                () => DialogHelper.confirmDialog(
                                  title:
                                      "Are you sure you want to delete this item?",
                                  confirmBtnBgColor: Colors.redAccent,
                                  onConfirm: deleteItem,
                                ),
                            color: Colors.red,
                            isDestructive: true,
                          ),
                        ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Color _getItemTypeColor() {
    switch (widget.itemType) {
      case "Contact":
        return Colors.blue;
      case "Note":
        return Colors.orange;
      case "Address":
        return Colors.green;
      case "Account":
        return Colors.purple;
      case "PaymentCard":
        return Colors.indigo;
      case "BankAccount":
        return Colors.teal;
      case "Folder":
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }

  PopupMenuEntry _buildMenuEntry({
    required String text,
    required IconData icon,
    required VoidCallback onPress,
    required Color color,
    bool isDestructive = false,
  }) {
    return PopupMenuItem(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      onTap: onPress,
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.08),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(icon, size: 16, color: color),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                fontFamily: 'Jura',
                color:
                    isDestructive
                        ? Colors.red
                        : (Get.isDarkMode ? Colors.white : Colors.black87),
              ),
            ),
          ),
        ],
      ),
    );
  }

  PopupMenuEntry _buildMenuDivider() {
    return PopupMenuItem(
      height: 0.5,
      enabled: false,
      padding: EdgeInsets.zero,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 12),
        height: 0.5,
        color: Get.isDarkMode ? Colors.grey[700] : Colors.grey[300],
      ),
    );
  }
}
