import 'package:flutter/material.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/themes/app_bar.dart';

import '../controllers/two_factor_methods_controller.dart';

class TwoFactorMethodsView extends GetView<TwoFactorMethodsController> {
  const TwoFactorMethodsView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Get.isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
      appBar: LockerAppBar(
        title: "Choose 2FA Method",
        leading: AppbarTheme.buildStyledBackButton(
          onPressed: () => Get.back(),
          tooltip: 'Back',
        ),
        showSearchIcon: false,
        showAddLockerItemButton: false,
      ),
      body: Column(
        children: [
          // Header Section
          _buildHeaderSection(),

          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  _buildMethodCard(
                    icon: Icons.quiz_outlined,
                    title: "Security Question",
                    subtitle:
                        "Answer a personal question to verify your identity",
                    color: Colors.blue,
                    onTap:
                        () => Get.toNamed(
                          Routes.TWO_FACTOR_QUESTION,
                          parameters: {"password": Get.parameters['password']!},
                        ),
                  ),
                  const SizedBox(height: 16),
                  _buildMethodCard(
                    icon: Icons.pin_outlined,
                    title: "PIN Code",
                    subtitle: "Use a 4-digit PIN for quick authentication",
                    color: Colors.green,
                    onTap:
                        () => Get.toNamed(
                          Routes.TWO_FACTOR_PIN,
                          parameters: {"password": Get.parameters['password']!},
                        ),
                  ),
                  const SizedBox(height: 16),
                  _buildMethodCard(
                    icon: Icons.smartphone_outlined,
                    title: "Authenticator App",
                    subtitle: "Use Google Authenticator or similar apps",
                    color: Colors.purple,
                    onTap:
                        () => Get.toNamed(
                          Routes.SCAN_QRCODE,
                          parameters: {"password": Get.parameters['password']!},
                        ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.green.withValues(alpha: 0.1),
            Colors.blue.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.verified_user,
              color: Colors.green,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Enable Two-Factor Authentication",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Get.isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  "Choose your preferred authentication method",
                  style: TextStyle(
                    fontSize: 14,
                    color: Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMethodCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: color.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Icon(icon, color: color, size: 28),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Get.isDarkMode ? Colors.white : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color:
                              Get.isDarkMode
                                  ? Colors.grey[400]
                                  : Colors.grey[600],
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 18,
                  color: Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
