import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/banner_add.dart';
import 'package:ironlocker/app/components/empty_state.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/components/locker_drawer.dart';
import 'package:ironlocker/app/components/locker_items.dart';

import 'package:ironlocker/app/constants/colors.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/modules/favourites/controllers/favourites_controller.dart';
import 'package:ironlocker/app/stores/locker_items.dart';

class FavouritesPage extends GetView<FavouritesController> {
  const FavouritesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (controller.selectedItems.isNotEmpty) {
          controller.emptySelectedItems();
          return false;
        }
        return true;
      },
      child: Obx(
        () => Scaffold(
          appBar: LockerAppBar(
            title: "Favourites",
            showAddLockerItemButton: false,
            searchController: controller.searchController,
            clearSearchText: controller.clearSearchText,
            selectionMode: controller.selectedItems.isNotEmpty,
            selectedItems: controller.selectedItems.length,
            selectAllItems: controller.selectAllItems,
            exitSelectionMode: () {
              controller.emptySelectedItems();
            },
            unmarkFavourite:
                () => DialogHelper.confirmDialog(
                  title:
                      "Are you sure you want to unmark these items as favourites?",
                  onConfirm: controller.unmarkFavourite,
                ),
            deleteItems:
                () => DialogHelper.confirmDialog(
                  title: "Are you sure you want to delete these items?",
                  confirmBtnBgColor: Colors.redAccent,
                  onConfirm: controller.deleteItems,
                ),
          ),
          drawer: LockerDrawer(page: "favourites"),
          body: Column(
            children: [
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async {
                    await Future.delayed(const Duration(seconds: 3));

                    await LockerItemsStore.to.fetchItems();
                  },
                  color: AppColors.primaryColor,
                  backgroundColor: Get.isDarkMode ? null : Colors.white,
                  child: LockerItems(
                    selectedItems: controller.selectedItems,
                    items:
                        controller.searching.isTrue
                            ? controller.searchedItems
                            : LockerItemsStore.to.favourites,
                    onSelected: controller.addItemToSelectedItems,
                    onUnSelected: controller.removeItemFromSelectedItems,
                  ).withEmptyState(
                    isEmpty:
                        controller.searching.isTrue
                            ? controller.searchedItems.isEmpty
                            : LockerItemsStore.to.favourites.isEmpty,
                    emptyState:
                        controller.searching.isTrue
                            ? EmptyStatePresets.noSearchResults(
                              searchTerm: controller.searchController.text,
                            )
                            : EmptyState(
                              icon: Icons.star_outline,
                              title: 'No Favorites',
                              description:
                                  'You haven\'t marked any items as favorites yet. Star items to find them quickly.',
                              actionText: 'Explore Items',
                              onActionPressed: () {
                                // Navigate to home screen to explore items
                                Get.offAllNamed('/home');
                              },
                              color:
                                  AppColors.primaryColor, // Using primary color
                            ),
                  ),
                ),
              ),
              // AdMob Banner Ad at the bottom
              Container(
                color: Get.isDarkMode ? Colors.grey[900] : Colors.grey[50],
                child: SafeArea(
                  top: false,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: BannerAdWidget(useTestAds: kDebugMode),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
