import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';

/// A beautiful, reusable empty state component for the IronLocker app
class EmptyState extends StatelessWidget {
  const EmptyState({
    super.key,
    required this.icon,
    required this.title,
    required this.description,
    this.actionText,
    this.onActionPressed,
    this.color,
    this.showBackground = true,
    this.size = EmptyStateSize.medium,
    this.illustration,
  });

  /// The icon to display
  final IconData icon;

  /// The main title text
  final String title;

  /// The description text
  final String description;

  /// Optional action button text
  final String? actionText;

  /// Optional action button callback
  final VoidCallback? onActionPressed;

  /// Optional custom color (defaults to blue02)
  final Color? color;

  /// Whether to show the background container
  final bool showBackground;

  /// The size variant of the empty state
  final EmptyStateSize size;

  /// Optional custom illustration widget
  final Widget? illustration;

  @override
  Widget build(BuildContext context) {
    final effectiveColor = color ?? IronLockerColors.blue02;

    return Container(
      width: double.infinity,
      padding: _getPadding(),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Illustration or Icon
          _buildIllustration(effectiveColor),

          SizedBox(height: _getSpacing() * 1.5),

          // Title
          Text(
            title,
            style: TextStyle(
              fontSize: _getTitleFontSize(),
              fontWeight: FontWeight.w600,
              color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
              fontFamily: 'Jura',
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: _getSpacing() * 0.5),

          // Description
          Text(
            description,
            style: TextStyle(
              fontSize: _getDescriptionFontSize(),
              color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
              fontFamily: 'Jura',
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),

          // Action Button
          if (actionText != null && onActionPressed != null) ...[
            SizedBox(height: _getSpacing() * 2),
            _buildActionButton(effectiveColor),
          ],
        ],
      ),
    );
  }

  Widget _buildIllustration(Color effectiveColor) {
    if (illustration != null) {
      return illustration!;
    }

    return Container(
      width: _getIconSize() * 2,
      height: _getIconSize() * 2,
      decoration: BoxDecoration(
        color:
            showBackground
                ? effectiveColor.withValues(alpha: 0.1)
                : Colors.transparent,
        borderRadius: BorderRadius.circular(_getIconSize() / 4),
        border:
            showBackground
                ? Border.all(
                  color: effectiveColor.withValues(alpha: 0.2),
                  width: 1,
                )
                : null,
      ),
      child: Icon(
        icon,
        size: _getIconSize(),
        color: effectiveColor.withValues(alpha: 0.7),
      ),
    );
  }

  Widget _buildActionButton(Color effectiveColor) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: effectiveColor.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Container(
        decoration: BoxDecoration(
          color: effectiveColor,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onActionPressed,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: _getSpacing() * 1.5,
                vertical: _getSpacing(),
              ),
              child: Text(
                actionText!,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  fontFamily: 'Jura',
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  EdgeInsets _getPadding() {
    switch (size) {
      case EmptyStateSize.small:
        return const EdgeInsets.all(16);
      case EmptyStateSize.medium:
        return const EdgeInsets.all(24);
      case EmptyStateSize.large:
        return const EdgeInsets.all(32);
    }
  }

  double _getIconSize() {
    switch (size) {
      case EmptyStateSize.small:
        return 32;
      case EmptyStateSize.medium:
        return 48;
      case EmptyStateSize.large:
        return 64;
    }
  }

  double _getTitleFontSize() {
    switch (size) {
      case EmptyStateSize.small:
        return 16;
      case EmptyStateSize.medium:
        return 18;
      case EmptyStateSize.large:
        return 22;
    }
  }

  double _getDescriptionFontSize() {
    switch (size) {
      case EmptyStateSize.small:
        return 13;
      case EmptyStateSize.medium:
        return 14;
      case EmptyStateSize.large:
        return 15;
    }
  }

  double _getSpacing() {
    switch (size) {
      case EmptyStateSize.small:
        return 12;
      case EmptyStateSize.medium:
        return 16;
      case EmptyStateSize.large:
        return 20;
    }
  }
}

/// Size variants for the empty state component
enum EmptyStateSize { small, medium, large }

/// Predefined empty state configurations for common use cases
class EmptyStatePresets {
  static EmptyState noSharedLockers({VoidCallback? onCreatePressed}) {
    return EmptyState(
      icon: Icons.folder_shared_outlined,
      title: 'No Shared Lockers',
      description:
          'You haven\'t created or joined any shared lockers yet. Create one to start collaborating securely.',
      actionText: onCreatePressed != null ? 'Create Shared Locker' : null,
      onActionPressed: onCreatePressed,
      color: IronLockerColors.blue02,
    );
  }

  static EmptyState noContacts({VoidCallback? onAddPressed}) {
    return EmptyState(
      icon: Icons.contacts_outlined,
      title: 'No Contacts',
      description:
          'You haven\'t added any contacts yet. Add your first contact to get started.',
      actionText: onAddPressed != null ? 'Add Contact' : null,
      onActionPressed: onAddPressed,
      color: IronLockerColors.blue02,
    );
  }

  static EmptyState noNotes({VoidCallback? onCreatePressed}) {
    return EmptyState(
      icon: Icons.note_outlined,
      title: 'No Notes',
      description:
          'You haven\'t created any notes yet. Add your first note to keep track of important information.',
      actionText: onCreatePressed != null ? 'Create Note' : null,
      onActionPressed: onCreatePressed,
      color: IronLockerColors.blue02,
    );
  }

  static EmptyState noAccounts({VoidCallback? onAddPressed}) {
    return EmptyState(
      icon: Icons.account_circle_outlined,
      title: 'No Accounts',
      description:
          'You haven\'t added any accounts yet. Store your login credentials securely.',
      actionText: onAddPressed != null ? 'Add Account' : null,
      onActionPressed: onAddPressed,
      color: IronLockerColors.blue02,
    );
  }

  static EmptyState noPaymentCards({VoidCallback? onAddPressed}) {
    return EmptyState(
      icon: Icons.credit_card_outlined,
      title: 'No Payment Cards',
      description:
          'You haven\'t added any payment cards yet. Store your card information securely.',
      actionText: onAddPressed != null ? 'Add Card' : null,
      onActionPressed: onAddPressed,
      color: IronLockerColors.blue02,
    );
  }

  static EmptyState noSearchResults({required String searchTerm}) {
    return EmptyState(
      icon: Icons.search_off_outlined,
      title: 'No Results Found',
      description:
          'We couldn\'t find anything matching "$searchTerm". Try adjusting your search terms.',
      color: IronLockerColors.blueGray,
      showBackground: false,
      size: EmptyStateSize.small,
    );
  }

  static EmptyState noFavorites({VoidCallback? onExplorePressed}) {
    return EmptyState(
      icon: Icons.star_outline,
      title: 'No Favorites',
      description:
          'You haven\'t marked any items as favorites yet. Star items to find them quickly.',
      actionText: onExplorePressed != null ? 'Explore Items' : null,
      onActionPressed: onExplorePressed,
      color: IronLockerColors.yellow,
    );
  }

  static EmptyState noMembers() {
    return EmptyState(
      icon: Icons.group_outlined,
      title: 'No Members Yet',
      description:
          'This shared locker doesn\'t have any members. Add members to start collaborating.',
      color: IronLockerColors.blue02,
      size: EmptyStateSize.medium,
    );
  }

  static EmptyState connectionError({VoidCallback? onRetryPressed}) {
    return EmptyState(
      icon: Icons.wifi_off_outlined,
      title: 'Connection Error',
      description:
          'Unable to load data. Please check your internet connection and try again.',
      actionText: onRetryPressed != null ? 'Retry' : null,
      onActionPressed: onRetryPressed,
      color: IronLockerColors.error,
    );
  }
}

/// Custom illustrations for empty states
class EmptyStateIllustrations {
  static Widget lockerIllustration({required Color color, double size = 120}) {
    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Background circle
          Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: color.withValues(alpha: 0.1),
              border: Border.all(color: color.withValues(alpha: 0.2), width: 2),
            ),
          ),
          // Lock icon
          Container(
            width: size * 0.4,
            height: size * 0.4,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(Icons.lock_outline, size: size * 0.25, color: color),
          ),
          // Floating dots
          Positioned(
            top: size * 0.2,
            right: size * 0.25,
            child: Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: color.withValues(alpha: 0.6),
              ),
            ),
          ),
          Positioned(
            bottom: size * 0.25,
            left: size * 0.2,
            child: Container(
              width: 6,
              height: 6,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: color.withValues(alpha: 0.4),
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Widget collaborationIllustration({
    required Color color,
    double size = 120,
  }) {
    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Background
          Container(
            width: size,
            height: size * 0.8,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: color.withValues(alpha: 0.2), width: 2),
            ),
          ),
          // People icons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildPersonIcon(color, size * 0.15),
              _buildPersonIcon(color, size * 0.15),
              _buildPersonIcon(color.withValues(alpha: 0.5), size * 0.15),
            ],
          ),
        ],
      ),
    );
  }

  static Widget _buildPersonIcon(Color color, double size) {
    return Container(
      width: size * 2,
      height: size * 2,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color.withValues(alpha: 0.2),
      ),
      child: Icon(Icons.person_outline, size: size, color: color),
    );
  }
}

/// Extension methods for easier empty state usage
extension EmptyStateExtensions on Widget {
  Widget withEmptyState({
    required bool isEmpty,
    required EmptyState emptyState,
  }) {
    return isEmpty ? emptyState : this;
  }
}
