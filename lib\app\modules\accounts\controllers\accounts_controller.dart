import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/get_folder_items.dart';
import 'package:ironlocker/app/helpers/helpers.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/locker.dart';
import 'package:ironlocker/app/stores/locker_items.dart';

class AccountsController extends GetxController {
  final searchedItems = [].obs;
  final selectedItems = [].obs;
  final searching = false.obs;
  final searchController = TextEditingController();

  final lockerService = Get.find<LockerService>();

  @override
  void onInit() {
    searchController.addListener(() {
      if (searchController.text.isEmpty) {
        searching(false);
        return;
      }

      if (searching.isFalse) {
        searching(true);
      }

      searchedItems.value =
          LockerItemsStore.to.accounts.where((item) {
            String itemName = item['name'];

            return itemName.toLowerCase().contains(
              searchController.text.toLowerCase(),
            );
          }).toList();
    });
    super.onInit();
  }

  emptySelectedItems() {
    selectedItems([]);
  }

  addItemToSelectedItems(item) {
    selectedItems.add(item);
  }

  removeItemFromSelectedItems(id) {
    selectedItems.removeWhere((item) => item == id);
  }

  clearSearchText() {
    searchController.clear();
  }

  selectAllItems() {
    var items =
        LockerItemsStore.to.accounts
            .map((lockerItem) => lockerItem['_id'])
            .toList();
    selectedItems(items);
  }

  deleteItems() async {
    Get.back();
    DialogHelper.loading();

    final resp = await lockerService.deleteItems(items: selectedItems);

    Get.back();

    if (resp.body?["error"] == "SESSION_LOCKED") {
      Get.offNamed(Routes.lockedLocker);
      return;
    } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
        resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
      await Helpers.clearData();
      Get.offAllNamed(Routes.signin);
      return;
    }

    if (resp.hasError) {
      ToastHelper.error("Something went wrong");
      return;
    }

    List itemsToDelete = [...selectedItems];

    for (final item in selectedItems) {
      final folderItems = getFolderItems(
        items: LockerItemsStore.to.lockerItems,
        folderId: item,
      );

      itemsToDelete.addAll(folderItems);
    }

    LockerItemsStore.to.removeLockerItems(itemsToDelete);

    selectedItems([]);
    ToastHelper.success("Items deleted");
  }

  markFavourite() async {
    Get.back();
    DialogHelper.loading();

    final resp = await lockerService.markFavourite(items: selectedItems);

    Get.back();

    if (resp.body?["error"] == "SESSION_LOCKED") {
      Get.offNamed(Routes.lockedLocker);
      return;
    } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
        resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
      await Helpers.clearData();
      Get.offAllNamed(Routes.signin);
      return;
    }

    if (resp.hasError) {
      ToastHelper.error("Something went wrong");
      return;
    }

    LockerItemsStore.to.markFavourite(selectedItems);

    emptySelectedItems();
  }

  unmarkFavourite() async {
    Get.back();
    DialogHelper.loading();

    final resp = await lockerService.unmarkFavourite(items: selectedItems);

    Get.back();

    if (resp.body?["error"] == "SESSION_LOCKED") {
      Get.offNamed(Routes.lockedLocker);
      return;
    } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
        resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
      await Helpers.clearData();
      Get.offAllNamed(Routes.signin);
      return;
    }

    if (resp.hasError) {
      ToastHelper.error("Something went wrong");
      return;
    }

    LockerItemsStore.to.unmarkFavourite(selectedItems);

    emptySelectedItems();
  }
}
