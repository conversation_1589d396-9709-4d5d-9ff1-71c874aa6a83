import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:ironlocker/app/services/subscription_service.dart';
import 'package:ironlocker/app/helpers/toast.dart';

/// Widget that displays subscription options and status
class SubscriptionWidget extends StatelessWidget {
  final bool showAsCard;

  const SubscriptionWidget({super.key, this.showAsCard = true});

  @override
  Widget build(BuildContext context) {
    return Obx(() => _buildWrapper());
  }

  Widget _buildWrapper() {
    if (showAsCard) {
      return Card(
        margin: const EdgeInsets.all(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: _buildContent(),
        ),
      );
    }

    return Container(padding: const EdgeInsets.all(16), child: _buildContent());
  }

  Widget _buildContent() {
    // Always show subscription options since backend manages status
    return _buildSubscriptionOptionsContent();
  }

  Widget _buildSubscriptionOptionsContent() {
    final offering = SubscriptionService.to.currentOffering;

    if (offering == null) {
      return _buildLoadingOrError();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.star_border,
              color: Get.isDarkMode ? Colors.blue[300] : Colors.blue,
              size: showAsCard ? 24 : 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                "Buy Premium",
                style: TextStyle(
                  fontSize: showAsCard ? 18 : 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          "Purchase premium subscription to unlock unlimited shared lockers",
          style: TextStyle(
            fontSize: showAsCard ? 14 : 12,
            color: Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
        ),
        const SizedBox(height: 16),

        // Subscription packages
        ...offering.availablePackages.map(
          (package) => _buildPackageOption(package),
        ),

        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: _restorePurchases,
                child: Text(
                  SubscriptionService.to.isLoading
                      ? "Restoring..."
                      : "Restore Purchases",
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPackageOption(Package package) {
    final isPopular = package.packageType == PackageType.annual;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _purchasePackage(package),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(
                color:
                    isPopular
                        ? Colors.blue
                        : (Get.isDarkMode
                            ? Colors.grey[700]!
                            : Colors.grey[300]!),
                width: isPopular ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
              color:
                  isPopular
                      ? (Get.isDarkMode
                          ? Colors.blue.withOpacity(0.1)
                          : Colors.blue.withOpacity(0.05))
                      : null,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            SubscriptionService.to.getSubscriptionPeriod(
                              package,
                            ),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: isPopular ? Colors.blue : null,
                            ),
                          ),
                          if (isPopular) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.blue,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                "POPULAR",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getPackageDescription(package),
                        style: TextStyle(
                          fontSize: 12,
                          color:
                              Get.isDarkMode
                                  ? Colors.grey[400]
                                  : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      SubscriptionService.to.getFormattedPrice(package),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (package.packageType == PackageType.annual) ...[
                      Text(
                        "Save 30%",
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.green[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingOrError() {
    if (SubscriptionService.to.isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Column(
      children: [
        const Icon(Icons.error_outline, size: 48, color: Colors.grey),
        const SizedBox(height: 16),
        const Text(
          "Unable to load subscription options",
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
        const SizedBox(height: 16),
        ElevatedButton(
          onPressed: () => SubscriptionService.to.loadOfferings(),
          child: const Text("Retry"),
        ),
      ],
    );
  }

  String _getPackageDescription(Package package) {
    switch (package.packageType) {
      case PackageType.monthly:
        return "Billed monthly";
      case PackageType.annual:
        return "Billed yearly • Best value";
      case PackageType.sixMonth:
        return "Billed every 6 months";
      case PackageType.threeMonth:
        return "Billed every 3 months";
      default:
        return "Subscription";
    }
  }

  Future<void> _purchasePackage(Package package) async {
    if (SubscriptionService.to.isLoading) return;

    final success = await SubscriptionService.to.purchasePackage(package);

    if (success) {
      ToastHelper.success(
        "Welcome to Premium! Enjoy unlimited shared lockers.",
      );
    } else {
      ToastHelper.error("Purchase failed. Please try again.");
    }
  }

  Future<void> _restorePurchases() async {
    if (SubscriptionService.to.isLoading) return;

    final success = await SubscriptionService.to.restorePurchases();

    if (success) {
      ToastHelper.success("Purchases restored successfully!");
    } else {
      ToastHelper.success("No purchases found to restore.");
    }
  }
}
