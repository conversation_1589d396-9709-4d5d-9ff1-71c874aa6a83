import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/storage.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/mixins/analytics_mixin.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/auth.dart';
import 'package:ironlocker/app/services/subscription_service.dart';
import 'package:ironlocker/app/services/user.dart';
import 'package:ironlocker/app/stores/user.dart';

class Complete2faPinController extends GetxController with AnalyticsMixin {
  var pinController = TextEditingController();
  var loading = false.obs;
  final authService = AuthService();
  var pinCompleted = false.obs;
  final userService = Get.find<UserService>();

  @override
  onInit() {
    pinController.addListener(() {
      pinCompleted.value = pinController.text.length == 4;
    });

    super.onInit();
  }

  @override
  void onClose() {
    pinController.dispose();
    super.onReady();
  }

  complete2fa() async {
    if (pinController.text.length < 4) {
      ToastHelper.error("Enter the complete code");
      return;
    }
    loading(true);

    final resp = await authService.complete2fa({
      'method': "pin",
      "complete2faToken": Get.parameters['complete2faToken'],
      "pin": pinController.text,
    });

    if (resp.hasError) {
      loading(false);

      ToastHelper.error(
        resp.body != null ? resp.body['error'] : "Something went wrong",
      );
      return;
    }

    final storage = Storage();

    await storage.setItem(key: "accessToken", value: resp.body['accessToken']);
    await storage.setItem(
      key: "refreshToken",
      value: resp.body['refreshToken'],
    );

    UserStore.to.refreshToken(resp.body['refreshToken']);
    UserStore.to.accessToken(resp.body['accessToken']);

    final userResp = await userService.getProfile();

    if (userResp.hasError) {
      loading(true);
      ToastHelper.error("Something went wrong");
      return;
    }

    UserStore.to.name(userResp.body['name']);
    UserStore.to.email(userResp.body['email']);
    UserStore.to.id(userResp.body['_id']);
    UserStore.to.twoFactorEnabled(userResp.body['twoFactorEnabled']);
    UserStore.to.sessionId(userResp.body['sessionId']);
    UserStore.to.plan(userResp.body['plan']);

    try {
      await analytics.trackLogin(method: 'email');
      await analytics.setUserProperties(
        userId: userResp.body['_id'] ?? '',
        email: userResp.body['email'],
        name: userResp.body['name'],
        twoFactorEnabled: userResp.body['twoFactorEnabled'] ?? false,
      );
    } catch (e) {
      debugPrint('Analytics error during login: $e');
    }

    try {
      await SubscriptionService.to.identifyUser(userResp.body['_id']);
    } catch (e) {
      debugPrint('RevenueCat identification error during login: $e');
    }

    Get.offAllNamed(Routes.VERIFY_ENCRYPTION_KEY);
  }
}
