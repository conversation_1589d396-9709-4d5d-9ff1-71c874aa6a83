import 'package:flutter/material.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:gap/gap.dart';

class DropDownField extends StatelessWidget {
  const DropDownField({
    super.key,
    required this.controller,
    this.labelText,
    this.items,
    this.selectedItem,
  });
  final TextEditingController controller;
  final String? labelText;
  final List? items;
  final dynamic selectedItem;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (labelText != null)
          Text(labelText!, style: Theme.of(context).textTheme.labelMedium),
        const Gap(5),
        DropdownSearch(
            items: (f, cs) => items!,
            itemAsString: (item) => item['name'],
            compareFn: (a, b){
              return a["_id"] == b["_id"];
            },
            decoratorProps: DropDownDecoratorProps(
              baseStyle: const TextStyle(fontSize: 14),
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.symmetric(
                  vertical: 15,
                ),
                prefix: const Padding(
                  padding: EdgeInsets.only(left: 10),
                ),
                suffix: const Padding(
                  padding: EdgeInsets.only(right: 10),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(7),
                  borderSide: BorderSide(
                    color: Colors.grey[300]!,
                    width: 2,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(7),
                  borderSide: const BorderSide(
                    color: Colors.blueAccent,
                    width: 2,
                  ),
                ),
              ),
            ),
            popupProps: PopupProps.menu(
                searchFieldProps: TextFieldProps(
                  cursorColor: Colors.blueAccent,
                  style: const TextStyle(fontSize: 14),
                  padding: EdgeInsets.zero,
                  decoration: InputDecoration(
                    hintText: "Search...",
                    hintStyle: const TextStyle(fontSize: 14),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(7),
                      borderSide: BorderSide(
                        color: Colors.grey[700]!,
                        width: 1,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(7),
                      borderSide: const BorderSide(
                        color: Colors.blueAccent,
                        width: 2,
                      ),
                    ),
                  ),
                ),
                searchDelay: Duration.zero,
                itemBuilder: (context, item, isDisabled, isSelected) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Text(
                      item is Map<String, dynamic> ? item["name"] : '',
                      style: const TextStyle(
                        fontSize: 16,
                        height: 2,
                      ),
                    ),
                  );
                },
                constraints: const BoxConstraints(maxHeight: 100)),
            selectedItem: selectedItem,
            onChanged: (val) {
              controller.text = val["_id"];
            })
      ],
    );
  }
}
