import 'package:get/get.dart';
import 'package:ironlocker/app/services/user.dart';

class ScanQrcodeController extends GetxController {
  var base64String = "";
  var secret = "";

  var loading = true.obs;
  final userService = Get.find<UserService>();

  @override
  void onInit() async {
    final resp =
        await userService.get2faSecret(password: Get.parameters['password']!);

    if (resp.hasError) {
      Get.back();
      return;
    }
    base64String = resp.body['qrcode'];
    secret = resp.body['secret'];

    loading(false);
    super.onInit();
  }
}
