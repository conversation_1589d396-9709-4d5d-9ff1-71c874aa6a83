import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/bottom%20sheets/add_locker_item.dart';
import 'package:ironlocker/app/components/bottom%20sheets/address_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/baccount_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/contact_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/folder_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/account_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/note_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/pcard_form.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/get_folder_items.dart';
import 'package:ironlocker/app/helpers/helpers.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/locker.dart';
import 'package:ironlocker/app/stores/locker_items.dart';
import 'package:ironlocker/app/mixins/analytics_mixin.dart';
import 'package:ironlocker/app/services/analytics_service.dart';

class HomeController extends GetxController with AnalyticsMixin {
  final selectedItems = [].obs;
  final searchedItems = [].obs;
  final searching = false.obs;
  final searchController = TextEditingController();

  final lockerService = Get.find<LockerService>();

  /// Log app open event
  Future<void> logAppOpen() async {
    try {
      await Get.find<AnalyticsService>().logAppOpen();
    } catch (e) {
      // Silently handle analytics errors in production
    }
  }

  @override
  void onInit() {
    super.onInit();
    trackScreenView('HomeScreen');

    // Log app open event
    logAppOpen();

    searchController.addListener(() {
      if (searchController.text.isEmpty) {
        searching(false);
        return;
      }

      if (searching.isFalse) {
        searching(true);
        // Track search action
        trackAction('search_initiated', category: 'locker_search');
      }

      searchedItems.value =
          LockerItemsStore.to.lockerItems.where((item) {
            String itemName = item['name'];

            return itemName.toLowerCase().contains(
              searchController.text.toLowerCase(),
            );
          }).toList();

      // Track search results
      trackAction(
        'search_performed',
        category: 'locker_search',
        value: searchedItems.length,
        parameters: {
          'search_term_length': searchController.text.length,
          'results_count': searchedItems.length,
        },
      );
    });
  }

  static addLockerItem(BuildContext context) {
    // Track add item action
    try {
      AnalyticsService.to.trackUserEngagement(
        action: 'add_item_sheet_opened',
        category: 'locker_management',
      );
    } catch (e) {
      // Analytics service might not be initialized yet
    }

    addLockerItemSheet(
      context: context,
      onPress: (itemType) {
        Navigator.of(context).pop();

        // Track item type selection
        try {
          AnalyticsService.to.trackLockerAction(
            action: 'create_item_initiated',
            itemType: itemType.toLowerCase(),
          );
        } catch (e) {
          // Analytics service might not be initialized yet
        }

        switch (itemType) {
          case "Note":
            noteFormSheet(context: context);
            break;
          case "Contact":
            contactFormSheet(context: context);
            break;
          case "Address":
            addressFormSheet(context: context);
            break;
          case "Account":
            accountFormSheet(context: context);
            break;
          case "Folder":
            folderFormSheet(context: context);
            break;
          case "PaymentCard":
            paymentCardFormSheet(context: context);
            break;
          default:
            bankAccountFormSheet(context: context);
        }
      },
    );
  }

  emptySelectedItems() {
    selectedItems([]);
  }

  addItemToSelectedItems(item) {
    selectedItems.add(item);
  }

  removeItemFromSelectedItems(id) {
    selectedItems.removeWhere((item) => item == id);
  }

  clearSearchText() {
    searchController.clear();
  }

  closeApp() async {
    await SystemNavigator.pop();
  }

  selectAllItems() {
    var items =
        LockerItemsStore.to.lockerItems
            .map((lockerItem) => lockerItem['_id'])
            .toList();
    selectedItems(items);
  }

  deleteItems() async {
    // Track delete action
    trackLockerOperation(
      'delete',
      'multiple',
      itemCount: selectedItems.length,
      parameters: {'bulk_delete': true},
    );

    Get.back();
    DialogHelper.loading();

    final resp = await lockerService.deleteItems(items: selectedItems);
    Get.back();

    if (resp.body?["error"] == "SESSION_LOCKED") {
      Get.offNamed(Routes.lockedLocker);
      return;
    } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
        resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
      await Helpers.clearData();
      Get.offAllNamed(Routes.signin);
      return;
    }

    if (resp.hasError) {
      trackError('delete_items_failed', context: 'HomeController.deleteItems');
      ToastHelper.error("Something went wrong");
      return;
    }

    List itemsToDelete = [...selectedItems];

    for (final item in selectedItems) {
      final folderItems = getFolderItems(
        items: LockerItemsStore.to.lockerItems,
        folderId: item,
      );

      itemsToDelete.addAll(folderItems);
    }

    LockerItemsStore.to.removeLockerItems(itemsToDelete);

    // Track successful deletion
    trackAction(
      'items_deleted_success',
      category: 'locker_management',
      value: itemsToDelete.length,
      parameters: {
        'selected_count': selectedItems.length,
        'total_deleted': itemsToDelete.length,
      },
    );

    selectedItems([]);
    ToastHelper.success("Items deleted");
  }

  markFavourite() async {
    Get.back();
    DialogHelper.loading();

    final resp = await lockerService.markFavourite(items: selectedItems);

    Get.back();

    if (resp.body?["error"] == "SESSION_LOCKED") {
      Get.offNamed(Routes.lockedLocker);
      return;
    } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
        resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
      await Helpers.clearData();
      Get.offAllNamed(Routes.signin);
      return;
    }

    if (resp.hasError) {
      ToastHelper.error("Something went wrong");
      return;
    }

    LockerItemsStore.to.markFavourite(selectedItems);

    emptySelectedItems();
  }

  unmarkFavourite() async {
    Get.back();
    DialogHelper.loading();

    final resp = await lockerService.unmarkFavourite(items: selectedItems);

    Get.back();

    if (resp.body?["error"] == "SESSION_LOCKED") {
      Get.offNamed(Routes.lockedLocker);
      return;
    } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
        resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
      await Helpers.clearData();
      Get.offAllNamed(Routes.signin);
      return;
    }

    if (resp.hasError) {
      ToastHelper.error("Something went wrong");
      return;
    }

    LockerItemsStore.to.unmarkFavourite(selectedItems);

    emptySelectedItems();
  }
}
