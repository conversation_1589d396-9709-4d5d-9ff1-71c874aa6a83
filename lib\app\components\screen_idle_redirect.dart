import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/storage.dart';

import '../routes/app_pages.dart';

class AutoRedirectWidget extends StatefulWidget {
  final Widget child;

  const AutoRedirectWidget({super.key, required this.child});

  @override
  AutoRedirectWidgetState createState() => AutoRedirectWidgetState();
}

class AutoRedirectWidgetState extends State<AutoRedirectWidget> {
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    startTimer();
  }

  void startTimer() {
    _timer = Timer(const Duration(minutes: 5), () async {
      final storage = Storage();
      await storage.setItem(key: "lockerLocked", value: true);
      Get.offAllNamed(Routes.lockedLocker);
    });
  }

  void _resetTimer() {
    _timer?.cancel();

    _timer = Timer(const Duration(minutes: 5), () async {
      final storage = Storage();
      await storage.setItem(key: "lockerLocked", value: true);
      Get.offAllNamed(Routes.lockedLocker);
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: (ev) {
        _resetTimer();
      },
      child: widget.child,
    );
  }
}
