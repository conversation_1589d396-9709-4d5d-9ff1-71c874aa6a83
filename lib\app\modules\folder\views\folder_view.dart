import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/banner_add.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/components/locker_item_icon.dart';
import 'package:ironlocker/app/components/locker_items.dart';
import 'package:ironlocker/app/components/something_went_wrong.dart';

import 'package:ironlocker/app/constants/colors.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/modules/folder/controllers/folder_controller.dart';
import 'package:ironlocker/app/stores/locker_items.dart';
import 'package:ironlocker/app/themes/app_bar.dart';

class FolderPage extends GetView<FolderController> {
  const FolderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: controller.selectedItems.isEmpty,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop && controller.selectedItems.isNotEmpty) {
          controller.emptySelectedItems();
        }
      },
      child: Obx(
        () => Scaffold(
          appBar: LockerAppBar(
            title: controller.folderName(),
            leading: AppbarTheme.buildStyledBackButton(
              onPressed: () => Get.back(),
              tooltip: 'Back',
            ),
            showAddLockerItemButton: false, // Move to footer
            searchController: controller.searchController,
            clearSearchText: controller.clearSearchText,
            selectionMode: controller.selectedItems.isNotEmpty,
            selectedItems: controller.selectedItems.length,
            selectAllItems: controller.selectAllItems,
            exitSelectionMode: () {
              controller.emptySelectedItems();
            },
            unmarkFavourite:
                () => DialogHelper.confirmDialog(
                  title:
                      "Are you sure you want to unmark these items as favourites?",
                  onConfirm: controller.unmarkFavourite,
                ),
            markFavourite:
                () => DialogHelper.confirmDialog(
                  title:
                      "Are you sure you want to mark these items as favourites?",
                  onConfirm: controller.markFavourite,
                ),
            deleteItems:
                () => DialogHelper.confirmDialog(
                  title: "Are you sure you want to delete these items?",
                  confirmBtnBgColor: Colors.redAccent,
                  onConfirm: controller.deleteItems,
                ),
          ),
          body: Column(
            children: [
              Expanded(
                child:
                    controller.loading.isTrue
                        ? const Center(
                          child: SizedBox(
                            height: 50,
                            width: 50,
                            child: CircularProgressIndicator(
                              color: AppColors.primaryColor,
                            ),
                          ),
                        )
                        : controller.error.isNotEmpty
                        ? SomethingWentWrong(retry: controller.fetchFolderItems)
                        : RefreshIndicator(
                          onRefresh: () async {
                            await Future.delayed(const Duration(seconds: 3));

                            await LockerItemsStore.to.fetchItems();
                          },
                          color: AppColors.primaryColor,
                          backgroundColor: Get.isDarkMode ? null : Colors.white,
                          child:
                              controller.folderItems.isNotEmpty
                                  ? LockerItems(
                                    selectedItems: controller.selectedItems,
                                    items:
                                        controller.searching.isTrue
                                            ? controller.searchedItems
                                            : controller.folderItems,
                                    onSelected:
                                        controller.addItemToSelectedItems,
                                    onUnSelected:
                                        controller.removeItemFromSelectedItems,
                                  )
                                  : Container(
                                    padding: const EdgeInsets.all(20),
                                    alignment: Alignment.topCenter,
                                    child: const Column(
                                      children: [
                                        LockerItemIcon(
                                          itemType: "Folder",
                                          size: 42,
                                        ),
                                        Gap(10),
                                        Text("No items in this folder"),
                                      ],
                                    ),
                                  ),
                        ),
              ),
              // Footer with Add Button
              _buildFooter(context, controller),
              // AdMob Banner Ad at the bottom
              Container(
                decoration: BoxDecoration(
                  color: Get.isDarkMode ? Colors.grey[850] : Colors.grey[100],
                  border: Border(
                    top: BorderSide(
                      color:
                          Get.isDarkMode
                              ? Colors.grey[700]!
                              : Colors.grey[300]!,
                      width: 0.5,
                    ),
                  ),
                ),
                child: SafeArea(
                  top: false,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 6.0),
                    child: BannerAdWidget(useTestAds: kDebugMode),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFooter(BuildContext context, FolderController controller) {
    // Hide footer when in selection mode
    if (controller.selectedItems.isNotEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[900] : Colors.white,
        border: Border(
          top: BorderSide(
            color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
            width: 0.5,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(20, 16, 20, 12),
        child: SafeArea(
          top: false,
          child: SizedBox(
            width: double.infinity,
            height: 52,
            child: ElevatedButton.icon(
              onPressed: () => controller.addLockerItem(context),
              icon: const Icon(Icons.add_rounded, size: 22),
              label: const Text(
                'Add Item',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Jura',
                  letterSpacing: 0.5,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: IronLockerColors.blue02,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                elevation: 3,
                shadowColor: IronLockerColors.blue02.withValues(alpha: 0.4),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
