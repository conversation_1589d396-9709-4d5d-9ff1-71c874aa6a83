import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/controllers/websocket_controller.dart';

/// Widget that displays WebSocket connection status
class WebSocketStatusWidget extends StatelessWidget {
  final bool showText;
  final double iconSize;
  final EdgeInsets padding;

  const WebSocketStatusWidget({
    super.key,
    this.showText = true,
    this.iconSize = 16.0,
    this.padding = const EdgeInsets.all(8.0),
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<WebSocketController>(
      builder: (controller) {
        return Obx(() {
          final status = controller.connectionStatus.value;
          final isConnected = controller.isConnected.value;
          final isConnecting = controller.isConnecting.value;

          Color statusColor;
          IconData statusIcon;
          String statusText;

          switch (status) {
            case 'connected':
              statusColor = Colors.green;
              statusIcon = Icons.wifi;
              statusText = 'Connected';
              break;
            case 'auth_failed':
              statusColor = Colors.red;
              statusIcon = Icons.security;
              statusText = 'Authentication Failed';
              break;
            case 'connecting':
            case 'disconnected':
            case 'error':
            case 'failed':
            default:
              statusColor = Colors.orange;
              statusIcon = Icons.wifi_off;
              statusText = 'Connecting...';
              break;
          }

          return Container(
            padding: padding,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (isConnecting)
                  SizedBox(
                    width: iconSize,
                    height: iconSize,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.0,
                      valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                    ),
                  )
                else
                  Icon(statusIcon, color: statusColor, size: iconSize),
                if (showText) ...[
                  const SizedBox(width: 8.0),
                  Text(
                    statusText,
                    style: TextStyle(
                      color: statusColor,
                      fontSize: 12.0,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
          );
        });
      },
    );
  }
}

/// Compact WebSocket status indicator for app bars
class WebSocketStatusIndicator extends StatelessWidget {
  const WebSocketStatusIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return const WebSocketStatusWidget(
      showText: false,
      iconSize: 20.0,
      padding: EdgeInsets.symmetric(horizontal: 8.0),
    );
  }
}

/// WebSocket status card for settings or debug screens
class WebSocketStatusCard extends StatelessWidget {
  const WebSocketStatusCard({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<WebSocketController>(
      builder: (controller) {
        return Obx(() {
          final status = controller.connectionStatus.value;
          final isConnected = controller.isConnected.value;
          final isConnecting = controller.isConnecting.value;

          return Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.wifi, size: 24.0),
                      const SizedBox(width: 8.0),
                      const Text(
                        'WebSocket Connection',
                        style: TextStyle(
                          fontSize: 16.0,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      WebSocketStatusWidget(
                        showText: false,
                        iconSize: 20.0,
                        padding: EdgeInsets.zero,
                      ),
                    ],
                  ),
                  const SizedBox(height: 12.0),
                  Row(
                    children: [
                      const Text('Status: '),
                      WebSocketStatusWidget(
                        iconSize: 16.0,
                        padding: EdgeInsets.zero,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8.0),
                  if (!isConnected && !isConnecting) ...[
                    const SizedBox(height: 8.0),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () => controller.reconnect(),
                        icon: const Icon(Icons.refresh),
                        label: const Text('Reconnect'),
                      ),
                    ),
                  ],
                  if (status == 'auth_failed') ...[
                    const SizedBox(height: 8.0),
                    Container(
                      padding: const EdgeInsets.all(8.0),
                      decoration: BoxDecoration(
                        color: Colors.red.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4.0),
                        border: Border.all(color: Colors.red.withOpacity(0.3)),
                      ),
                      child: const Row(
                        children: [
                          Icon(Icons.warning, color: Colors.red, size: 16.0),
                          SizedBox(width: 8.0),
                          Expanded(
                            child: Text(
                              'Authentication failed. Please sign in again.',
                              style: TextStyle(
                                color: Colors.red,
                                fontSize: 12.0,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        });
      },
    );
  }
}

/// Debug WebSocket information widget
class WebSocketDebugInfo extends StatelessWidget {
  const WebSocketDebugInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<WebSocketController>(
      builder: (controller) {
        return Obx(() {
          return ExpansionTile(
            title: const Text('WebSocket Debug Info'),
            leading: const Icon(Icons.bug_report),
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDebugRow('Status', controller.connectionStatus.value),
                    _buildDebugRow(
                      'Connected',
                      controller.isConnected.value.toString(),
                    ),
                    _buildDebugRow(
                      'Connecting',
                      controller.isConnecting.value.toString(),
                    ),
                    const SizedBox(height: 16.0),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () => controller.connect(),
                            child: const Text('Connect'),
                          ),
                        ),
                        const SizedBox(width: 8.0),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () => controller.disconnect(),
                            child: const Text('Disconnect'),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8.0),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => controller.reconnect(),
                        child: const Text('Reconnect'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        });
      },
    );
  }

  Widget _buildDebugRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          SizedBox(
            width: 100.0,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
