import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/input_field.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/helpers/helpers.dart';
import 'package:ironlocker/app/helpers/theme.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/modules/shared-locker/models/shared_locker_member.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/shared_locker_member.dart';

class DialogHelper {
  static confirmDialog({
    required String title,
    required VoidCallback onConfirm,
    String? content,
    String? confirmBtnTxt,
    Color? confirmBtnBgColor,
    Color? confirmBtnFgColor,
    Color? cancelBtnBgColor,
    Color? cancelBtnFgColor,
    bool hideCancel = false,
    IconData? icon,
  }) {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        backgroundColor: Get.isDarkMode ? Colors.grey[850] : Colors.white,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icon
              if (icon != null) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: (confirmBtnBgColor ?? Colors.blue).withValues(
                      alpha: 0.1,
                    ),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: Icon(
                    icon,
                    size: 32,
                    color: confirmBtnBgColor ?? Colors.blue,
                  ),
                ),
                const SizedBox(height: 20),
              ],

              // Title
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Get.isDarkMode ? Colors.white : Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),

              // Content
              if (content != null) ...[
                const SizedBox(height: 12),
                Text(
                  content,
                  style: TextStyle(
                    fontSize: 14,
                    color: Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],

              const SizedBox(height: 24),

              // Buttons
              Row(
                children: [
                  if (!hideCancel) ...[
                    Expanded(
                      child: _buildDialogButton(
                        text: "Cancel",
                        onPressed: () => Get.back(),
                        backgroundColor: cancelBtnBgColor ?? Colors.transparent,
                        foregroundColor:
                            cancelBtnFgColor ??
                            (Get.isDarkMode
                                ? Colors.grey[400]!
                                : Colors.grey[600]!),
                        borderColor:
                            Get.isDarkMode
                                ? Colors.grey[600]!
                                : Colors.grey[300]!,
                      ),
                    ),
                    const SizedBox(width: 12),
                  ],
                  Expanded(
                    child: _buildDialogButton(
                      text: confirmBtnTxt ?? "Confirm",
                      onPressed: onConfirm,
                      backgroundColor: confirmBtnBgColor ?? Colors.blue,
                      foregroundColor: confirmBtnFgColor ?? Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  static confirmPassword({
    required Function(String password) onConfirm,
    String? content,
    Color? confirmBtnBgColor,
    Color? confirmBtnFgColor,
    Color? cancelBtnBgColor,
    Color? cancelBtnFgColor,
  }) {
    final passwordController = TextEditingController();

    Get.dialog(
      barrierDismissible: false,
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        backgroundColor: IronLockerColors.getThemeCard(Get.isDarkMode),
        child: SingleChildScrollView(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 400),
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: (confirmBtnBgColor ?? IronLockerColors.blue02)
                        .withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: Icon(
                    Icons.lock_outline,
                    size: 32,
                    color: confirmBtnBgColor ?? IronLockerColors.blue02,
                  ),
                ),
                const SizedBox(height: 20),

                // Title
                Text(
                  "Password Required",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Jura',
                    color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),

                // Content
                Text(
                  content ?? "Enter your password to proceed",
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'Jura',
                    color: IronLockerColors.getThemeTextSecondary(
                      Get.isDarkMode,
                    ),
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // Password Input
                InputField(
                  obscureText: true,
                  controller: passwordController,
                  hintText: "Enter your password",
                ),
                const SizedBox(height: 24),

                // Buttons
                Row(
                  children: [
                    Expanded(
                      child: _buildDialogButton(
                        text: "Cancel",
                        onPressed: () => Get.back(),
                        backgroundColor: cancelBtnBgColor ?? Colors.transparent,
                        foregroundColor:
                            cancelBtnFgColor ??
                            IronLockerColors.getThemeTextSecondary(
                              Get.isDarkMode,
                            ),
                        borderColor: IronLockerColors.getThemeBorder(
                          Get.isDarkMode,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildDialogButton(
                        text: "Continue",
                        onPressed: () {
                          if (passwordController.text.length < 8) {
                            ToastHelper.error("Enter your password");
                            return;
                          }
                          onConfirm(passwordController.text);
                        },
                        backgroundColor:
                            confirmBtnBgColor ?? IronLockerColors.blue02,
                        foregroundColor: confirmBtnFgColor ?? Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  static updateName({
    required TextEditingController controller,
    required Function() onUpdate,
  }) {
    Get.dialog(
      barrierDismissible: false,
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        backgroundColor: IronLockerColors.getThemeCard(Get.isDarkMode),
        child: SingleChildScrollView(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 400),
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: IronLockerColors.blue02.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: const Icon(
                    Icons.person_outline,
                    size: 32,
                    color: IronLockerColors.blue02,
                  ),
                ),
                const SizedBox(height: 20),

                // Title
                Text(
                  "Update Name",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Jura',
                    color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),

                // Subtitle
                Text(
                  "Enter your new display name",
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'Jura',
                    color: IronLockerColors.getThemeTextSecondary(
                      Get.isDarkMode,
                    ),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // Input Field
                InputField(controller: controller, hintText: "Enter your name"),
                const SizedBox(height: 24),

                // Buttons
                Row(
                  children: [
                    Expanded(
                      child: _buildDialogButton(
                        text: "Cancel",
                        onPressed: () => Get.back(),
                        backgroundColor: Colors.transparent,
                        foregroundColor: IronLockerColors.getThemeTextSecondary(
                          Get.isDarkMode,
                        ),
                        borderColor: IronLockerColors.getThemeBorder(
                          Get.isDarkMode,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildDialogButton(
                        text: "Update",
                        onPressed: onUpdate,
                        backgroundColor: IronLockerColors.blue02,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  static updateEmail({
    required TextEditingController emailController,
    required TextEditingController currentPasswordController,
    required Function() onUpdate,
  }) {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        backgroundColor: IronLockerColors.getThemeCard(Get.isDarkMode),
        child: SingleChildScrollView(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 400),
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: IronLockerColors.blue02.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: const Icon(
                    Icons.email_outlined,
                    size: 32,
                    color: IronLockerColors.blue02,
                  ),
                ),
                const SizedBox(height: 20),

                // Title
                Text(
                  "Update Email",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Jura',
                    color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),

                // Subtitle
                Text(
                  "Enter your new email address and current password",
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'Jura',
                    color: IronLockerColors.getThemeTextSecondary(
                      Get.isDarkMode,
                    ),
                  ),
                  textAlign: TextAlign.center,
                ),
                // Email Input Field
                InputField(
                  controller: emailController,
                  hintText: "Enter your email",
                ),
                const SizedBox(height: 16),

                // Password Input Field
                InputField(
                  controller: currentPasswordController,
                  hintText: "Current password",
                  obscureText: true,
                ),
                const SizedBox(height: 24),

                // Buttons
                Row(
                  children: [
                    Expanded(
                      child: _buildDialogButton(
                        text: "Cancel",
                        onPressed: () {
                          Get.back();
                          currentPasswordController.clear();
                        },
                        backgroundColor: Colors.transparent,
                        foregroundColor: IronLockerColors.getThemeTextSecondary(
                          Get.isDarkMode,
                        ),
                        borderColor: IronLockerColors.getThemeBorder(
                          Get.isDarkMode,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildDialogButton(
                        text: "Update",
                        onPressed: onUpdate,
                        backgroundColor: IronLockerColors.blue02,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  static confirmAccountDeletion({
    required Function(String password) onConfirm,
    String? content,
    Color? confirmBtnBgColor,
    Color? confirmBtnFgColor,
    Color? cancelBtnBgColor,
    Color? cancelBtnFgColor,
  }) {
    final passwordController = TextEditingController();

    Get.dialog(
      barrierDismissible: false,
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        backgroundColor: IronLockerColors.getThemeCard(Get.isDarkMode),
        child: SingleChildScrollView(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 400),
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: IronLockerColors.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: const Icon(
                    Icons.warning_outlined,
                    size: 32,
                    color: IronLockerColors.error,
                  ),
                ),
                const SizedBox(height: 20),

                // Title
                Text(
                  "Delete Account",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Jura',
                    color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),

                // Warning Message
                Text(
                  "Deleting your account will remove all your saved locker items and this is irreversible.",
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'Jura',
                    color: IronLockerColors.getThemeTextSecondary(
                      Get.isDarkMode,
                    ),
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),

                // Confirmation Message
                Text(
                  "To confirm, enter your password below:",
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'Jura',
                    color: IronLockerColors.getThemeTextSecondary(
                      Get.isDarkMode,
                    ),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // Password Field
                InputField(
                  controller: passwordController,
                  hintText: "Enter your current password",
                  obscureText: true,
                ),
                const SizedBox(height: 24),

                // Buttons
                Row(
                  children: [
                    Expanded(
                      child: _buildDialogButton(
                        text: "Cancel",
                        onPressed: () => Get.back(),
                        backgroundColor: cancelBtnBgColor ?? Colors.transparent,
                        foregroundColor:
                            cancelBtnFgColor ??
                            IronLockerColors.getThemeTextSecondary(
                              Get.isDarkMode,
                            ),
                        borderColor: IronLockerColors.getThemeBorder(
                          Get.isDarkMode,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildDialogButton(
                        text: "Delete",
                        onPressed: () => onConfirm(passwordController.text),
                        backgroundColor:
                            confirmBtnBgColor ?? IronLockerColors.error,
                        foregroundColor: confirmBtnFgColor ?? Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  static info({required String info}) {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        backgroundColor: Get.isDarkMode ? Colors.grey[850] : Colors.white,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icon
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(50),
                ),
                child: const Icon(
                  Icons.info_outline,
                  size: 32,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(height: 20),

              // Title
              Text(
                "Information",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Get.isDarkMode ? Colors.white : Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),

              // Content
              Text(
                info,
                style: TextStyle(
                  fontSize: 14,
                  color: Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),

              // Button
              SizedBox(
                width: double.infinity,
                child: _buildDialogButton(
                  text: "OK",
                  onPressed: () => Get.back(),
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  static loading() {
    Get.dialog(
      PopScope(
        canPop: false,
        child: Dialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Get.isDarkMode ? Colors.grey[850] : Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(
                  height: 50,
                  width: 50,
                  child: CircularProgressIndicator(color: Colors.blue),
                ),
                const SizedBox(height: 16),
                Text(
                  "Loading...",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Get.isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  static selectTheme() {
    final themeController = Get.find<ThemeController>();
    Get.dialog(
      PopScope(
        canPop: true,
        child: AlertDialog(
          title: Text(
            "Select theme",
            style: Theme.of(Get.context!).textTheme.titleSmall,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              RadioListTile(
                value: themeController.themeMode.value == ThemeMode.light,
                groupValue: true,
                title: const Text("Light", style: TextStyle(fontSize: 18)),
                contentPadding: EdgeInsets.zero,
                onChanged: (val) {
                  themeController.setThemeMode(ThemeMode.light);
                  Get.back();
                },
              ),
              RadioListTile(
                value: themeController.themeMode.value == ThemeMode.dark,
                groupValue: true,
                title: const Text("Dark", style: TextStyle(fontSize: 18)),
                contentPadding: EdgeInsets.zero,
                onChanged: (val) {
                  themeController.setThemeMode(ThemeMode.dark);
                  Get.back();
                },
              ),
              RadioListTile(
                value: themeController.themeMode.value == ThemeMode.system,
                groupValue: true,
                title: const Text(
                  "System (Default)",
                  style: TextStyle(fontSize: 18),
                ),
                contentPadding: EdgeInsets.zero,
                onChanged: (val) {
                  themeController.setThemeMode(ThemeMode.system);
                  Get.back();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  static sharedLockerMemberActions(
    BuildContext context,
    SharedLockerMember member,
  ) {
    final sharedLockerMemberService = Get.find<SharedLockerMemberService>();
    removeMember() async {
      Get.back();
      Get.back();

      DialogHelper.loading();
      var resp = await sharedLockerMemberService.removeMember(
        member.membershipId,
      );

      Get.back();

      if (resp.hasError) {
        if (resp.body?["error"] == "SESSION_LOCKED") {
          Get.offAllNamed(Routes.lockedLocker);
        } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
            resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
          await Helpers.clearData();
          Get.offAllNamed(Routes.signin);
        } else {
          ToastHelper.error("Something went wrong");
        }
        return;
      }

      ToastHelper.success("Member removed");
    }

    Get.dialog(
      AlertDialog(
        contentPadding: EdgeInsets.zero,
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              dense: true,
              title: Text(
                "Remove",
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              onTap: () {
                DialogHelper.confirmDialog(
                  title: "Are you sure you want to remove `${member.email}`",
                  onConfirm: removeMember,
                );
              },
            ),
            ListTile(
              dense: true,
              title: Text(
                "Update Permission",
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              onTap: () {},
            ),
          ],
        ),
      ),
    );
  }

  static Widget _buildDialogButton({
    required String text,
    required VoidCallback onPressed,
    required Color backgroundColor,
    required Color foregroundColor,
    Color? borderColor,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: foregroundColor,
        padding: const EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side:
              borderColor != null
                  ? BorderSide(color: borderColor, width: 1)
                  : BorderSide.none,
        ),
        elevation: backgroundColor == Colors.transparent ? 0 : 2,
      ),
      child: Text(
        text,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
    );
  }
}
