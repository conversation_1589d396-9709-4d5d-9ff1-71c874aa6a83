import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';

class Base64Image extends StatelessWidget {
  final String base64String;

  const Base64Image({super.key, required this.base64String});

  @override
  Widget build(BuildContext context) {
    String cleanedBase64String = base64String.split(",")[1];
    Uint8List bytes = base64Decode(cleanedBase64String);

    return SizedBox(
      height: 150,
      width: 150,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(15),
        child: Image.memory(
          bytes,
          fit: BoxFit.cover, // Adjust the fit according to your needs
        ),
      ),
    );
  }
}
