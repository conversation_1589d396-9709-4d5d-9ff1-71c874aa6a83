import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/banner_add.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/helpers/date_formatter.dart';
import 'package:ironlocker/app/modules/sessions/models/session.dart';
import 'package:ironlocker/app/stores/user.dart';
import 'package:ironlocker/app/themes/app_bar.dart';

import '../controllers/session_detail_controller.dart';

class SessionDetailView extends GetView<SessionDetailController> {
  const SessionDetailView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Get.isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
      appBar: LockerAppBar(
        title: "Session Details",
        leading: AppbarTheme.buildStyledBackButton(
          onPressed: () => Get.back(),
          tooltip: 'Back',
        ),
        showSearchIcon: false,
        showAddLockerItemButton: false,
      ),
      body: Column(
        children: [
          Expanded(
            child: Obx(
              () =>
                  controller.loading.isFalse
                      ? SingleChildScrollView(
                        padding: const EdgeInsets.all(20),
                        child: _buildSessionDetails(context),
                      )
                      : const Center(
                        child: CircularProgressIndicator(color: Colors.blue),
                      ),
            ),
          ),

          // AdMob Banner Ad at the bottom
          Container(
            color: Get.isDarkMode ? Colors.grey[900] : Colors.grey[50],
            child: SafeArea(
              top: false,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: BannerAdWidget(useTestAds: kDebugMode),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionDetails(BuildContext context) {
    final sessionData = SessionData.fromMap(controller.sessionData);
    final isCurrentSession = sessionData.id == UserStore.to.sessionId.value;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header Card
        _buildHeaderCard(sessionData, isCurrentSession),

        const SizedBox(height: 24),

        // Location Information
        _buildSectionHeader("Location Information"),
        const SizedBox(height: 12),
        _buildInfoCard([
          _buildDetailRow(
            icon: Icons.public,
            label: "IP Address",
            value: sessionData.ipAddress,
          ),
          _buildDivider(),
          _buildDetailRow(
            icon: Icons.language,
            label: "Continent",
            value: sessionData.continent ?? "Unknown",
          ),
          _buildDivider(),
          _buildDetailRow(
            icon: Icons.flag,
            label: "Country",
            value: sessionData.country ?? "Unknown",
          ),
          _buildDivider(),
          _buildDetailRow(
            icon: Icons.location_city,
            label: "City",
            value: sessionData.city ?? "Unknown",
          ),
          _buildDivider(),
          _buildDetailRow(
            icon: Icons.schedule,
            label: "Timezone",
            value: sessionData.timezone ?? "Unknown",
          ),
        ]),

        const SizedBox(height: 24),

        // Session Information
        _buildSectionHeader("Session Information"),
        const SizedBox(height: 12),
        _buildInfoCard([
          _buildDetailRow(
            icon: Icons.devices,
            label: "Device",
            value: _parseUserAgent(sessionData.userAgent),
          ),
          _buildDivider(),
          _buildDetailRow(
            icon: sessionData.locked ? Icons.lock : Icons.lock_open,
            label: "Status",
            value: sessionData.locked ? "Locked" : "Active",
            valueColor: sessionData.locked ? Colors.red : Colors.green,
          ),
          _buildDivider(),
          _buildDetailRow(
            icon: Icons.access_time,
            label: "Created",
            value: DateFormatter.format(sessionData.createdAt.toLocal()),
          ),
          _buildDivider(),
          _buildDetailRow(
            icon: Icons.update,
            label: "Last Active",
            value: DateFormatter.format(sessionData.lastActive.toLocal()),
          ),
        ]),

        if (isCurrentSession) ...[
          const SizedBox(height: 24),
          _buildCurrentSessionBanner(),
        ],

        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildHeaderCard(SessionData sessionData, bool isCurrentSession) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.withValues(alpha: 0.1),
            Colors.purple.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color:
              isCurrentSession
                  ? Colors.blue.withValues(alpha: 0.5)
                  : (Get.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!),
          width: isCurrentSession ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          // Country Flag or Location Icon
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(15),
            ),
            child:
                sessionData.countryFlag != null
                    ? ClipRRect(
                      borderRadius: BorderRadius.circular(15),
                      child: Image.network(
                        sessionData.countryFlag!,
                        width: 60,
                        height: 60,
                        fit: BoxFit.cover,
                        errorBuilder:
                            (context, error, stackTrace) => const Icon(
                              Icons.location_on,
                              color: Colors.blue,
                              size: 30,
                            ),
                      ),
                    )
                    : const Icon(
                      Icons.location_on,
                      color: Colors.blue,
                      size: 30,
                    ),
          ),
          const SizedBox(width: 20),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${sessionData.city ?? 'Unknown'}, ${sessionData.country ?? 'Unknown'}",
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Get.isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(
                          sessionData,
                          isCurrentSession,
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        _getStatusText(sessionData, isCurrentSession),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: _getStatusColor(sessionData, isCurrentSession),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: Get.isDarkMode ? Colors.white : Colors.black87,
      ),
    );
  }

  Widget _buildInfoCard(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(children: children),
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, size: 20, color: Colors.blue),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color:
                        valueColor ??
                        (Get.isDarkMode ? Colors.white : Colors.black87),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      thickness: 1,
      color: Get.isDarkMode ? Colors.grey[700] : Colors.grey[200],
    );
  }

  Widget _buildCurrentSessionBanner() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        children: [
          const Icon(Icons.info_outline, color: Colors.blue, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              "This is your current active session",
              style: TextStyle(
                fontSize: 14,
                color: Colors.blue[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(SessionData session, bool isCurrentSession) {
    if (session.locked) return Colors.red;
    if (isCurrentSession) return Colors.blue;
    return Colors.green;
  }

  String _getStatusText(SessionData session, bool isCurrentSession) {
    if (session.locked) return "Locked";
    if (isCurrentSession) return "Current Session";
    return "Active";
  }

  String _parseUserAgent(String userAgent) {
    // Simple user agent parsing - you can make this more sophisticated
    if (userAgent.contains('Chrome')) return 'Chrome Browser';
    if (userAgent.contains('Firefox')) return 'Firefox Browser';
    if (userAgent.contains('Safari')) return 'Safari Browser';
    if (userAgent.contains('Edge')) return 'Edge Browser';
    if (userAgent.contains('Android')) return 'Android Device';
    if (userAgent.contains('iPhone')) return 'iPhone';
    if (userAgent.contains('iPad')) return 'iPad';
    if (userAgent.contains('Windows')) return 'Windows Device';
    if (userAgent.contains('Mac')) return 'Mac Device';
    if (userAgent.contains('Linux')) return 'Linux Device';
    return 'Unknown Device';
  }
}
