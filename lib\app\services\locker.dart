import 'package:get/get.dart';
import 'package:ironlocker/app/constants/endpoints.dart';
import 'package:ironlocker/app/helpers/device_info.dart';
import 'package:ironlocker/app/helpers/storage.dart';
import 'package:ironlocker/app/stores/user.dart';

class LockerService extends GetConnect {
  final endPoints = ApiEndpoints();

  @override
  void onInit() {
    httpClient.timeout = const Duration(seconds: 30);
    httpClient.addRequestModifier<dynamic>((request) async {
      var device = await DeviceInfo.device();

      request.headers['User-Agent'] = device;
      request.headers['Authorization'] =
          "Bearer ${UserStore.to.accessToken.value}";
      return request;
    });

    httpClient.addAuthenticator<dynamic>((request) async {
      final storage = Storage();

      final response = await post("${endPoints.auth}/refresh-token",
          {"refreshToken": UserStore.to.refreshToken.value});

      if (response.hasError) {
        return request;
      }

      final accessToken = response.body['accessToken'];
      final refreshToken = response.body['refreshToken'];

      await storage.setItem(key: "accessToken", value: accessToken);
      await storage.setItem(key: "refreshToken", value: refreshToken);

      UserStore.to.accessToken(accessToken);
      UserStore.to.refreshToken(refreshToken);

      // Set the header
      request.headers['Authorization'] = "Bearer $accessToken";
      return request;
    });

    httpClient.maxAuthRetries = 3;

    super.onInit();
  }

  Future<Response> getLockerItems() {
    return get(endPoints.lockerItems);
  }

  Future<Response> getFolderItems(String folderId) {
    return get("${endPoints.lockerItems}/folder/$folderId");
  }

  Future<Response> deleteItem({
    String? itemType,
    required String itemId,
  }) {
    return delete("${endPoints.lockerItems}/$itemId");
  }

  Future<Response> deleteItems({
    required List items,
  }) {
    return post("${endPoints.lockerItems}/delete-items", {"items": items});
  }

  Future<Response> addItem({
    required String itemType,
    dynamic data,
    dynamic folder,
    dynamic note,
    dynamic contact,
    dynamic bankAccount,
    dynamic paymentCard,
    dynamic account,
    dynamic address,
  }) {
    Map body;

    switch (itemType) {
      case "Folder":
        body = {"itemType": "Folder", "folder": folder};
        break;
      case "Note":
        body = {"itemType": "Note", "note": note};
        break;
      case "Contact":
        body = {"itemType": "Contact", "contact": contact};
        break;
      case "Account":
        body = {"itemType": "Account", "account": account};
        break;
      case "Address":
        body = {"itemType": "Address", "address": address};
        break;
      case "BankAccount":
        body = {"itemType": "BankAccount", "bankAccount": bankAccount};
        break;
      case "PaymentCard":
        body = {"itemType": "PaymentCard", "paymentCard": paymentCard};
        break;
      default:
        body = {};
    }

    return post(endPoints.lockerItems, body);
  }

  Future<Response> updateItem({
    required String itemType,
    required String itemId,
    dynamic data,
    dynamic folder,
    dynamic note,
    dynamic contact,
    dynamic bankAccount,
    dynamic paymentCard,
    dynamic account,
    dynamic address,
  }) {
    Map body;

    switch (itemType) {
      case "Folder":
        body = {"itemType": "Folder", "folder": folder};
        break;

      case "Note":
        body = {"itemType": "Note", "note": note};
        break;
      case "Contact":
        body = {"itemType": "Contact", "contact": contact};
        break;
      case "Account":
        body = {"itemType": "Account", "account": account};
        break;
      case "Address":
        body = {"itemType": "Address", "address": address};
        break;
      case "BankAccount":
        body = {"itemType": "BankAccount", "bankAccount": bankAccount};
        break;
      case "PaymentCard":
        body = {"itemType": "PaymentCard", "paymentCard": paymentCard};
        break;
      default:
        body = {};
    }

    return put("${endPoints.lockerItems}/$itemId", body);
  }

  Future<Response> markFavourite({
    required List items,
  }) {
    return post("${endPoints.lockerItems}/mark-favourite", {"items": items});
  }

  Future<Response> unmarkFavourite({
    required List items,
  }) {
    return post("${endPoints.lockerItems}/unmark-favourite", {"items": items});
  }
}
