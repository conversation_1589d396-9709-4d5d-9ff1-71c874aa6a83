import 'dart:convert';
import 'dart:typed_data';

import 'package:bson/bson.dart';
import 'package:ironlocker/app/helpers/secure_encryption.dart';

class DecryptionHelper {
  static bool isMongoDBObjectId(String value) {
    try {
      ObjectId.parse(value);
      return true;
    } catch (e) {
      return false;
    }
  }

  static bool isEncryptedData(String value) {
    final base64Pattern = RegExp(
      r'^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$',
    );

    if (!base64Pattern.hasMatch(value)) {
      return false;
    }

    try {
      final Uint8List decodedBytes = base64.decode(value);
      const minEncryptedLength =
          SecureEncryptionHelper.saltLength +
          SecureEncryptionHelper.ivLength +
          SecureEncryptionHelper.authTagLength;
      return decodedBytes.length >= minEncryptedLength;
    } catch (e) {
      return false;
    }
  }

  static Future<dynamic> decryptDynamicData(
    dynamic data, [
    String? encryptionKey,
  ]) async {
    if (data is Map) {
      final decryptedMap = <String, dynamic>{};
      for (final key in data.keys) {
        decryptedMap[key] = await decryptDynamicData(data[key], encryptionKey);
      }
      return decryptedMap;
    } else if (data is List) {
      final decryptedList = [];
      for (final item in data) {
        decryptedList.add(await decryptDynamicData(item, encryptionKey));
      }
      return decryptedList;
    } else if (data is String &&
        isEncryptedData(data) &&
        !isMongoDBObjectId(data)) {
      try {
        return await SecureEncryptionHelper.decryptText(data, encryptionKey);
      } catch (e) {
        return 'Decryption failed';
      }
    } else {
      return data;
    }
  }
}
