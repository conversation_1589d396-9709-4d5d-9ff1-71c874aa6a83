import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/button.dart';
import 'package:ironlocker/app/components/drop_down_field.dart';
import 'package:ironlocker/app/components/input_field.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/rsa_key_helper.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/services/shared_locker_member.dart';
import 'package:ironlocker/app/services/user.dart';
import 'package:ironlocker/app/stores/shared_lockers.dart';
import 'package:pointycastle/export.dart' as encrypter;

void sharedLockerMemberSheet({
  required BuildContext context,
  required String sharedLockerId,
  required String encryptionKey,
}) {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  final emailController = TextEditingController();
  final permissionController = TextEditingController(text: "READ");
  ScrollController scrollController = ScrollController();
  var loading = false.obs;

  final sharedLockersStore = Get.find<SharedLockersStore>();
  final sharedLockerMemberService = Get.find<SharedLockerMemberService>();
  final userService = Get.find<UserService>();

  handleSubmit() async {
    if (formKey.currentState!.validate()) {
      DialogHelper.loading();

      var userResp = await userService.getPublic(emailController.text);

      if (userResp.hasError) {
        ToastHelper.error("Something went wrong");
        Get.back();
        return;
      }

      encrypter.RSAPublicKey publicKey = RSAKeyHelper.publicKeyFromBase64(
        userResp.body["publicKey"],
      );

      final sharedLockerKey = RSAKeyHelper.encryptWithPublicKey(
        encryptionKey,
        publicKey,
      );

      var resp = await sharedLockerMemberService.addMember(
        email: emailController.text,
        permission: permissionController.text,
        sharedLockerId: sharedLockerId,
        key: sharedLockerKey,
      );

      if (resp.hasError) {
        Get.back();
        if (resp.body?["error"] == "USER_NOT_FOUND") {
          ToastHelper.error("User with this email doesn't exist");
        } else if (resp.body?["error"] == "MEMBERSHIP_EXIST") {
          ToastHelper.error("Already a member");
        } else if (resp.body?["error"] == "MEMBERSHIP_LIMIT_REACHED") {
          ToastHelper.error("Membership limit reached");
        } else {
          ToastHelper.error("Something went wrong");
        }
        return;
      }

      await sharedLockersStore.fetchSharedLockers();

      emailController.clear();

      Get.back();
      Get.back();
      ToastHelper.success("Added");
    }
  }

  showModalBottomSheet(
    context: context,
    elevation: 0,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder:
        (context) => Container(
          decoration: BoxDecoration(
            color: IronLockerColors.getThemeCard(Get.isDarkMode),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(28),
              topRight: Radius.circular(28),
            ),
            border: Border.all(
              color: IronLockerColors.getThemeBorder(Get.isDarkMode),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(
                  alpha: Get.isDarkMode ? 0.4 : 0.15,
                ),
                blurRadius: 30,
                offset: const Offset(0, -10),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: IronLockerColors.blue02.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, -5),
                spreadRadius: -5,
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: SingleChildScrollView(
              controller: scrollController,
              child: SafeArea(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle bar
                    Container(
                      margin: const EdgeInsets.only(top: 16),
                      width: 48,
                      height: 5,
                      decoration: BoxDecoration(
                        color: IronLockerColors.getThemeBorder(Get.isDarkMode),
                        borderRadius: BorderRadius.circular(3),
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Header
                    _buildAddMemberHeader(),

                    // Content
                    Padding(
                      padding: const EdgeInsets.fromLTRB(24, 8, 24, 32),
                      child: Form(
                        key: formKey,
                        child: Column(
                          children: [
                            // Email Input Section
                            _buildEmailInputSection(emailController),

                            const SizedBox(height: 20),

                            // Permission Selection Section
                            _buildPermissionSection(permissionController),

                            const SizedBox(height: 32),

                            // Submit Button
                            Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: IronLockerColors.blue02.withValues(
                                      alpha: 0.3,
                                    ),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Obx(
                                () => Button(
                                  text: 'Add Member',
                                  loading: loading.value,
                                  onPress: handleSubmit,
                                  bgColor: IronLockerColors.blue02,
                                  height: 50,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
  );
}

Widget _buildAddMemberHeader() {
  return Container(
    margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    padding: const EdgeInsets.all(24),
    decoration: BoxDecoration(
      color: IronLockerColors.blue02.withValues(alpha: 0.1),
      borderRadius: BorderRadius.circular(20),
      border: Border.all(
        color: IronLockerColors.blue02.withValues(alpha: 0.2),
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: IronLockerColors.blue02.withValues(alpha: 0.1),
          blurRadius: 12,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: Row(
      children: [
        // Enhanced icon container
        Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            color: IronLockerColors.blue02,
            borderRadius: BorderRadius.circular(18),
            boxShadow: [
              BoxShadow(
                color: IronLockerColors.blue02.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: const Icon(
            Icons.person_add_rounded,
            color: Colors.white,
            size: 28,
          ),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Add Member",
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w700,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  letterSpacing: 0.5,
                ),
              ),
              const SizedBox(height: 6),
              Text(
                "Invite someone to collaborate on this shared locker",
                style: TextStyle(
                  fontSize: 15,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                  height: 1.3,
                ),
              ),
            ],
          ),
        ),
        // Decorative element
        Container(
          width: 4,
          height: 40,
          decoration: BoxDecoration(
            color: IronLockerColors.blue02,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ],
    ),
  );
}

Widget _buildEmailInputSection(TextEditingController emailController) {
  return Container(
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color:
          Get.isDarkMode
              ? Colors.grey[850]?.withValues(alpha: 0.5)
              : Colors.grey[50],
      borderRadius: BorderRadius.circular(16),
      border: Border.all(
        color: IronLockerColors.getThemeBorder(Get.isDarkMode),
        width: 1,
      ),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: IronLockerColors.blue02.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.email_rounded,
                color: IronLockerColors.blue02,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              "Member Email",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        InputField(
          labelText: "Email Address",
          controller: emailController,
          keyboardType: TextInputType.emailAddress,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter an email address';
            }
            if (!GetUtils.isEmail(value)) {
              return 'Please enter a valid email address';
            }
            return null;
          },
        ),
        const SizedBox(height: 8),
        Text(
          "Enter the email address of the person you want to invite to this shared locker.",
          style: TextStyle(
            fontSize: 13,
            color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
            fontFamily: 'Jura',
            height: 1.4,
          ),
        ),
      ],
    ),
  );
}

Widget _buildPermissionSection(TextEditingController permissionController) {
  return Container(
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color:
          Get.isDarkMode
              ? Colors.grey[850]?.withValues(alpha: 0.5)
              : Colors.grey[50],
      borderRadius: BorderRadius.circular(16),
      border: Border.all(
        color: IronLockerColors.getThemeBorder(Get.isDarkMode),
        width: 1,
      ),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: IronLockerColors.blue02.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.security_rounded,
                color: IronLockerColors.blue02,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              "Access Permission",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        DropDownField(
          controller: permissionController,
          items: const [
            {"name": "Read Only", "_id": "READ"},
            {"name": "Read & Write", "_id": "READ_WRITE"},
          ],
          labelText: "Permission Level",
          selectedItem: const {"name": "Read Only", "_id": "READ"},
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: IronLockerColors.blue02.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: IronLockerColors.blue02.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.info_outline_rounded,
                    color: IronLockerColors.blue02,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    "Permission Levels",
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Jura',
                      color: IronLockerColors.blue02,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                "• Read Only: Can view items but cannot add, edit, or delete\n• Read & Write: Can view, add, edit, and delete items",
                style: TextStyle(
                  fontSize: 13,
                  color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                  fontFamily: 'Jura',
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  );
}
