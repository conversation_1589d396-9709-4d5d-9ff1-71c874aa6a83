import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/button.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/themes/app_bar.dart';
import '../controllers/two_factor_app_controller.dart';

class TwoFactorAppView extends GetView<TwoFactorAppController> {
  const TwoFactorAppView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Get.isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
      appBar: LockerAppBar(
        title: "Setup Authenticator App",
        showSearchIcon: false,
        showAddLockerItemButton: false,
        leading: AppbarTheme.buildStyledBackButton(
          onPressed: () => Get.back(),
          tooltip: 'Back',
        ),
      ),
      body: Safe<PERSON>rea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              const SizedBox(height: 20),

              // Title and Description
              Text(
                "Authenticator App Setup",
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w700,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),

              Text(
                "Enter the 6-digit code from your authenticator app to complete setup",
                style: TextStyle(
                  fontSize: 16,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),

              // Main Content Card
              _buildMainCard(),
              const SizedBox(height: 24),

              // Setup Instructions
              _buildSetupInstructions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMainCard() {
    return Container(
      decoration: BoxDecoration(
        color: IronLockerColors.getThemeCard(Get.isDarkMode),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: IronLockerColors.getThemeBorder(Get.isDarkMode),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // App Icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: IronLockerColors.blue02.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(40),
                border: Border.all(
                  color: IronLockerColors.blue02.withValues(alpha: 0.3),
                  width: 2,
                ),
              ),
              child: Icon(
                Icons.smartphone,
                size: 40,
                color: IronLockerColors.blue02,
              ),
            ),
            const SizedBox(height: 24),

            // Instructions
            Text(
              "Enter Verification Code",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),

            Text(
              "Open your authenticator app and enter the 6-digit code",
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),

            // OTP Input
            _buildOTPInput(),
            const SizedBox(height: 32),

            // Enable Button
            SizedBox(
              width: double.infinity,
              child: Button(
                text: "Enable 2FA with Authenticator",
                onPress: controller.enable2fa,
                bgColor: IronLockerColors.blue02,
                fgColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSetupInstructions() {
    return Container(
      decoration: BoxDecoration(
        color: IronLockerColors.getThemeCard(Get.isDarkMode),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: IronLockerColors.getThemeBorder(Get.isDarkMode),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: IronLockerColors.blue02,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  "Setup Instructions",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Jura',
                    color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildInstructionStep(
              step: "1",
              text:
                  "Download an authenticator app like Google Authenticator or Authy",
            ),
            _buildInstructionStep(
              step: "2",
              text: "Scan the QR code or enter the setup key in your app",
            ),
            _buildInstructionStep(
              step: "3",
              text: "Enter the 6-digit code from your authenticator app above",
            ),
            _buildInstructionStep(
              step: "4",
              text: "Save your recovery codes in a secure location",
              isLast: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionStep({
    required String step,
    required String text,
    bool isLast = false,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: isLast ? 0 : 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: IronLockerColors.blue02,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                step,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Jura',
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOTPInput() {
    return StatefulBuilder(
      builder: (context, setState) {
        final List<TextEditingController> controllers = List.generate(
          6,
          (index) => TextEditingController(),
        );
        final List<FocusNode> focusNodes = List.generate(
          6,
          (index) => FocusNode(),
        );

        // Update the main controller when any field changes
        void updateMainController() {
          final code = controllers.map((c) => c.text).join();
          controller.tokenController.text = code;
        }

        return LayoutBuilder(
          builder: (context, constraints) {
            final availableWidth = constraints.maxWidth;
            final fieldWidth = (availableWidth - (5 * 8)) / 6;
            final maxFieldWidth = 45.0;
            final actualFieldWidth =
                fieldWidth > maxFieldWidth ? maxFieldWidth : fieldWidth;

            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(6, (index) {
                return Container(
                  width: actualFieldWidth,
                  margin: EdgeInsets.only(right: index < 5 ? 8 : 0),
                  child: TextFormField(
                    controller: controllers[index],
                    focusNode: focusNodes[index],
                    textAlign: TextAlign.center,
                    keyboardType: TextInputType.number,
                    maxLength: 6, // Allow longer input to detect paste
                    inputFormatters: [
                      _OTPInputFormatter(
                        index: index,
                        controllers: controllers,
                        focusNodes: focusNodes,
                        updateMainController: updateMainController,
                      ),
                    ],
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Jura',
                      color: IronLockerColors.getThemeTextPrimary(
                        Get.isDarkMode,
                      ),
                    ),
                    decoration: InputDecoration(
                      counterText: '',
                      contentPadding: const EdgeInsets.symmetric(vertical: 10),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: IronLockerColors.getThemeBorder(
                            Get.isDarkMode,
                          ),
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: IronLockerColors.getThemeBorder(
                            Get.isDarkMode,
                          ),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: IronLockerColors.blue02,
                          width: 2,
                        ),
                      ),
                      filled: true,
                      fillColor:
                          Get.isDarkMode ? Colors.grey[800] : Colors.grey[50],
                    ),
                    onChanged: (value) {
                      // This will be handled by the input formatter
                    },
                  ),
                );
              }),
            );
          },
        );
      },
    );
  }
}

class _OTPInputFormatter extends TextInputFormatter {
  final int index;
  final List<TextEditingController> controllers;
  final List<FocusNode> focusNodes;
  final VoidCallback updateMainController;

  _OTPInputFormatter({
    required this.index,
    required this.controllers,
    required this.focusNodes,
    required this.updateMainController,
  });

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final newText = newValue.text;

    // Handle paste operation (when length > 1)
    if (newText.length > 1) {
      final pastedCode = newText.replaceAll(RegExp(r'[^0-9]'), '');

      if (pastedCode.length >= 6) {
        // Fill all fields with pasted code
        for (int i = 0; i < 6; i++) {
          controllers[i].text = i < pastedCode.length ? pastedCode[i] : '';
        }
        updateMainController();

        // Unfocus current field and focus last field
        WidgetsBinding.instance.addPostFrameCallback((_) {
          focusNodes[5].unfocus();
        });

        // Return only the digit for current field
        return TextEditingValue(
          text: pastedCode[index],
          selection: TextSelection.collapsed(offset: 1),
        );
      } else if (pastedCode.isNotEmpty) {
        // If pasted text is shorter, just take the first digit
        final digit = pastedCode[0];
        updateMainController();

        // Move to next field
        if (index < 5) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            focusNodes[index + 1].requestFocus();
          });
        }

        return TextEditingValue(
          text: digit,
          selection: TextSelection.collapsed(offset: 1),
        );
      }
    }

    // Handle normal single character input
    if (newText.length == 1 && RegExp(r'[0-9]').hasMatch(newText)) {
      updateMainController();

      // Move to next field
      if (index < 5) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          focusNodes[index + 1].requestFocus();
        });
      } else {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          focusNodes[index].unfocus();
        });
      }

      return TextEditingValue(
        text: newText,
        selection: TextSelection.collapsed(offset: 1),
      );
    }

    // Handle backspace/delete
    if (newText.isEmpty) {
      updateMainController();

      if (index > 0) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          focusNodes[index - 1].requestFocus();
        });
      }

      return const TextEditingValue(
        text: '',
        selection: TextSelection.collapsed(offset: 0),
      );
    }

    // Reject invalid input
    return oldValue;
  }
}
