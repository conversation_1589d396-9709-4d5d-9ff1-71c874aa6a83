import 'package:get/get.dart';
import 'package:ironlocker/app/services/analytics_service.dart';

/// Mixin to provide easy analytics tracking for controllers
mixin AnalyticsMixin on GetxController {
  AnalyticsService get analytics => AnalyticsService.to;

  /// Track screen view when controller initializes
  void trackScreenView(String screenName, {Map<String, dynamic>? parameters}) {
    analytics.trackScreenView(
      screenName: screenName,
      parameters: parameters,
    );
  }

  /// Track user actions
  void trackAction(String action, {
    String? category,
    String? label,
    int? value,
    Map<String, dynamic>? parameters,
  }) {
    analytics.trackUserEngagement(
      action: action,
      category: category,
      label: label,
      value: value,
      parameters: parameters,
    );
  }

  /// Track locker operations
  void trackLockerOperation(String action, String itemType, {
    int? itemCount,
    Map<String, dynamic>? parameters,
  }) {
    analytics.trackLockerAction(
      action: action,
      itemType: itemType,
      itemCount: itemCount,
      parameters: parameters,
    );
  }

  /// Track security events
  void trackSecurity(String event, {
    bool? success,
    String? method,
    Map<String, dynamic>? parameters,
  }) {
    analytics.trackSecurityEvent(
      event: event,
      success: success,
      method: method,
      parameters: parameters,
    );
  }

  /// Track performance metrics
  void trackPerformance(String metric, int durationMs, {
    bool? success,
    String? details,
  }) {
    analytics.trackPerformance(
      metric: metric,
      duration: durationMs,
      success: success,
      details: details,
    );
  }

  /// Track errors
  void trackError(String error, {
    String? context,
    String? stackTrace,
    bool? fatal,
  }) {
    analytics.trackError(
      error: error,
      context: context,
      stackTrace: stackTrace,
      fatal: fatal,
    );
  }
}
