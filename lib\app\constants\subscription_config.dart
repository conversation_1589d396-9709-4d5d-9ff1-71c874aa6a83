/// RevenueCat configuration constants
class SubscriptionConfig {
  /// RevenueCat API Key for Google Play Store (Android)
  static const String googleApiKey = "goog_lUezSHmqJBLWAYZtdgBdIkvtwnK";

  /// RevenueCat API Key for Apple App Store (iOS)
  static const String appleApiKey = "appl_YOUR_APPLE_API_KEY_HERE";

  /// Entitlement identifier for premium features
  /// This should match what you configure in RevenueCat dashboard
  static const String premiumEntitlementId = "premium";

  /// Product identifiers for your subscription products
  /// These should match what you configure in both app stores and RevenueCat
  static const String monthlyProductId = "monthly_premium";
  static const String yearlyProductId = "yearly_premium";

  /// Offering identifier (optional - uses default if not specified)
  static const String defaultOfferingId = "default";
}
