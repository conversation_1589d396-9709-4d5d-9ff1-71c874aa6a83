import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:pointycastle/export.dart';
import 'package:basic_utils/basic_utils.dart';

class RSAKeyHelper {
  static AsymmetricKeyPair<RSAPublicKey, RSAPrivateKey> generateRSAKeyPair() {
    final keyParams = RSAKeyGeneratorParameters(
      BigInt.parse('65537'),
      2048,
      12,
    );
    final secureRandom = FortunaRandom();
    final random = Random.secure();
    final seed = Uint8List.fromList(
      List<int>.generate(32, (_) => random.nextInt(256)),
    );
    secureRandom.seed(KeyParameter(seed));
    final rngParams = ParametersWithRandom(keyParams, secureRandom);
    final keyGenerator = RSAKeyGenerator()..init(rngParams);
    final pair = keyGenerator.generateKeyPair();
    return AsymmetricKeyPair<RSAPublicKey, RSAPrivateKey>(
      pair.publicKey,
      pair.privateKey,
    );
  }

  /// Encodes an RSAPublicKey to Base64 format.
  static String encodePublicKeyToPem(RSAPublicKey publicKey) {
    return CryptoUtils.encodeRSAPublicKeyToPem(publicKey);
  }

  /// Encodes an RSAPrivateKey to Base64 format.
  static String encodePrivateKeyToPem(RSAPrivateKey privateKey) {
    return CryptoUtils.encodeRSAPrivateKeyToPem(privateKey);
  }

  /// Encrypts a plaintext using the RSA public key.
  static String encryptWithPublicKey(String plaintext, RSAPublicKey publicKey) {
    final encryptor = OAEPEncoding(RSAEngine())..init(
      true,
      PublicKeyParameter<RSAPublicKey>(publicKey),
    ); // true for encryption
    final plaintextBytes = Uint8List.fromList(utf8.encode(plaintext));
    return base64.encode(_processInBlocks(encryptor, plaintextBytes));
  }

  /// Decrypts ciphertext using the RSA private key.
  static String decryptWithPrivateKey(
    String encryptedText,
    RSAPrivateKey privateKey,
  ) {
    final decryptor = OAEPEncoding(RSAEngine())..init(
      false,
      PrivateKeyParameter<RSAPrivateKey>(privateKey),
    ); // false for decryption
    final decryptedBytes = _processInBlocks(
      decryptor,
      base64.decode(encryptedText),
    );
    return utf8.decode(decryptedBytes);
  }

  /// Processes data in blocks for encryption/decryption.
  static Uint8List _processInBlocks(
    AsymmetricBlockCipher cipher,
    Uint8List input,
  ) {
    final blockSize = cipher.inputBlockSize;
    final output = Uint8List(
      cipher.outputBlockSize,
    ); // Use outputBlockSize property
    var offset = 0;
    var outputOffset = 0;

    while (offset < input.length) {
      final chunkSize =
          offset + blockSize <= input.length
              ? blockSize
              : input.length - offset;
      final outputChunk = cipher.process(
        input.sublist(offset, offset + chunkSize),
      );
      output.setRange(
        outputOffset,
        outputOffset + outputChunk.length,
        outputChunk,
      );
      offset += chunkSize;
      outputOffset += outputChunk.length;
    }

    return output.sublist(0, outputOffset); // Return only the processed part
  }

  /// Generates an RSA key pair and returns the keys as Base64-encoded PEM strings.
  static Future<Map<String, String>> generateAndEncodeKeys() async {
    final keyPair = generateRSAKeyPair();
    final publicKeyBase64 = encodePublicKeyToPem(keyPair.publicKey);
    final privateKeyBase64 = encodePrivateKeyToPem(keyPair.privateKey);
    return {'publicKey': publicKeyBase64, 'privateKey': privateKeyBase64};
  }

  static RSAPublicKey publicKeyFromBase64(String keyBase64) {
    return CryptoUtils.rsaPublicKeyFromPem(keyBase64);
  }

  static RSAPrivateKey privateKeyFromBase64(String keyBase64) {
    return CryptoUtils.rsaPrivateKeyFromPem(keyBase64);
  }
}
