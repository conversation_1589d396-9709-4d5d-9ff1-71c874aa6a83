import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/controllers/ad_free_controller.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/stores/user.dart';

/// Widget that displays ad-free experience options and status
class AdFreeWidget extends StatelessWidget {
  final bool showAsCard;
  final bool showInDrawer;

  const AdFreeWidget({
    super.key,
    this.showAsCard = true,
    this.showInDrawer = false,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // Hide completely when user has premium or lifetime plan
      if (UserStore.to.plan["name"] == "premium" ||
          UserStore.to.plan["name"] == "lifetime") {
        return const SizedBox.shrink();
      }

      return _buildWrapper();
    });
  }

  Widget _buildWrapper() {
    if (showInDrawer) {
      return InkWell(
        onTap:
            AdFreeController.to.isAdFree ? null : _showRewardedVideoForAdFree,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: _buildContent(),
        ),
      );
    }

    if (showAsCard) {
      return Card(
        margin: const EdgeInsets.all(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: _buildContent(),
        ),
      );
    }

    return Container(padding: const EdgeInsets.all(16), child: _buildContent());
  }

  Future<void> _showRewardedVideoForAdFree() async {
    if (AdFreeController.to.isLoading) return;

    try {
      final success = await AdFreeController.to.activateAdFreeExperience(
        minutes: 30,
      );

      if (success) {
        ToastHelper.success("Enjoy 30 minutes of ad-free experience!");
      } else {
        ToastHelper.error("Unable to load video ad. Please try again.");
      }
    } catch (e) {
      ToastHelper.error("Something went wrong. Please try again.");
    }
  }

  Widget _buildContent() {
    if (AdFreeController.to.isAdFree) {
      return _buildActiveAdFreeContent();
    } else {
      return _buildInactiveAdFreeContent();
    }
  }

  Widget _buildActiveAdFreeContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            Icon(
              Icons.block,
              color: Colors.green,
              size: showInDrawer ? 20 : 24,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                "Ad-Free Active",
                style: TextStyle(
                  fontSize: showInDrawer ? 14 : 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.green,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          "Time remaining: ${AdFreeController.to.formattedRemainingTime}",
          style: TextStyle(
            fontSize: showInDrawer ? 12 : 14,
            color: Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildInactiveAdFreeContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            Icon(
              Icons.play_circle,
              color: Get.isDarkMode ? Colors.blue[300] : Colors.blue,
              size: showInDrawer ? 20 : 24,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                "Go Ad-Free",
                style: TextStyle(
                  fontSize: showInDrawer ? 14 : 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          "Watch video for 30 minutes without ads",
          style: TextStyle(
            fontSize: showInDrawer ? 12 : 14,
            color: Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
        ),
        if (!showInDrawer) ...[
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed:
                  AdFreeController.to.isLoading
                      ? null
                      : _showRewardedVideoForAdFree,
              icon:
                  AdFreeController.to.isLoading
                      ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Icon(Icons.play_circle, size: 18),
              label: Text(
                AdFreeController.to.isLoading ? "Loading..." : "Watch Video",
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    Get.isDarkMode ? Colors.blue[700] : Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ],
    );
  }
}

/// Simple drawer item for ad-free experience
class AdFreeDrawerItem extends StatelessWidget {
  const AdFreeDrawerItem({super.key});

  @override
  Widget build(BuildContext context) {
    return const AdFreeWidget(showAsCard: false, showInDrawer: true);
  }
}

/// Card widget for settings or main screens
class AdFreeCard extends StatelessWidget {
  const AdFreeCard({super.key});

  @override
  Widget build(BuildContext context) {
    return const AdFreeWidget(showAsCard: true, showInDrawer: false);
  }
}
