import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:ironlocker/app/helpers/ad_helper.dart';
import 'package:ironlocker/app/helpers/ad_manager.dart';
import 'package:ironlocker/app/stores/secure_storage.dart';
import 'package:ironlocker/app/stores/user.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';

/// Manages rewarded video ads for ad-free experience
class AdFreeManager extends GetxService {
  static AdFreeManager get instance => Get.find<AdFreeManager>();

  final SecureStorageService _storage = Get.find<SecureStorageService>();

  // Storage keys
  static const String _adFreeUntilKey = 'ad_free_until';
  static const String _totalAdFreeTimeKey = 'total_ad_free_time';
  static const String _rewardedVideosWatchedKey = 'rewarded_videos_watched';

  // Ad-free duration options (in minutes for better control)
  static const int defaultAdFreeMinutes = 30; // 30 minutes default
  static const int maxAdFreeMinutes = 120; // 2 hours max

  // Rewarded video ad
  RewardedAd? _rewardedAd;
  bool _isLoading = false;
  bool _isShowing = false;
  int _loadAttempts = 0;
  static const int _maxLoadAttempts = 3;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _preloadRewardedAd();
  }

  /// Check if user currently has ad-free experience active
  Future<bool> isAdFreeActive() async {
    try {
      final adFreeUntilString = await _storage.read(_adFreeUntilKey);
      if (adFreeUntilString == null) return false;

      final adFreeUntil = DateTime.parse(adFreeUntilString);
      final isActive = DateTime.now().isBefore(adFreeUntil);

      debugPrint(
        'AdFree status: ${isActive ? "ACTIVE" : "EXPIRED"} until $adFreeUntil',
      );
      return isActive;
    } catch (e) {
      debugPrint('Error checking ad-free status: $e');
      return false;
    }
  }

  /// Get remaining ad-free time in minutes
  Future<int> getRemainingAdFreeMinutes() async {
    try {
      final adFreeUntilString = await _storage.read(_adFreeUntilKey);
      if (adFreeUntilString == null) return 0;

      final adFreeUntil = DateTime.parse(adFreeUntilString);
      final now = DateTime.now();

      if (now.isBefore(adFreeUntil)) {
        return adFreeUntil.difference(now).inMinutes;
      }
      return 0;
    } catch (e) {
      debugPrint('Error getting remaining ad-free time: $e');
      return 0;
    }
  }

  /// Grant ad-free experience for specified hours
  Future<void> grantAdFreeTime(int hours) async {
    try {
      final now = DateTime.now();
      final adFreeUntil = now.add(Duration(hours: hours));

      await _storage.write(_adFreeUntilKey, adFreeUntil.toIso8601String());

      // Track total ad-free time granted
      final currentTotal = await _getTotalAdFreeTime();
      await _storage.write(
        _totalAdFreeTimeKey,
        (currentTotal + hours).toString(),
      );

      // Track rewarded videos watched
      final currentCount = await _getRewardedVideosWatched();
      await _storage.write(
        _rewardedVideosWatchedKey,
        (currentCount + 1).toString(),
      );

      debugPrint(
        '✅ Ad-free experience granted for $hours hours until $adFreeUntil',
      );

      // Analytics tracking
      _trackAdFreeGranted(hours);
    } catch (e) {
      debugPrint('❌ Error granting ad-free time: $e');
      if (!kDebugMode) {
        FirebaseCrashlytics.instance.recordError(
          'AdFreeManager: Failed to grant ad-free time',
          StackTrace.current,
          information: ['Hours: $hours', 'Error: $e'],
        );
      }
    }
  }

  /// Grant ad-free experience for specified minutes
  Future<void> grantAdFreeTimeMinutes(int minutes) async {
    try {
      final now = DateTime.now();
      final adFreeUntil = now.add(Duration(minutes: minutes));

      await _storage.write(_adFreeUntilKey, adFreeUntil.toIso8601String());

      // Track total ad-free time granted (in minutes)
      final currentTotal = await _getTotalAdFreeTime();
      await _storage.write(
        _totalAdFreeTimeKey,
        (currentTotal + minutes).toString(),
      );

      // Track rewarded videos watched
      final currentCount = await _getRewardedVideosWatched();
      await _storage.write(
        _rewardedVideosWatchedKey,
        (currentCount + 1).toString(),
      );

      debugPrint(
        '✅ Ad-free experience granted for $minutes minutes until $adFreeUntil',
      );

      // Analytics tracking
      _trackAdFreeGranted(minutes);
    } catch (e) {
      debugPrint('❌ Error granting ad-free time: $e');
      if (!kDebugMode) {
        FirebaseCrashlytics.instance.recordError(
          'AdFreeManager: Failed to grant ad-free time',
          StackTrace.current,
          information: ['Minutes: $minutes', 'Error: $e'],
        );
      }
    }
  }

  /// Load rewarded video ad
  Future<bool> _loadRewardedAd({bool useTestAds = false}) async {
    if (_isLoading || _rewardedAd != null) return _rewardedAd != null;

    _isLoading = true;
    _loadAttempts++;

    try {
      debugPrint(
        '🎬 Loading rewarded video ad (attempt $_loadAttempts/$_maxLoadAttempts)',
      );

      final adUnitId =
          useTestAds
              ? AdHelper.testRewardedAdUnitId
              : AdHelper.rewardedAdUnitId;

      final completer = Completer<bool>();

      await RewardedAd.load(
        adUnitId: adUnitId,
        request: AdManager.instance.getOptimizedAdRequest(),
        rewardedAdLoadCallback: RewardedAdLoadCallback(
          onAdLoaded: (RewardedAd ad) {
            debugPrint('✅ Rewarded video ad loaded successfully');
            _rewardedAd = ad;
            _isLoading = false;
            _loadAttempts = 0;
            if (!completer.isCompleted) completer.complete(true);
          },
          onAdFailedToLoad: (LoadAdError error) {
            debugPrint('❌ Rewarded video ad failed to load: ${error.message}');
            _isLoading = false;

            // Report error
            if (!kDebugMode) {
              FirebaseCrashlytics.instance.recordError(
                'RewardedAd load failed',
                StackTrace.current,
                information: [
                  'Error Code: ${error.code}',
                  'Error Message: ${error.message}',
                  'Load Attempt: $_loadAttempts',
                ],
              );
            }

            if (!completer.isCompleted) completer.complete(false);
          },
        ),
      );

      return await completer.future;
    } catch (e) {
      debugPrint('❌ Exception loading rewarded video ad: $e');
      _isLoading = false;
      return false;
    }
  }

  /// Preload rewarded video ad for better user experience
  Future<void> _preloadRewardedAd() async {
    await _loadRewardedAd(useTestAds: kDebugMode);
  }

  /// Show rewarded video ad and grant ad-free time on completion
  Future<bool> showRewardedVideoForAdFree({
    int minutes = defaultAdFreeMinutes,
    bool useTestAds = false,
  }) async {
    try {
      debugPrint(
        '🎬 Attempting to show rewarded video for $minutes minutes ad-free',
      );

      // Check if user has premium or lifetime plan
      if (UserStore.to.plan["name"] == "premium" ||
          UserStore.to.plan["name"] == "lifetime") {
        debugPrint(
          '🚫 Skipping rewarded video - user has premium/lifetime plan',
        );
        return false;
      }

      // Check if already ad-free
      if (await isAdFreeActive()) {
        debugPrint('⚠️ User already has active ad-free time');
        return false;
      }

      // Load ad if not already loaded
      if (_rewardedAd == null) {
        final loaded = await _loadRewardedAd(useTestAds: useTestAds);
        if (!loaded) {
          debugPrint('❌ Failed to load rewarded video ad');
          return false;
        }
      }

      if (_rewardedAd == null || _isShowing) {
        debugPrint('⚠️ Rewarded video ad not ready or already showing');
        return false;
      }

      final completer = Completer<bool>();
      bool rewardEarned = false;

      // Set up ad callbacks
      _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
        onAdShowedFullScreenContent: (RewardedAd ad) {
          debugPrint('🎬 Rewarded video ad displayed');
          _isShowing = true;
        },
        onAdDismissedFullScreenContent: (RewardedAd ad) {
          debugPrint('🎬 Rewarded video ad dismissed');
          _isShowing = false;
          ad.dispose();
          _rewardedAd = null;

          if (!completer.isCompleted) {
            completer.complete(rewardEarned);
          }

          // Preload next ad
          _preloadRewardedAd();
        },
        onAdFailedToShowFullScreenContent: (RewardedAd ad, AdError error) {
          debugPrint('❌ Rewarded video ad failed to show: ${error.message}');
          _isShowing = false;
          ad.dispose();
          _rewardedAd = null;

          if (!completer.isCompleted) {
            completer.complete(false);
          }

          if (!kDebugMode) {
            FirebaseCrashlytics.instance.recordError(
              'RewardedAd show failed',
              StackTrace.current,
              information: [
                'Error Code: ${error.code}',
                'Error Message: ${error.message}',
              ],
            );
          }
        },
      );

      // Show the ad
      await _rewardedAd!.show(
        onUserEarnedReward: (AdWithoutView ad, RewardItem reward) async {
          debugPrint('🎁 User earned reward: ${reward.amount} ${reward.type}');
          rewardEarned = true;

          // Grant ad-free time in minutes
          await grantAdFreeTimeMinutes(minutes);
        },
      );

      return await completer.future;
    } catch (e) {
      debugPrint('❌ Exception showing rewarded video ad: $e');
      _isShowing = false;
      return false;
    }
  }

  /// Get total ad-free time granted (in hours)
  Future<int> _getTotalAdFreeTime() async {
    try {
      final totalString = await _storage.read(_totalAdFreeTimeKey);
      return totalString != null ? int.parse(totalString) : 0;
    } catch (e) {
      return 0;
    }
  }

  /// Get total rewarded videos watched
  Future<int> _getRewardedVideosWatched() async {
    try {
      final countString = await _storage.read(_rewardedVideosWatchedKey);
      return countString != null ? int.parse(countString) : 0;
    } catch (e) {
      return 0;
    }
  }

  /// Track ad-free granted event for analytics
  void _trackAdFreeGranted(int hours) {
    // Add analytics tracking here if needed
    debugPrint('📊 Analytics: Ad-free granted for $hours hours');
  }

  /// Check if rewarded video is ready to show
  bool get isRewardedAdReady => _rewardedAd != null && !_isShowing;

  /// Check if rewarded video is currently loading
  bool get isLoading => _isLoading;

  /// Dispose of current rewarded ad
  void dispose() {
    _rewardedAd?.dispose();
    _rewardedAd = null;
    _isLoading = false;
    _isShowing = false;
  }
}
