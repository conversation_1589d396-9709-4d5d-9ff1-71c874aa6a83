import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/button.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/helpers/copy_text_to_clipboard.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/themes/app_bar.dart';
import '../controllers/recovery_codes_controller.dart';

class RecoveryCodesView extends GetView<RecoveryCodesController> {
  const RecoveryCodesView({super.key});

  static final recoveryCodes = Get.parameters["recoveryCodes"]!.split(" ");

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        Get.offNamed(Routes.SECURITY);
      },
      child: Scaffold(
        backgroundColor:
            Get.isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
        appBar: LockerAppBar(
          title: "Recovery Codes",
          showSearchIcon: false,
          showAddLockerItemButton: false,
          leading: AppbarTheme.buildStyledBackButton(
            onPressed: () => Get.offNamed(Routes.SECURITY),
            tooltip: 'Back to Security',
          ),
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                const SizedBox(height: 20),

                // Title and Description
                Text(
                  "Your Recovery Codes",
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                    fontFamily: 'Jura',
                    color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),

                Text(
                  "Save these codes in a secure location. You can use them to access your account if you lose your 2FA device.",
                  style: TextStyle(
                    fontSize: 16,
                    fontFamily: 'Jura',
                    color: IronLockerColors.getThemeTextSecondary(
                      Get.isDarkMode,
                    ),
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),

                // Recovery Codes Card
                _buildRecoveryCodesCard(),
                const SizedBox(height: 24),

                // Action Buttons
                _buildActionButtons(),
                const SizedBox(height: 24),

                // Important Notice
                _buildImportantNotice(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRecoveryCodesCard() {
    return Container(
      decoration: BoxDecoration(
        color: IronLockerColors.getThemeCard(Get.isDarkMode),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: IronLockerColors.getThemeBorder(Get.isDarkMode),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: IronLockerColors.blue02.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: IronLockerColors.blue02.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.security,
                    size: 20,
                    color: IronLockerColors.blue02,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    "Recovery Codes",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Jura',
                      color: IronLockerColors.getThemeTextPrimary(
                        Get.isDarkMode,
                      ),
                    ),
                  ),
                ),
                // Copy All Button
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      copyTextToClipboard(recoveryCodes.join('\n'));
                      ToastHelper.success(
                        "All recovery codes copied to clipboard",
                      );
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      child: Icon(
                        Icons.copy_all,
                        size: 20,
                        color: IronLockerColors.blue02,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Recovery Codes Grid
            _buildRecoveryCodesGrid(),
          ],
        ),
      ),
    );
  }

  Widget _buildRecoveryCodesGrid() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[900] : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: IronLockerColors.getThemeBorder(Get.isDarkMode),
          width: 1,
        ),
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final isSmallScreen = constraints.maxWidth < 400;

          if (isSmallScreen) {
            // Single column layout for small screens
            return Column(
              children: [
                for (int i = 0; i < recoveryCodes.length; i++)
                  Padding(
                    padding: EdgeInsets.only(
                      bottom: i < recoveryCodes.length - 1 ? 12 : 0,
                    ),
                    child: _buildRecoveryCodeItem(recoveryCodes[i], i + 1),
                  ),
              ],
            );
          } else {
            // Two column layout for larger screens
            return Column(
              children: [
                for (int i = 0; i < recoveryCodes.length; i += 2)
                  Padding(
                    padding: EdgeInsets.only(
                      bottom: i < recoveryCodes.length - 2 ? 16 : 0,
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: _buildRecoveryCodeItem(
                            recoveryCodes[i],
                            i + 1,
                          ),
                        ),
                        if (i + 1 < recoveryCodes.length) ...[
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildRecoveryCodeItem(
                              recoveryCodes[i + 1],
                              i + 2,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
              ],
            );
          }
        },
      ),
    );
  }

  Widget _buildRecoveryCodeItem(String code, int index) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        color: IronLockerColors.getThemeCard(Get.isDarkMode),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: IronLockerColors.getThemeBorder(Get.isDarkMode),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Index
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: IronLockerColors.blue02.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                '$index',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Jura',
                  color: IronLockerColors.blue02,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),

          // Code
          Expanded(
            child: Text(
              code,
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'monospace',
                fontWeight: FontWeight.w500,
                color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                letterSpacing: 1.2,
              ),
            ),
          ),

          // Copy Button
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                copyTextToClipboard(code);
                ToastHelper.success("Recovery code copied");
              },
              borderRadius: BorderRadius.circular(4),
              child: Container(
                padding: const EdgeInsets.all(4),
                child: Icon(
                  Icons.copy,
                  size: 16,
                  color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: Button(
            text: "Download Codes",
            onPress: controller.downloadRecoveryCodes,
            bgColor: IronLockerColors.blue02,
            fgColor: Colors.white,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Container(
            height: 48,
            decoration: BoxDecoration(
              color: IronLockerColors.getThemeCard(Get.isDarkMode),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: IronLockerColors.blue02.withValues(alpha: 0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(
                    alpha: Get.isDarkMode ? 0.2 : 0.05,
                  ),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  copyTextToClipboard(recoveryCodes.join('\n'));
                  ToastHelper.success("All recovery codes copied to clipboard");
                },
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.copy_all,
                        size: 18,
                        color: IronLockerColors.blue02,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        "Copy All",
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Jura',
                          color: IronLockerColors.blue02,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImportantNotice() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning_amber, color: Colors.orange, size: 20),
                const SizedBox(width: 8),
                Text(
                  "Important",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Jura',
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            _buildNoticeItem("Each recovery code can only be used once"),
            _buildNoticeItem(
              "Store these codes in a secure location (password manager, safe, etc.)",
            ),
            _buildNoticeItem("Don't share these codes with anyone"),
            _buildNoticeItem(
              "Generate new codes if you suspect they've been compromised",
              isLast: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoticeItem(String text, {bool isLast = false}) {
    return Padding(
      padding: EdgeInsets.only(bottom: isLast ? 0 : 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 4,
            height: 4,
            margin: const EdgeInsets.only(top: 8, right: 8),
            decoration: BoxDecoration(
              color: Colors.orange,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Jura',
                color: Colors.orange[800],
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
