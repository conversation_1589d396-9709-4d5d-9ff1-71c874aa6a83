import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/something_went_wrong.dart';
import 'package:ironlocker/app/constants/colors.dart';

import '../controllers/loading_controller.dart';

class LoadingView extends GetView<LoadingController> {
  const LoadingView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(
        () => Center(
          child: controller.error.isEmpty
              ? const SizedBox(
                  height: 50,
                  width: 50,
                  child: CircularProgressIndicator(
                    color: AppColors.primaryColor,
                  ),
                )
              : SomethingWentWrong(retry: controller.getData),
        ),
      ),
    );
  }
}
