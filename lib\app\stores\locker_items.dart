import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/decryption.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/locker.dart';

class LockerItemsStore extends GetxController {
  final lockerItems = [].obs;
  final contacts = [].obs;
  final addresses = [].obs;
  final accounts = [].obs;
  final bankAccounts = [].obs;
  final paymentCards = [].obs;
  final notes = [].obs;
  final folders = [].obs;
  final favourites = [].obs;

  static LockerItemsStore get to => Get.find();
  final lockerService = Get.find<LockerService>();

  @override
  void onInit() {
    super.onInit();

    ever(lockerItems, (callback) {
      updateContacts();
      updateAddresses();
      updateBankAccounts();
      updateFolders();
      updateNotes();
      updateFavourites();
      updateAccounts();
      updatePaymentCards();
    });
  }

  updateLockerItems(List items) {
    lockerItems(items);
  }

  updateContacts() {
    contacts(
      lockerItems.where((item) => item['itemType'] == "Contact").toList(),
    );
  }

  updateAddresses() {
    addresses(
      lockerItems.where((item) => item['itemType'] == "Address").toList(),
    );
  }

  updateBankAccounts() {
    bankAccounts(
      lockerItems.where((item) => item['itemType'] == "BankAccount").toList(),
    );
  }

  updatePaymentCards() {
    paymentCards(
      lockerItems.where((item) => item['itemType'] == "PaymentCard").toList(),
    );
  }

  updateNotes() {
    notes(lockerItems.where((item) => item['itemType'] == "Note").toList());
  }

  updateAccounts() {
    accounts(
      lockerItems.where((item) => item['itemType'] == "Account").toList(),
    );
  }

  updateFolders() {
    folders(lockerItems.where((item) => item['itemType'] == "Folder").toList());
  }

  updateFavourites() {
    favourites(lockerItems.where((item) => item['favourite']).toList());
  }

  addLockerItem(Map item) {
    lockerItems.add(item);
  }

  updateLockerItem(Map item) {
    int itemIndex = lockerItems.indexWhere((map) => map['_id'] == item['_id']);

    if (itemIndex == -1) return;

    lockerItems[itemIndex] = item;
  }

  getLockerItem(String id) {
    return lockerItems.firstWhereOrNull((item) => item["_id"] == id);
  }

  removeLockerItem(String id) {
    lockerItems.removeWhere((item) => item["_id"] == id);
  }

  removeLockerItems(List items) {
    lockerItems.removeWhere((item) => items.contains(item["_id"]));
  }

  addItem(dynamic data) {
    lockerItems.value = [data, ...lockerItems];
  }

  markFavourite(List items) {
    lockerItems.value =
        lockerItems.map((lockerItem) {
          if (items.contains(lockerItem['_id'])) {
            return {...lockerItem, "favourite": true};
          }

          return lockerItem;
        }).toList();
  }

  unmarkFavourite(List items) {
    lockerItems.value =
        lockerItems.map((lockerItem) {
          if (items.contains(lockerItem['_id'])) {
            return {...lockerItem, "favourite": false};
          }

          return lockerItem;
        }).toList();
  }

  Future fetchItems() async {
    final resp = await lockerService.getLockerItems();

    if (resp.hasError) {
      if (resp.body?["error"] == "SESSION_LOCKED") {
        ToastHelper.error("Session is locked");
        Get.offAllNamed(Routes.lockedLocker);
      } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
          resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
        Get.offAllNamed(Routes.signin);
      } else {
        ToastHelper.error("Something went wrong");
      }
      return;
    }

    var data = await DecryptionHelper.decryptDynamicData(resp.body);
    lockerItems(data);
  }
}
