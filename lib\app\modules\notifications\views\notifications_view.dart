import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:get/get.dart';
import 'package:ironlocker/app/components/locker_app_bar.dart';
import 'package:ironlocker/app/components/something_went_wrong.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/modules/notifications/models/notification.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/themes/app_bar.dart';

import '../controllers/notifications_controller.dart';

class NotificationsView extends GetView<NotificationsController> {
  const NotificationsView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: LockerAppBar(
        title: "Notifications",
        page: "notifications",
        showSearchIcon: false,
        showAddLockerItemButton: false,
        leading: AppbarTheme.buildStyledBackButton(
          onPressed: () {
            Get.toNamed(Routes.home);
          },
          tooltip: 'Back to Home',
        ),
        actions: [
          TextButton(
            onPressed: () {
              DialogHelper.confirmDialog(
                title:
                    "Are you sure you want to mark all notifications as read?",
                onConfirm: controller.markAllAsRead,
              );
            },
            child: Text(
              "Mark all as read",
              style: TextStyle(color: Colors.white, fontSize: 14),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Notifications Center',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              Gap(5),
              Text(
                'View all your notifications below.',
                style: TextStyle(fontSize: 15),
              ),
              Gap(30),

              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async {
                    await controller.fetchNotifications();
                  },
                  color: Colors.blue,
                  backgroundColor: Get.isDarkMode ? null : Colors.white,
                  child: Obx(
                    () =>
                        controller.loading.isTrue
                            ? const Center(
                              child: SizedBox(
                                height: 50,
                                width: 50,
                                child: CircularProgressIndicator(
                                  color: Colors.blue,
                                ),
                              ),
                            )
                            : controller.error.isNotEmpty
                            ? SomethingWentWrong(
                              retry: controller.fetchNotifications,
                            )
                            : controller.notifications.isEmpty
                            ? Text("No Notifications")
                            : ListView.builder(
                              padding: EdgeInsets.symmetric(vertical: 10),
                              itemCount: controller.notifications.length,
                              itemBuilder: (BuildContext context, int index) {
                                var notification = NotificationData.fromJson(
                                  controller.notifications[index],
                                );
                                return _buildNotificationWidget(notification);
                              },
                            ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Container _buildNotificationWidget(NotificationData notification) {
    DateTime createdAt = DateTime.parse(notification.createdAt);
    String ago = timeago.format(createdAt);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
      margin: EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: const Color(0xffF5F7FA),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_outlined,
            size: 40,
            color: Color(0xff6C7A89),
          ),
          Gap(10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  notification.title,
                  style: TextStyle(fontSize: 15, fontWeight: FontWeight.bold),
                ),
                Gap(2),
                Text(
                  notification.message,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(fontSize: 14),
                ),
                Gap(10),
                Text(
                  ago,
                  style: TextStyle(
                    color: Color(0xff6C7A89),
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Gap(10),
          Container(
            height: 33,
            width: 33,
            decoration: BoxDecoration(
              color: IronLockerColors.blue02,
              shape: BoxShape.circle,
            ),
            child: Icon(Icons.check, color: Colors.white, size: 18),
          ),
        ],
      ),
    );
  }
}
