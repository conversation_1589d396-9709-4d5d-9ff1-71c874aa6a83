import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:ironlocker/app/constants/colors.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';

class Button extends StatefulWidget {
  const Button({
    super.key,
    required this.text,
    required this.onPress,
    this.width,
    this.height,
    this.loading = false,
    this.disabled = false,
    this.bgColor,
    this.borderColor,
    this.fgColor,
  });
  final String text;
  final double? height;
  final double? width;
  final bool loading;
  final bool disabled;
  final Color? bgColor;
  final Color? fgColor;
  final Color? borderColor;
  final VoidCallback onPress;

  @override
  State<Button> createState() => _ButtonState();
}

class _ButtonState extends State<Button> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.loading || widget.disabled ? null : widget.onPress,
      child: Container(
        height: widget.height ?? 45,
        width: widget.width ?? double.infinity,
        alignment: const Alignment(0, 0),
        padding: const EdgeInsets.symmetric(horizontal: 10),
        decoration: BoxDecoration(
          color:
              widget.loading || widget.disabled
                  ? (widget.bgColor ?? IronLockerColors.blue02).withValues(
                    alpha: 0.75,
                  )
                  : widget.bgColor ?? IronLockerColors.blue02,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: widget.borderColor ?? Colors.transparent),
          boxShadow:
              widget.loading || widget.disabled
                  ? null
                  : [
                    BoxShadow(
                      color: (widget.bgColor ?? IronLockerColors.blue02)
                          .withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
        ),
        child:
            widget.loading
                ? const SpinKitThreeBounce(color: Colors.white, size: 30.0)
                : Text(
                  widget.text,
                  style: Theme.of(context).textTheme.labelMedium!.copyWith(
                    color: widget.fgColor ?? Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Jura',
                  ),
                ),
      ),
    );
  }
}
