import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/bottom%20sheets/add_locker_item.dart';
import 'package:ironlocker/app/components/bottom%20sheets/address_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/baccount_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/contact_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/folder_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/account_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/note_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/pcard_form.dart';
import 'package:ironlocker/app/helpers/decryption.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/helpers.dart';
import 'package:ironlocker/app/helpers/secure_encryption.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/modules/shared-locker/models/shared_locker_member.dart';
import 'package:ironlocker/app/modules/shared-lockers/models/shared_locker.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/shared_locker.dart';
import 'package:ironlocker/app/stores/shared_lockers.dart';
import 'package:ironlocker/app/stores/user.dart';

class SharedLockerController extends GetxController {
  final searchedItems = [].obs;
  final searching = false.obs;
  final searchController = TextEditingController();
  var sharedLockersStore = Get.find<SharedLockersStore>();
  var sharedLockerService = Get.find<SharedLockerService>();
  late Rx<SharedLockerData> sharedLockerData;
  var sharedLockerItems = RxList();
  var folders = RxList();
  late RxList<SharedLockerMember> sharedLockerMembers;

  var error = "".obs;
  var encryptionKey = "".obs;
  var originalKey = "".obs;

  final loading = true.obs;

  @override
  void onInit() async {
    sharedLockerData =
        sharedLockersStore.getSharedLocker(Get.parameters["id"]!).obs;
    sharedLockerMembers = sharedLockerData.value.members.obs;

    ever(sharedLockerItems, (items) {
      folders =
          sharedLockerItems
              .where((item) => item["itemType"] == "Folder")
              .toList()
              .obs;
    });

    await fetchSharedLockerKey();
    await fetchSharedLockerItems();

    loading(false);

    searchController.addListener(() {
      if (searchController.text.isEmpty) {
        searching(false);
        return;
      }

      if (searching.isFalse) {
        searching(true);
      }

      searchedItems.value =
          sharedLockerItems.where((item) {
            String itemName = item['name'];

            return itemName.toLowerCase().contains(
              searchController.text.toLowerCase(),
            );
          }).toList();
    });

    ever(SharedLockersStore.to.sharedLockers, (_) {
      sharedLockerData =
          sharedLockersStore.getSharedLocker(sharedLockerData.value.id).obs;
      sharedLockerMembers = sharedLockerData.value.members.obs;
    });

    super.onInit();
  }

  Future<void> fetchSharedLockerItems() async {
    if (error.isNotEmpty) loading(true);
    error("");

    var resp = await sharedLockerService.getSharedsharedLockerItems(
      Get.parameters["id"]!,
    );

    if (resp.hasError) {
      handleErrors(resp);
      return;
    }

    if (encryptionKey.value.isEmpty) {
      ToastHelper.error("Failed to load encryption key.");
      return;
    }

    final items = await DecryptionHelper.decryptDynamicData(
      resp.body["items"],
      encryptionKey.value,
    );

    loading(false);
    sharedLockerItems(items);
  }

  Future fetchSharedLockerKey() async {
    try {
      error("");
      var resp = await sharedLockerService.getSharedsharedLockerKey(
        Get.parameters["id"]!,
      );

      if (resp.hasError) {
        handleErrors(resp);
        return;
      }

      final keys = await SecureEncryptionHelper.generateSharedLockerKey(
        resp.body["key"],
      );

      encryptionKey.value = keys["encryptionKey"];
      originalKey.value = keys["originalKey"];
    } catch (e) {
      error("Something went wrong");

      loading(false);
    }
  }

  Future deleteLocker() async {
    Get.back();
    DialogHelper.loading();
    var resp = await sharedLockerService.deleteSharedLocker(
      sharedLockerData.value.id,
    );

    if (resp.hasError) {
      if (resp.body?["error"] == "SESSION_LOCKED") {
        Get.offAllNamed(Routes.lockedLocker);
      } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
          resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
        await Helpers.clearData();
        Get.offAllNamed(Routes.signin);
      } else {
        Get.back();
        ToastHelper.error("Something went wrong");
      }
      return;
    }

    sharedLockersStore.removeSharedLocker(sharedLockerData.value.id);

    Get.offAllNamed(Routes.SHARED_LOCKERS);
  }

  clearSearchText() {
    searchController.clear();
  }

  bool get hasWritePermission {
    if (UserStore.to.id.value == sharedLockerData.value.creator) {
      return true;
    }

    var member = sharedLockerData.value.members.firstWhere(
      (element) => element.id == UserStore.to.id.value,
    );

    if (member.permission != "READ_WRITE") {
      return false;
    }

    return true;
  }

  addSharedLockerItem(Map item) {
    if (item["folder"] != null) return;
    sharedLockerItems.value = [item, ...sharedLockerItems];
  }

  void handleErrors(var resp) {
    if (resp.body?["error"] == "SESSION_LOCKED") {
      Get.offAllNamed(Routes.lockedLocker);
    } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
        resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
      Helpers.clearData();
      Get.offAllNamed(Routes.signin);
    } else {
      if (loading.isTrue) {
        error("Something went wrong");
      } else {
        ToastHelper.error("Something went wrong");
      }
    }
    loading(false);
  }

  addLockerItem(BuildContext context, String sharedLockerId) {
    addLockerItemSheet(
      context: context,
      onPress: (itemType) {
        Navigator.of(context).pop();

        switch (itemType) {
          case "Note":
            noteFormSheet(
              context: context,
              folders: folders,
              sharedLockerId: sharedLockerId,
              onCreated: addSharedLockerItem,
              encryptionKey: encryptionKey.value,
            );
            break;
          case "Contact":
            contactFormSheet(
              context: context,
              folders: folders,
              sharedLockerId: sharedLockerId,
              onCreated: addSharedLockerItem,
              encryptionKey: encryptionKey.value,
            );
            break;
          case "Address":
            addressFormSheet(
              context: context,
              folders: folders,
              sharedLockerId: sharedLockerId,
              onCreated: addSharedLockerItem,
              encryptionKey: encryptionKey.value,
            );
            break;
          case "Account":
            accountFormSheet(
              context: context,
              folders: folders,
              sharedLockerId: sharedLockerId,
              onCreated: addSharedLockerItem,
              encryptionKey: encryptionKey.value,
            );
            break;
          case "Folder":
            folderFormSheet(
              context: context,
              folders: folders,
              sharedLockerId: sharedLockerId,
              onCreated: addSharedLockerItem,
              encryptionKey: encryptionKey.value,
            );
            break;
          case "PaymentCard":
            paymentCardFormSheet(
              context: context,
              folders: folders,
              sharedLockerId: sharedLockerId,
              onCreated: addSharedLockerItem,
              encryptionKey: encryptionKey.value,
            );
            break;
          default:
            bankAccountFormSheet(
              context: context,
              folders: folders,
              sharedLockerId: sharedLockerId,
              onCreated: addSharedLockerItem,
              encryptionKey: encryptionKey.value,
            );
        }
      },
    );
  }
}
