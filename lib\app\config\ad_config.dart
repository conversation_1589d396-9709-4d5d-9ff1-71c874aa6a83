/// Configuration class for AdMob settings and production optimizations
class AdConfig {
  // Ad loading timeouts
  static const int adLoadTimeoutSeconds = 10;
  static const int maxRetryAttempts = 5;
  static const int retryDelaySeconds = 2;
  
  // Ad refresh intervals (in seconds)
  static const int bannerRefreshInterval = 60; // 1 minute
  static const int maxRefreshAttempts = 3;
  
  // Production optimizations
  static const bool enableAdPreloading = true;
  static const bool enableAdCaching = true;
  static const bool enableProgressiveRetry = true;
  
  // Error handling
  static const bool showErrorPlaceholders = true;
  static const bool enableManualRetry = true;
  static const bool logAdEvents = true;
  
  // Device compatibility
  static const List<String> problematicDeviceModels = [
    // Add device models that have known WebView issues
    // Example: 'SM-G973F', 'Pixel 3'
  ];
  
  // Network error codes that should trigger retry
  static const List<int> retryableErrorCodes = [
    0, // No fill
    1, // Invalid request  
    2, // Network error
    3, // No fill
  ];
  
  // Keywords for better ad targeting
  static const List<String> adKeywords = [
    'security',
    'privacy',
    'password',
    'vault',
    'encryption',
    'protection',
    'mobile',
    'app',
    'utility',
    'productivity',
    'business',
    'professional',
  ];
  
  // Ad unit refresh strategies
  static const Map<String, int> refreshStrategies = {
    'banner': 60,      // 1 minute
    'interstitial': 300, // 5 minutes
    'rewarded': 600,   // 10 minutes
  };
}
