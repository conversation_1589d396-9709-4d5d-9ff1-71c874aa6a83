import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:url_launcher/url_launcher.dart';

class ErrorHelper {
  /// Display a user-friendly error message using SnackBar
  static void showError(dynamic error, {String? title}) {
    // Check for special account deletion case
    if (_isAccountDeletionInProgress(error)) {
      _showAccountDeletionDialog();
      return;
    }

    final errorMessage = _parseError(error);
    final errorTitle = title ?? _getErrorTitle(error);

    Get.snackbar(
      errorTitle,
      errorMessage,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.red.shade50,
      colorText: Colors.red.shade800,
      borderColor: Colors.red.shade200,
      borderWidth: 1,
      borderRadius: 12,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      duration: const Duration(seconds: 4),
      isDismissible: true,
      dismissDirection: DismissDirection.horizontal,
      forwardAnimationCurve: Curves.easeOutBack,
      reverseAnimationCurve: Curves.easeInBack,
      icon: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.red.shade100,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(Icons.error_outline, color: Colors.red.shade600, size: 24),
      ),
      shouldIconPulse: false,
      mainButton: TextButton(
        onPressed: () => Get.closeCurrentSnackbar(),
        child: Text(
          'Dismiss',
          style: TextStyle(
            color: Colors.red.shade600,
            fontWeight: FontWeight.w600,
            fontFamily: 'Jura',
          ),
        ),
      ),
    );
  }

  /// Display a success message using SnackBar
  static void showSuccess(String message, {String? title}) {
    Get.snackbar(
      title ?? 'Success',
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.green.shade50,
      colorText: Colors.green.shade800,
      borderColor: Colors.green.shade200,
      borderWidth: 1,
      borderRadius: 12,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      duration: const Duration(seconds: 3),
      isDismissible: true,
      dismissDirection: DismissDirection.horizontal,
      forwardAnimationCurve: Curves.easeOutBack,
      reverseAnimationCurve: Curves.easeInBack,
      icon: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.green.shade100,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.check_circle_outline,
          color: Colors.green.shade600,
          size: 24,
        ),
      ),
      shouldIconPulse: false,
    );
  }

  /// Display a warning message using SnackBar
  static void showWarning(String message, {String? title}) {
    Get.snackbar(
      title ?? 'Warning',
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.orange.shade50,
      colorText: Colors.orange.shade800,
      borderColor: Colors.orange.shade200,
      borderWidth: 1,
      borderRadius: 12,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      duration: const Duration(seconds: 3),
      isDismissible: true,
      dismissDirection: DismissDirection.horizontal,
      forwardAnimationCurve: Curves.easeOutBack,
      reverseAnimationCurve: Curves.easeInBack,
      icon: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.orange.shade100,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.warning_amber_outlined,
          color: Colors.orange.shade600,
          size: 24,
        ),
      ),
      shouldIconPulse: false,
    );
  }

  /// Parse error from various formats into user-friendly message
  static String _parseError(dynamic error) {
    if (error == null) {
      return 'An unexpected error occurred. Please try again.';
    }

    // If it's already a string, check if it needs formatting
    if (error is String) {
      return _formatErrorMessage(error);
    }

    // If it's a Map (typical backend response)
    if (error is Map<String, dynamic>) {
      // Check for common error fields
      if (error.containsKey('error')) {
        return _formatErrorMessage(error['error'].toString());
      }
      if (error.containsKey('message')) {
        return _formatErrorMessage(error['message'].toString());
      }
      if (error.containsKey('errors')) {
        // Handle validation errors
        final errors = error['errors'];
        if (errors is List) {
          return errors
              .map((e) => _formatErrorMessage(e.toString()))
              .join('\n');
        }
        if (errors is Map) {
          return errors.values
              .map((e) => _formatErrorMessage(e.toString()))
              .join('\n');
        }
      }
    }

    // Fallback for any other type
    return _formatErrorMessage(error.toString());
  }

  /// Format error message to be more user-friendly
  static String _formatErrorMessage(String message) {
    // Remove technical prefixes
    message = message.replaceAll(
      RegExp(r'^(Error:|Exception:|HTTP \d+:)\s*'),
      '',
    );

    // Handle common backend error patterns
    final errorMappings = {
      'invalid_credentials':
          'Invalid email or password. Please check your credentials and try again.',
      'user_not_found': 'No account found with this email address.',
      'invalid_password': 'The password you entered is incorrect.',
      'invalid_email': 'Please enter a valid email address.',
      'email_already_exists': 'An account with this email already exists.',
      'account_locked':
          'Your account has been temporarily locked. Please try again later.',
      'too_many_attempts':
          'Too many failed attempts. Please wait before trying again.',
      'network_error':
          'Network connection error. Please check your internet connection.',
      'server_error':
          'Server is temporarily unavailable. Please try again later.',
      'validation_failed': 'Please check your input and try again.',
      'unauthorized': 'You are not authorized to perform this action.',
      'forbidden':
          'Access denied. You don\'t have permission to perform this action.',
      'not_found': 'The requested resource was not found.',
      'timeout': 'Request timed out. Please try again.',
    };

    // Check for exact matches first
    final lowerMessage = message.toLowerCase();
    for (final entry in errorMappings.entries) {
      if (lowerMessage.contains(entry.key)) {
        return entry.value;
      }
    }

    // Capitalize first letter and ensure proper punctuation
    if (message.isNotEmpty) {
      message = message[0].toUpperCase() + message.substring(1);
      if (!message.endsWith('.') &&
          !message.endsWith('!') &&
          !message.endsWith('?')) {
        message += '.';
      }
    }

    return message.isNotEmpty
        ? message
        : 'An unexpected error occurred. Please try again.';
  }

  /// Get appropriate error title based on error type
  static String _getErrorTitle(dynamic error) {
    if (error is Map<String, dynamic>) {
      final errorString = error['error']?.toString().toLowerCase() ?? '';

      if (errorString.contains('credential') ||
          errorString.contains('password') ||
          errorString.contains('login')) {
        return 'Sign In Failed';
      }
      if (errorString.contains('network') ||
          errorString.contains('connection')) {
        return 'Connection Error';
      }
      if (errorString.contains('validation') ||
          errorString.contains('invalid')) {
        return 'Invalid Input';
      }
      if (errorString.contains('server') || errorString.contains('internal')) {
        return 'Server Error';
      }
    }

    return 'Error';
  }

  /// Show error dialog for critical errors
  static void showErrorDialog(
    dynamic error, {
    String? title,
    VoidCallback? onRetry,
  }) {
    final errorMessage = _parseError(error);
    final errorTitle = title ?? _getErrorTitle(error);

    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.error_outline,
                color: Colors.red.shade600,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                errorTitle,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.red.shade800,
                  fontFamily: 'Jura',
                ),
              ),
            ),
          ],
        ),
        content: Text(
          errorMessage,
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade700,
            fontFamily: 'Jura',
            height: 1.4,
          ),
        ),
        actions: [
          if (onRetry != null)
            TextButton(
              onPressed: () {
                Get.back();
                onRetry();
              },
              child: Text(
                'Retry',
                style: TextStyle(
                  color: IronLockerColors.blue02,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Jura',
                ),
              ),
            ),
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'OK',
              style: TextStyle(
                color: Colors.red.shade600,
                fontWeight: FontWeight.w600,
                fontFamily: 'Jura',
              ),
            ),
          ),
        ],
      ),
      barrierDismissible: true,
    );
  }

  /// Check if error is account deletion in progress
  static bool _isAccountDeletionInProgress(dynamic error) {
    if (error is String) {
      return error.toUpperCase().contains('ACCOUNT_DELETION_IN_PROGRESS');
    }
    if (error is Map<String, dynamic>) {
      final errorString = error['error']?.toString().toUpperCase() ?? '';
      return errorString.contains('ACCOUNT_DELETION_IN_PROGRESS');
    }
    return false;
  }

  /// Show special dialog for account deletion in progress
  static void _showAccountDeletionDialog() {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: Get.width > 600 ? 500 : Get.width * 0.9,
            maxHeight: Get.height * 0.8,
          ),
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.all(Get.width > 600 ? 32 : 24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Responsive header
                  _buildResponsiveHeader(),

                  SizedBox(height: Get.width > 600 ? 24 : 20),

                  // Main content
                  Text(
                    'Your account is currently scheduled for deletion. This process will permanently remove all your data, including:',
                    style: TextStyle(
                      fontSize: Get.width > 600 ? 16 : 15,
                      color: Colors.grey.shade700,
                      fontFamily: 'Jura',
                      height: 1.5,
                    ),
                  ),

                  SizedBox(height: Get.width > 600 ? 16 : 12),

                  // Bullet points with responsive padding
                  Padding(
                    padding: EdgeInsets.only(left: Get.width > 600 ? 20 : 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildResponsiveBulletPoint(
                          'All locker items and folders',
                        ),
                        _buildResponsiveBulletPoint(
                          'Account settings and preferences',
                        ),
                        _buildResponsiveBulletPoint(
                          'Shared lockers and collaborations',
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: Get.width > 600 ? 20 : 16),

                  // Info box with responsive design
                  _buildResponsiveInfoBox(),

                  SizedBox(height: Get.width > 600 ? 32 : 24),

                  // Responsive action buttons
                  _buildResponsiveActionButtons(),
                ],
              ),
            ),
          ),
        ),
      ),
      barrierDismissible: true,
    );
  }

  /// Helper method to create bullet points
  static Widget _buildBulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade600,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
                fontFamily: 'Jura',
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build responsive header for the dialog
  static Widget _buildResponsiveHeader() {
    final isTablet = Get.width > 600;

    return Center(
      child: Text(
        'Account Deletion in Progress',
        style: TextStyle(
          fontSize: isTablet ? 22 : 20,
          fontWeight: FontWeight.w600,
          color: Colors.orange.shade800,
          fontFamily: 'Jura',
        ),
        textAlign: TextAlign.center,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Build responsive bullet point
  static Widget _buildResponsiveBulletPoint(String text) {
    final isTablet = Get.width > 600;

    return Padding(
      padding: EdgeInsets.only(bottom: isTablet ? 8 : 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(top: isTablet ? 9 : 8),
            width: isTablet ? 5 : 4,
            height: isTablet ? 5 : 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade600,
              borderRadius: BorderRadius.circular(isTablet ? 2.5 : 2),
            ),
          ),
          SizedBox(width: isTablet ? 16 : 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: isTablet ? 15 : 14,
                color: Colors.grey.shade700,
                fontFamily: 'Jura',
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build responsive info box
  static Widget _buildResponsiveInfoBox() {
    final isTablet = Get.width > 600;

    return Container(
      padding: EdgeInsets.all(isTablet ? 20 : 16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(isTablet ? 16 : 12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.blue.shade600,
            size: isTablet ? 24 : 20,
          ),
          SizedBox(width: isTablet ? 16 : 12),
          Expanded(
            child: Text(
              'You can cancel this deletion process if you want to keep your account.',
              style: TextStyle(
                fontSize: isTablet ? 15 : 14,
                color: Colors.blue.shade700,
                fontFamily: 'Jura',
                fontWeight: FontWeight.w500,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build responsive action buttons
  static Widget _buildResponsiveActionButtons() {
    final isTablet = Get.width > 600;
    final isSmallScreen = Get.width < 400;

    if (isSmallScreen) {
      // Stack buttons vertically on very small screens
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          ElevatedButton(
            onPressed: () {
              Get.back();
              _openCancelDeletionUrl();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: IronLockerColors.blue02,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
              minimumSize: const Size(double.infinity, 48),
            ),
            child: Text(
              'Cancel Deletion',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontFamily: 'Jura',
                fontSize: 16,
              ),
            ),
          ),
          const SizedBox(height: 12),
          TextButton(
            onPressed: () => Get.back(),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              minimumSize: const Size(double.infinity, 48),
            ),
            child: Text(
              'Close',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w600,
                fontFamily: 'Jura',
                fontSize: 16,
              ),
            ),
          ),
        ],
      );
    } else {
      // Horizontal layout for larger screens
      return Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: () => Get.back(),
            style: TextButton.styleFrom(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 24 : 20,
                vertical: isTablet ? 16 : 12,
              ),
            ),
            child: Text(
              'Close',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w600,
                fontFamily: 'Jura',
                fontSize: isTablet ? 16 : 15,
              ),
            ),
          ),
          SizedBox(width: isTablet ? 16 : 12),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _openCancelDeletionUrl();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: IronLockerColors.blue02,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 32 : 24,
                vertical: isTablet ? 16 : 12,
              ),
            ),
            child: Text(
              'Cancel Deletion',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontFamily: 'Jura',
                fontSize: isTablet ? 16 : 15,
              ),
            ),
          ),
        ],
      );
    }
  }

  /// Open the cancel deletion URL
  static void _openCancelDeletionUrl() async {
    try {
      final url = Uri.parse('https://www.ironlocker.app/cancel-deletion');

      // Show loading message
      Get.snackbar(
        'Opening Browser',
        'Redirecting to cancel deletion page...',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.blue.shade50,
        colorText: Colors.blue.shade800,
        borderColor: Colors.blue.shade200,
        borderWidth: 1,
        borderRadius: 12,
        margin: const EdgeInsets.all(16),
        duration: const Duration(seconds: 2),
        icon: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.open_in_browser,
            color: Colors.blue.shade600,
            size: 20,
          ),
        ),
      );

      // Try to launch the URL
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        // If can't launch, show the URL in a dialog
        _showUrlDialog();
      }
    } catch (e) {
      // If any error occurs, show the URL in a dialog
      _showUrlDialog();
    }
  }

  /// Show URL in a dialog if browser can't be opened
  static void _showUrlDialog() {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: Get.width > 600 ? 400 : Get.width * 0.9,
            maxHeight: Get.height * 0.6,
          ),
          child: Padding(
            padding: EdgeInsets.all(Get.width > 600 ? 32 : 24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Cancel Account Deletion',
                  style: TextStyle(
                    fontSize: Get.width > 600 ? 20 : 18,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Jura',
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: Get.width > 600 ? 24 : 20),
                Text(
                  'Please visit the following URL to cancel your account deletion:',
                  style: TextStyle(
                    fontSize: Get.width > 600 ? 16 : 15,
                    fontFamily: 'Jura',
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: Get.width > 600 ? 20 : 16),
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(Get.width > 600 ? 16 : 12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(
                      Get.width > 600 ? 12 : 8,
                    ),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: SelectableText(
                    'https://www.ironlocker.app/cancel-deletion',
                    style: TextStyle(
                      fontSize: Get.width > 600 ? 15 : 14,
                      fontFamily: 'monospace',
                      color: Colors.blue.shade700,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(height: Get.width > 600 ? 32 : 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Get.back(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: IronLockerColors.blue02,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: EdgeInsets.symmetric(
                        vertical: Get.width > 600 ? 16 : 14,
                      ),
                    ),
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Jura',
                        fontSize: Get.width > 600 ? 16 : 15,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
