import 'package:get/get.dart';

import '../modules/accounts/bindings/accounts_bindings.dart';
import '../modules/accounts/views/accounts_view.dart';
import '../modules/addresses/bindings/addresses_bindings.dart';
import '../modules/addresses/views/addresses_view.dart';
import '../modules/baccounts/bindings/baccounts_bindings.dart';
import '../modules/baccounts/views/baccounts_view.dart';
import '../modules/complete2fa-app/bindings/complete2fa_app_binding.dart';
import '../modules/complete2fa-app/views/complete2fa_app_view.dart';
import '../modules/complete2fa-pin/bindings/complete2fa_pin_binding.dart';
import '../modules/complete2fa-pin/views/complete2fa_pin_view.dart';
import '../modules/complete2fa-question/bindings/complete2fa_question_binding.dart';
import '../modules/complete2fa-question/views/complete2fa_question_view.dart';
import '../modules/complete2fa-recovery-code/bindings/complete2fa_recovery_code_binding.dart';
import '../modules/complete2fa-recovery-code/views/complete2fa_recovery_code_view.dart';
import '../modules/contacts/bindings/contact_bindings.dart';
import '../modules/contacts/views/contacts_view.dart';
import '../modules/create_encryption_key/bindings/create_encryption_key_binding.dart';
import '../modules/create_encryption_key/views/create_encryption_key_view.dart';
import '../modules/favourites/bindings/favourites_bindings.dart';
import '../modules/favourites/views/favourites_view.dart';
import '../modules/folder/bindings/folder_bindings.dart';
import '../modules/folder/views/folder_view.dart';
import '../modules/folders/bindings/folders_bindings.dart';
import '../modules/folders/views/folders_view.dart';
import '../modules/home/<USER>/home_binding.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/loading/bindings/loading_binding.dart';
import '../modules/loading/views/loading_view.dart';
import '../modules/local_auth/bindings/local_auth_binding.dart';
import '../modules/local_auth/views/local_auth_view.dart';
import '../modules/locked_locker/binding/index.dart';
import '../modules/locked_locker/views/index.dart';
import '../modules/notes/bindings/notes_bindings.dart';
import '../modules/notes/views/notes_view.dart';
import '../modules/notifications/bindings/notifications_binding.dart';
import '../modules/notifications/views/notifications_view.dart';
import '../modules/onboarding/bindings/onboarding_binding.dart';
import '../modules/onboarding/views/onboarding_view.dart';
import '../modules/pcards/bindings/pcards_bindings.dart';
import '../modules/pcards/views/pcards_view.dart';
import '../modules/profile/bindings/profile_binding.dart';
import '../modules/profile/views/profile_view.dart';
import '../modules/recovery_codes/bindings/recovery_codes_binding.dart';
import '../modules/recovery_codes/views/recovery_codes_view.dart';
import '../modules/scan_qrcode/bindings/scan_qrcode_binding.dart';
import '../modules/scan_qrcode/views/scan_qrcode_view.dart';
import '../modules/security/bindings/security_binding.dart';
import '../modules/security/views/security_view.dart';
import '../modules/session-detail/bindings/session_detail_binding.dart';
import '../modules/session-detail/views/session_detail_view.dart';
import '../modules/sessions/bindings/sessions_binding.dart';
import '../modules/sessions/views/sessions_view.dart';
import '../modules/settings/binding/index.dart';
import '../modules/settings/views/index.dart';
import '../modules/shared-folder/bindings/shared_folder_binding.dart';
import '../modules/shared-folder/views/shared_folder_view.dart';
import '../modules/shared-locker/bindings/shared_locker_binding.dart';
import '../modules/shared-locker/views/shared_locker_view.dart';
import '../modules/shared-lockers/bindings/shared_lockers_binding.dart';
import '../modules/shared-lockers/views/shared_lockers_view.dart';
import '../modules/signin/binding/index.dart';
import '../modules/signin/views/index.dart';
import '../modules/signup_completed/bindings/signup_completed_binding.dart';
import '../modules/signup_completed/views/signup_completed_view.dart';
import '../modules/two_factor_app/bindings/two_factor_app_binding.dart';
import '../modules/two_factor_app/views/two_factor_app_view.dart';
import '../modules/two_factor_methods/bindings/two_factor_methods_binding.dart';
import '../modules/two_factor_methods/views/two_factor_methods_view.dart';
import '../modules/two_factor_pin/bindings/two_factor_pin_binding.dart';
import '../modules/two_factor_pin/views/two_factor_pin_view.dart';
import '../modules/two_factor_question/bindings/two_factor_question_binding.dart';
import '../modules/two_factor_question/views/two_factor_question_view.dart';
import '../modules/update_password/bindings/update_password_binding.dart';
import '../modules/update_password/views/update_password_view.dart';
import '../modules/verify_encryption_key/bindings/verify_encryption_key_binding.dart';
import '../modules/verify_encryption_key/views/verify_encryption_key_view.dart';
import '../modules/welcome/bindings/welcome_binding.dart';
import '../modules/welcome/views/welcome_view.dart';

part "app_routes.dart";

class AppPages {
  static final pages = [
    GetPage(
      name: Routes.home,
      page: () {
        return const HomePage();
      },
      binding: HomeBinding(),
    ),
    GetPage(
      name: Routes.favourites,
      page: () {
        return const FavouritesPage();
      },
      binding: FavouritesBinding(),
    ),
    GetPage(
      name: Routes.contacts,
      page: () {
        return const ContactsPage();
      },
      binding: ContactsBinding(),
    ),
    GetPage(
      name: Routes.folders,
      page: () {
        return const FoldersPage();
      },
      binding: FoldersBinding(),
    ),
    GetPage(
      name: Routes.folder,
      page: () {
        return const FolderPage();
      },
      binding: FolderBinding(),
    ),
    GetPage(
      name: Routes.addresses,
      page: () {
        return const AddressesPage();
      },
      binding: AddressesBinding(),
    ),
    GetPage(
      name: Routes.notes,
      page: () {
        return const NotesPage();
      },
      binding: NotesBinding(),
    ),
    GetPage(
      name: Routes.accounts,
      page: () {
        return const AccountsPage();
      },
      binding: AccountsBinding(),
    ),
    GetPage(
      name: Routes.bankAccounts,
      page: () {
        return const BankAccountsPage();
      },
      binding: BankAccountsBinding(),
    ),
    GetPage(
      name: Routes.paymentCards,
      page: () {
        return const PaymentCardsPage();
      },
      binding: PaymentCardsBinding(),
    ),
    GetPage(
      name: Routes.signin,
      page: () {
        return const SignInPage();
      },
      binding: SignInBinding(),
    ),
    GetPage(
      name: Routes.lockedLocker,
      page: () {
        return const LockedLockerPage();
      },
      binding: LockedLockerBinding(),
    ),
    GetPage(
      name: Routes.settings,
      page: () {
        return const SettingsPage();
      },
      binding: SettingsBinding(),
    ),
    GetPage(
      name: Routes.LOADING,
      page: () => const LoadingView(),
      binding: LoadingBinding(),
    ),
    GetPage(
      name: Routes.UPDATE_PASSWORD,
      page: () => const UpdatePasswordView(),
      binding: UpdatePasswordBinding(),
    ),
    GetPage(
      name: Routes.UPDATE_PASSWORD,
      page: () => const UpdatePasswordView(),
      binding: UpdatePasswordBinding(),
    ),
    GetPage(
      name: Routes.PROFILE,
      page: () => const ProfileView(),
      binding: ProfileBinding(),
    ),
    GetPage(
      name: Routes.SECURITY,
      page: () => const SecurityView(),
      binding: SecurityBinding(),
    ),
    GetPage(
      name: Routes.TWO_FACTOR_METHODS,
      page: () => const TwoFactorMethodsView(),
      binding: TwoFactorMethodsBinding(),
    ),
    GetPage(
      name: Routes.TWO_FACTOR_PIN,
      page: () => const TwoFactorPinView(),
      binding: TwoFactorPinBinding(),
    ),
    GetPage(
      name: Routes.TWO_FACTOR_APP,
      page: () => const TwoFactorAppView(),
      binding: TwoFactorAppBinding(),
    ),
    GetPage(
      name: Routes.TWO_FACTOR_QUESTION,
      page: () => const TwoFactorQuestionView(),
      binding: TwoFactorQuestionBinding(),
    ),
    GetPage(
      name: Routes.TWO_FACTOR_QUESTION,
      page: () => const TwoFactorQuestionView(),
      binding: TwoFactorQuestionBinding(),
    ),
    GetPage(
      name: Routes.SCAN_QRCODE,
      page: () => const ScanQrcodeView(),
      binding: ScanQrcodeBinding(),
    ),
    GetPage(
      name: Routes.COMPLETE2FA_APP,
      page: () => const Complete2faAppView(),
      binding: Complete2faAppBinding(),
    ),
    GetPage(
      name: Routes.COMPLETE2FA_PIN,
      page: () => const Complete2faPinView(),
      binding: Complete2faPinBinding(),
    ),
    GetPage(
      name: Routes.COMPLETE2FA_QUESTION,
      page: () => const Complete2faQuestionView(),
      binding: Complete2faQuestionBinding(),
    ),
    GetPage(
      name: Routes.ONBOARDING,
      page: () => const OnboardingView(),
      binding: OnboardingBinding(),
    ),
    GetPage(
      name: Routes.SIGNUP_COMPLETED,
      page: () => const SignupCompletedView(),
      binding: SignupCompletedBinding(),
    ),
    GetPage(
      name: Routes.RECOVERY_CODES,
      page: () => const RecoveryCodesView(),
      binding: RecoveryCodesBinding(),
    ),
    GetPage(
      name: Routes.COMPLETE2FA_RECOVERY_CODE,
      page: () => const Complete2faRecoveryCodeView(),
      binding: Complete2faRecoveryCodeBinding(),
    ),
    GetPage(
      name: Routes.SESSIONS,
      page: () => const SessionsView(),
      binding: SessionsBinding(),
    ),
    GetPage(
      name: Routes.SESSION_DETAIL,
      page: () => const SessionDetailView(),
      binding: SessionDetailBinding(),
    ),
    GetPage(
      name: Routes.SHARED_LOCKERS,
      page: () => const SharedLockersView(),
      binding: SharedLockersBinding(),
    ),
    GetPage(
      name: Routes.SHARED_LOCKER,
      page: () => const SharedLockerView(),
      binding: SharedLockerBinding(),
    ),
    GetPage(
      name: Routes.SHARED_FOLDER,
      page: () => const SharedFolderView(),
      binding: SharedFolderBinding(),
    ),
    GetPage(
      name: Routes.VERIFY_ENCRYPTION_KEY,
      page: () => const VerifyEncryptionKeyView(),
      binding: VerifyEncryptionKeyBinding(),
    ),
    GetPage(
      name: Routes.CREATE_ENCRYPTION_KEY,
      page: () => const CreateEncryptionKeyView(),
      binding: CreateEncryptionKeyBinding(),
    ),
    GetPage(
      name: Routes.LOCAL_AUTH,
      page: () => const LocalAuthView(),
      binding: LocalAuthBinding(),
    ),
    GetPage(
      name: Routes.WELCOME,
      page: () => const WelcomeView(),
      binding: WelcomeBinding(),
    ),
    GetPage(
      name: Routes.NOTIFICATIONS,
      page: () => const NotificationsView(),
      binding: NotificationsBinding(),
    ),
  ];
}
