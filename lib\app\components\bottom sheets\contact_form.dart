import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/button.dart';
import 'package:ironlocker/app/components/drop_down_field.dart';
import 'package:ironlocker/app/components/input_field.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import 'package:ironlocker/app/helpers/decryption.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/interstitial_ad_manager.dart';
import 'package:ironlocker/app/helpers/secure_encryption.dart';

import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/services/locker.dart';
import 'package:ironlocker/app/stores/locker_items.dart';

void contactFormSheet({
  required BuildContext context,
  String? folderId,
  Function(Map item)? onCreated,
  String? sharedLockerId,
  String? encryptionKey,
  Map? updateContact,
  List? folders,
}) {
  final folderController = TextEditingController(
    text: folderId ?? updateContact?['folder'] ?? "",
  );
  final sharedLockerController = TextEditingController(
    text: sharedLockerId ?? "",
  );
  final nameController = TextEditingController(
    text: updateContact?['name'] ?? "",
  );
  final phoneNumberController = TextEditingController(
    text: updateContact?['phoneNumber'] ?? "",
  );
  final notesController = TextEditingController(
    text: updateContact?['notes'] ?? "",
  );

  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  ScrollController scrollController = ScrollController();

  var loading = false.obs;
  final localFolders = folders ?? LockerItemsStore.to.folders;

  final lockerService = Get.find<LockerService>();

  handleSubmit() async {
    if (formKey.currentState!.validate()) {
      DialogHelper.loading();

      var encryptedFields = await Future.wait([
        SecureEncryptionHelper.encryptText(nameController.text, encryptionKey),
        SecureEncryptionHelper.encryptText(
          phoneNumberController.text,
          encryptionKey,
        ),
        SecureEncryptionHelper.encryptText(notesController.text, encryptionKey),
      ]);

      final resp =
          updateContact == null
              ? await lockerService.addItem(
                itemType: "Contact",
                contact: {
                  "name": encryptedFields[0],
                  "phoneNumber": encryptedFields[1],
                  "notes": encryptedFields[2],
                  "folder":
                      folderController.text != "no_folder"
                          ? folderController.text
                          : "",
                  "sharedLocker": sharedLockerController.text,
                },
              )
              : await lockerService.updateItem(
                itemType: "Contact",
                itemId: updateContact["_id"],
                contact: {
                  "name": encryptedFields[0],
                  "phoneNumber": encryptedFields[1],
                  "notes": encryptedFields[2],
                  "folder":
                      folderController.text != "no_folder"
                          ? folderController.text
                          : "",
                },
              );

      if (resp.hasError) {
        Get.back();
        ToastHelper.error("Something went wrong");
        return;
      }

      if (sharedLockerId != null) {
        Get.back();
        Get.back();

        var data = await DecryptionHelper.decryptDynamicData(
          resp.body,
          encryptionKey!,
        );

        if (updateContact != null) {
          ToastHelper.success("Contact Updated");
        } else {
          onCreated!(data);
          ToastHelper.success("Contact Added");
          // Show interstitial ad after successful item creation
          InterstitialAdManager.instance.showItemCreationAd(
            useTestAds: kDebugMode,
          );
        }
        return;
      }

      var data = await DecryptionHelper.decryptDynamicData(resp.body);

      if (updateContact != null) {
        LockerItemsStore.to.updateLockerItem(data);
        Get.back();
        Get.back();
        ToastHelper.success("Contact Updated");
        return;
      } else {
        if (onCreated != null) {
          Get.back();
          Get.back();
          ToastHelper.success("Contact Added");
          onCreated(data);
          // Show interstitial ad after successful item creation
          InterstitialAdManager.instance.showItemCreationAd(
            useTestAds: kDebugMode,
          );
          return;
        }
      }

      if (folderController.text.isEmpty) {
        LockerItemsStore.to.addItem(data);
      }

      nameController.clear();
      notesController.clear();
      phoneNumberController.clear();

      Get.back();
      Get.back();
      ToastHelper.success("Contact Added");

      // Show interstitial ad after successful item creation
      InterstitialAdManager.instance.showItemCreationAd(useTestAds: kDebugMode);
    }
  }

  showModalBottomSheet(
    context: context,
    elevation: 0,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder:
        (context) => Container(
          decoration: BoxDecoration(
            color: IronLockerColors.getThemeBackground(Get.isDarkMode),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(
                  alpha: Get.isDarkMode ? 0.5 : 0.1,
                ),
                blurRadius: 20,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: SingleChildScrollView(
              controller: scrollController,
              child: SafeArea(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle bar
                    Container(
                      margin: const EdgeInsets.only(top: 12),
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: IronLockerColors.getThemeTextSecondary(
                          Get.isDarkMode,
                        ),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),

                    // Header
                    _buildContactHeader(updateContact != null),

                    // Form Content
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: Form(
                        key: formKey,
                        child: Column(
                          children: [
                            // Basic Information Section
                            _buildContactFormSection(
                              title: "Basic Information",
                              icon: Icons.info_outline,
                              color: IronLockerColors.contactTheme,
                              children: [
                                InputField(
                                  controller: nameController,
                                  labelText: "Name",
                                  validator: (val) {
                                    if (val!.isEmpty) {
                                      return "This is a required field";
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: 16),
                                if (folderId == null)
                                  DropDownField(
                                    controller: folderController,
                                    items: [
                                      const {
                                        "name": "No folder",
                                        "_id": "no_folder",
                                      },
                                      ...localFolders,
                                    ],
                                    labelText: "Folder",
                                    selectedItem:
                                        updateContact?['folder'] != null
                                            ? localFolders.firstWhere(
                                              (folder) =>
                                                  folder["_id"] ==
                                                  updateContact?['folder'],
                                            )
                                            : const {
                                              "name": "No folder",
                                              "_id": "no_folder",
                                            },
                                  ),
                              ],
                            ),

                            const SizedBox(height: 24),

                            // Contact Information Section
                            _buildContactFormSection(
                              title: "Contact Information",
                              icon: Icons.phone_outlined,
                              color: IronLockerColors.blue02,
                              children: [
                                InputField(
                                  controller: phoneNumberController,
                                  labelText: "Phone Number",
                                ),
                              ],
                            ),

                            const SizedBox(height: 24),

                            // Additional Information Section
                            _buildContactFormSection(
                              title: "Additional Information",
                              icon: Icons.note_outlined,
                              color: IronLockerColors.blue02.withValues(
                                alpha: 0.7,
                              ),
                              children: [
                                InputField(
                                  controller: notesController,
                                  labelText: "Notes",
                                  maxLines: 4,
                                ),
                              ],
                            ),

                            const SizedBox(height: 32),

                            // Submit Button
                            Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: IronLockerColors.blue02.withValues(
                                      alpha: 0.3,
                                    ),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Obx(
                                () => Button(
                                  text:
                                      updateContact != null
                                          ? 'Update Contact'
                                          : 'Add Contact',
                                  loading: loading.value,
                                  onPress: handleSubmit,
                                  bgColor: IronLockerColors.blue02,
                                  height: 50,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
  );
}

Widget _buildContactHeader(bool isUpdate) {
  return Container(
    margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    padding: const EdgeInsets.all(24),
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [
          IronLockerColors.blue02.withValues(alpha: 0.1),
          IronLockerColors.blue02.withValues(alpha: 0.05),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(20),
      border: Border.all(
        color: IronLockerColors.blue02.withValues(alpha: 0.2),
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: IronLockerColors.blue02.withValues(alpha: 0.1),
          blurRadius: 12,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: Row(
      children: [
        // Enhanced icon container
        Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                IronLockerColors.blue02,
                IronLockerColors.blue02.withValues(alpha: 0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(18),
            boxShadow: [
              BoxShadow(
                color: IronLockerColors.blue02.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: const Center(
            child: Icon(Icons.contacts_outlined, color: Colors.white, size: 32),
          ),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "${isUpdate ? 'Update' : 'Add'} Contact",
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w600,
                  color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  fontFamily: 'Jura',
                  letterSpacing: 0.5,
                ),
              ),
              const SizedBox(height: 6),
              Text(
                "${isUpdate ? 'Update your' : 'Add a new'} contact to your locker",
                style: TextStyle(
                  fontSize: 14,
                  color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                  fontFamily: 'Jura',
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
        // Decorative element
        Container(
          width: 4,
          height: 40,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                IronLockerColors.blue02,
                IronLockerColors.blue02.withValues(alpha: 0.3),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ],
    ),
  );
}

Widget _buildContactFormSection({
  required String title,
  required IconData icon,
  required Color color,
  required List<Widget> children,
}) {
  return Container(
    decoration: BoxDecoration(
      color: Get.isDarkMode ? Colors.grey[850] : Colors.grey[50],
      borderRadius: BorderRadius.circular(16),
      border: Border.all(
        color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
        width: 1,
      ),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Get.isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
          child: Column(children: children),
        ),
      ],
    ),
  );
}
