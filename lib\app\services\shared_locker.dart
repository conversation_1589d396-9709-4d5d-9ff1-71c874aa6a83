import 'package:get/get.dart';
import 'package:ironlocker/app/constants/endpoints.dart';
import 'package:ironlocker/app/helpers/device_info.dart';
import 'package:ironlocker/app/helpers/storage.dart';
import 'package:ironlocker/app/stores/user.dart';

class SharedLockerService extends GetConnect {
  final endPoints = ApiEndpoints();
  @override
  void onInit() {
    httpClient.timeout = const Duration(seconds: 30);
    httpClient.addRequestModifier<dynamic>((request) async {
      var device = await DeviceInfo.device();
      request.headers['User-Agent'] = device;

      request.headers['Authorization'] =
          "Bearer ${UserStore.to.accessToken.value}";
      return request;
    });

    httpClient.addAuthenticator<dynamic>((request) async {
      final storage = Storage();

      final response = await post("${endPoints.auth}/refresh-token", {
        "refreshToken": UserStore.to.refreshToken.value,
      });

      final accessToken = response.body['accessToken'];
      final refreshToken = response.body['refreshToken'];

      await storage.setItem(key: "accessToken", value: accessToken);
      await storage.setItem(key: "refreshToken", value: refreshToken);

      UserStore.to.accessToken(accessToken);
      UserStore.to.refreshToken(refreshToken);

      // Set the header
      request.headers['Authorization'] = "Bearer $accessToken";
      return request;
    });

    httpClient.maxAuthRetries = 3;

    super.onInit();
  }

  Future<Response> getSharedLockers() {
    return get("${endPoints.sharedLockers}/user/${UserStore.to.id}");
  }

  Future<Response> getSharedsharedLockerItems(String sharedLockerId) {
    return get("${endPoints.sharedLockers}/$sharedLockerId/items");
  }

  Future<Response> getSharedsharedLockerKey(String sharedLockerId) {
    return get("${endPoints.sharedLockers}/$sharedLockerId/encryption-key");
  }

  Future<Response> deleteSharedLocker(String sharedLockerId) {
    return delete("${endPoints.sharedLockers}/$sharedLockerId");
  }

  Future<Response> updateSharedLockerName({
    required String sharedLockerId,
    required String name,
  }) {
    return patch("${endPoints.sharedLockers}/$sharedLockerId/name", {
      "name": name,
    });
  }

  Future<Response> createSharedLocker({
    required String name,
    required String key,
  }) {
    return post(endPoints.sharedLockers, {"name": name, "key": key});
  }
}
