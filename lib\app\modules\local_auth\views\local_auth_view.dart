import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/local_auth_controller.dart';

class LocalAuthView extends GetView<LocalAuthController> {
  const LocalAuthView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(color: Theme.of(context).primaryColor),
        child: Center(
          child: Image.asset(
            'assets/icons/app-icon.png',
            width: 150,
            height: 150,
          ),
        ),
      ),
    );
  }
}
