import 'package:get/get.dart';
import 'package:ironlocker/app/constants/endpoints.dart';
import 'package:ironlocker/app/helpers/device_info.dart';
import 'package:ironlocker/app/helpers/storage.dart';
import 'package:ironlocker/app/stores/user.dart';

class UserService extends GetConnect {
  final endPoints = ApiEndpoints();
  @override
  void onInit() {
    httpClient.timeout = const Duration(seconds: 30);
    httpClient.addRequestModifier<dynamic>((request) async {
      var device = await DeviceInfo.device();
      request.headers['User-Agent'] = device;

      request.headers['Authorization'] =
          "Bearer ${UserStore.to.accessToken.value}";
      return request;
    });

    httpClient.addAuthenticator<dynamic>((request) async {
      final storage = Storage();

      final response = await post("${endPoints.auth}/refresh-token", {
        "refreshToken": UserStore.to.refreshToken.value,
      });

      final accessToken = response.body['accessToken'];
      final refreshToken = response.body['refreshToken'];

      await storage.setItem(key: "accessToken", value: accessToken);
      await storage.setItem(key: "refreshToken", value: refreshToken);

      UserStore.to.accessToken(accessToken);
      UserStore.to.refreshToken(refreshToken);

      // Set the header
      request.headers['Authorization'] = "Bearer $accessToken";
      return request;
    });

    httpClient.maxAuthRetries = 3;

    super.onInit();
  }

  Future<Response> getProfile() {
    return get("${endPoints.user}/profile");
  }

  Future<Response> getPublic(String user) {
    return get("${endPoints.user}/public-key/$user");
  }

  Future<Response> getKeys() {
    return get("${endPoints.user}/keys");
  }

  Future<Response> saveKeys(Map data) {
    return post("${endPoints.user}/keys", data);
  }

  Future<Response> updateName(String name) {
    return patch("${endPoints.user}/name", {"name": name});
  }

  Future<Response> updateEmail({
    required String email,
    required String currentPassword,
  }) {
    return patch("${endPoints.user}/email", {
      "email": email,
      "currentPassword": currentPassword,
    });
  }

  Future<Response> updatePassword({
    required String newPassword,
    required String currentPassword,
  }) {
    return patch("${endPoints.user}/password", {
      "newPassword": newPassword,
      "currentPassword": currentPassword,
    });
  }

  Future<Response> enable2fa(Map data) {
    return post("${endPoints.user}/enable-2fa", data);
  }

  Future<Response> get2faSecret({required String password}) {
    return post("${endPoints.user}/2fa-secret", {"password": password});
  }

  Future<Response> getRecoveryCodes({required String password}) {
    return post("${endPoints.user}/recovery-codes", {"password": password});
  }

  Future<Response> disable2fa({required String password}) {
    return post("${endPoints.user}/disable-2fa", {"password": password});
  }

  Future<Response> deleteAccount({required String currentPassword}) {
    return post("${endPoints.user}/delete-account", {
      "currentPassword": currentPassword,
    });
  }

  Future<Response> signout() {
    final storage = Storage();
    return post("${endPoints.user}/signout", {
      "refreshToken": storage.getItem("refreshToken"),
    });
  }

  Future<Response> verifyPassword(String password) {
    return post("${endPoints.user}/verify-password", {"password": password});
  }
}
