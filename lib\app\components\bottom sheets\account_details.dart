import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/locker_item_property_detail.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';

void accountDetails({required BuildContext context, required Map account}) {
  showModalBottomSheet(
    context: context,
    elevation: 0,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder:
        (context) => Container(
          decoration: BoxDecoration(
            color: IronLockerColors.getThemeCard(Get.isDarkMode),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(28),
              topRight: Radius.circular(28),
            ),
            border: Border.all(
              color: IronLockerColors.getThemeBorder(Get.isDarkMode),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(
                  alpha: Get.isDarkMode ? 0.4 : 0.15,
                ),
                blurRadius: 30,
                offset: const Offset(0, -10),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: IronLockerColors.blue02.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, -5),
                spreadRadius: -5,
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: SingleChildScrollView(
              child: SafeArea(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle bar
                    Container(
                      margin: const EdgeInsets.only(top: 16),
                      width: 48,
                      height: 5,
                      decoration: BoxDecoration(
                        color: IronLockerColors.getThemeBorder(Get.isDarkMode),
                        borderRadius: BorderRadius.circular(3),
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Header
                    _buildAccountHeader(),

                    // Content
                    Padding(
                      padding: const EdgeInsets.fromLTRB(24, 8, 24, 32),
                      child: Column(
                        children: [
                          _buildAccountPropertyDetail(
                            icon: Icons.account_circle_outlined,
                            name: "Name",
                            value: account['name'],
                            color: IronLockerColors.blue02,
                          ),
                          if (account['userName'] != null &&
                              account['userName'].toString().isNotEmpty)
                            _buildAccountPropertyDetail(
                              icon: Icons.person_outline,
                              name: "Username",
                              value: account['userName'],
                              color: IronLockerColors.blue02,
                            ),
                          if (account['email'] != null &&
                              account['email'].toString().isNotEmpty)
                            _buildAccountPropertyDetail(
                              icon: Icons.email_outlined,
                              name: "Email",
                              value: account['email'],
                              color: IronLockerColors.blue02,
                            ),
                          if (account['password'] != null &&
                              account['password'].toString().isNotEmpty)
                            _buildAccountPropertyDetail(
                              icon: Icons.lock_outline,
                              name: "Password",
                              value: account['password'],
                              color: IronLockerColors.blue02,
                              isSecret: true,
                            ),
                          if (account['website'] != null &&
                              account['website'].toString().isNotEmpty)
                            _buildAccountPropertyDetail(
                              icon: Icons.language,
                              name: "Website",
                              value: account['website'],
                              color: IronLockerColors.blue02,
                            ),
                          if (account['notes'] != null &&
                              account['notes'].toString().isNotEmpty)
                            _buildAccountPropertyDetail(
                              icon: Icons.note_outlined,
                              name: "Notes",
                              value: account['notes'],
                              color: IronLockerColors.blue02,
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
  );
}

Widget _buildAccountHeader() {
  return Container(
    margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    padding: const EdgeInsets.all(24),
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [
          IronLockerColors.blue02.withValues(alpha: 0.1),
          IronLockerColors.blue02.withValues(alpha: 0.05),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(20),
      border: Border.all(
        color: IronLockerColors.blue02.withValues(alpha: 0.2),
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: IronLockerColors.blue02.withValues(alpha: 0.1),
          blurRadius: 12,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: Row(
      children: [
        // Enhanced icon container
        Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                IronLockerColors.blue02,
                IronLockerColors.blue02.withValues(alpha: 0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(18),
            boxShadow: [
              BoxShadow(
                color: IronLockerColors.blue02.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: const Icon(
            Icons.account_circle_rounded,
            color: Colors.white,
            size: 28,
          ),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Account Details",
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w700,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  letterSpacing: 0.5,
                ),
              ),
              const SizedBox(height: 6),
              Text(
                "Login credentials & information",
                style: TextStyle(
                  fontSize: 15,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                  height: 1.3,
                ),
              ),
            ],
          ),
        ),
        // Decorative element
        Container(
          width: 4,
          height: 40,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                IronLockerColors.blue02,
                IronLockerColors.blue02.withValues(alpha: 0.3),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ],
    ),
  );
}

Widget _buildAccountPropertyDetail({
  required IconData icon,
  required String name,
  required dynamic value,
  required Color color,
  bool isSecret = false,
}) {
  return Container(
    margin: const EdgeInsets.only(bottom: 16),
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: Get.isDarkMode ? Colors.grey[850] : Colors.grey[50],
      borderRadius: BorderRadius.circular(12),
      border: Border.all(
        color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
        width: 1,
      ),
    ),
    child: Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 18),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: LockerItemPropertyDetail(
            name: name,
            value: value?.toString() ?? "",
          ),
        ),
      ],
    ),
  );
}
