import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/locker_item.dart';
import 'package:responsive_grid/responsive_grid.dart';

class LockerItems extends StatelessWidget {
  const LockerItems({
    super.key,
    required this.items,
    this.selectedItems,
    this.encryptionKey,
    this.onSelected,
    this.onUnSelected,
  });
  final RxList items;
  final RxList? selectedItems;
  final Function(dynamic)? onSelected;
  final Function(dynamic)? onUnSelected;
  final String? encryptionKey;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
      child: Obx(
        () => ResponsiveGridList(
          desiredItemWidth: 160,
          minSpacing: 12,
          children:
              List.generate(items.length, (index) => index).map((i) {
                return LockerItem(
                  id: items[i]['_id'],
                  title: items[i]['name'],
                  itemType: items[i]['itemType'],
                  favourite: items[i]['favourite'],
                  lockerItem: items[i],
                  encryptionKey: encryptionKey,
                  selectionMode: selectedItems?.isNotEmpty ?? false,
                  selected:
                      selectedItems?.firstWhereOrNull(
                        (item) => item == items[i]['_id'],
                      ) !=
                      null,
                  onSelected: () {
                    onSelected != null && onSelected!(items[i]['_id']);
                  },
                  onUnSelected: () {
                    onUnSelected != null && onUnSelected!(items[i]['_id']);
                  },
                );
              }).toList(),
        ),
      ),
    );
  }
}
