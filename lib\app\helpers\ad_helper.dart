import 'dart:io';

class AdHelper {
  // Your production App ID: ca-app-pub-9148534327614816~5030822015
  // Your production Banner Ad Unit ID: ca-app-pub-9148534327614816/2427282356
  // Your production Interstitial Ad Unit ID: You'll need to create this in AdMob console

  static String get bannerAdUnitId {
    if (Platform.isAndroid) {
      return "ca-app-pub-9148534327614816/2427282356";
    } else if (Platform.isIOS) {
      // You'll need to provide iOS ad unit ID when you create one
      // For now, using the same Android ID
      return "ca-app-pub-9148534327614816/2427282356";
    } else {
      throw UnsupportedError("Unsupported platform");
    }
  }

  static String get interstitialAdUnitId {
    if (Platform.isAndroid) {
      return "ca-app-pub-9148534327614816/2812678161";
    } else if (Platform.isIOS) {
      // You'll need to provide iOS interstitial ad unit ID when you create one
      // For now, using Android ID as fallback
      return "ca-app-pub-9148534327614816/2812678161";
    } else {
      throw UnsupportedError("Unsupported platform");
    }
  }

  static String get rewardedAdUnitId {
    if (Platform.isAndroid) {
      return "ca-app-pub-9148534327614816/9456880081";
    } else if (Platform.isIOS) {
      // You'll need to provide iOS rewarded ad unit ID when you create one
      return "ca-app-pub-3940256099942544/1712485313"; // Test rewarded ad unit
    } else {
      throw UnsupportedError("Unsupported platform");
    }
  }

  // Test ad unit IDs for development/testing
  static String get testBannerAdUnitId {
    if (Platform.isAndroid) {
      return "ca-app-pub-3940256099942544/6300978111";
    } else if (Platform.isIOS) {
      return "ca-app-pub-3940256099942544/2934735716";
    } else {
      throw UnsupportedError("Unsupported platform");
    }
  }

  static String get testInterstitialAdUnitId {
    if (Platform.isAndroid) {
      return "ca-app-pub-3940256099942544/1033173712";
    } else if (Platform.isIOS) {
      return "ca-app-pub-3940256099942544/4411468910";
    } else {
      throw UnsupportedError("Unsupported platform");
    }
  }

  static String get testRewardedAdUnitId {
    if (Platform.isAndroid) {
      return "ca-app-pub-3940256099942544/5224354917";
    } else if (Platform.isIOS) {
      return "ca-app-pub-3940256099942544/1712485313";
    } else {
      throw UnsupportedError("Unsupported platform");
    }
  }
}
