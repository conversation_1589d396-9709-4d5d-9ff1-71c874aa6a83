import 'package:flutter/material.dart';
import 'package:ironlocker/app/components/password_checklist_item.dart';
import 'package:ironlocker/app/helpers/password_validator.dart';

class PasswordChecklist extends StatefulWidget {
  const PasswordChecklist(
      {super.key,
      required this.passwordController,
      this.confirmPasswordController,
      this.passwordsMustMatch = false});
  final TextEditingController passwordController;
  final TextEditingController? confirmPasswordController;
  final bool passwordsMustMatch;

  @override
  State<PasswordChecklist> createState() => _PasswordChecklistState();
}

class _PasswordChecklistState extends State<PasswordChecklist> {
  var atleastEightChars = false;
  var atleastOneUppercase = false;
  var atleastOneLowercase = false;
  var atleastOneNumber = false;
  var atleastOneCharacter = false;
  var passwordsMatch = false;

  @override
  void initState() {
    super.initState();
    widget.passwordController.addListener(() {
      setState(() {
        final password = widget.passwordController.text;

        atleastEightChars = PwdValidator.atleastEightChars(password);
        atleastOneUppercase = PwdValidator.atleastOneUppercase(password);
        atleastOneLowercase = PwdValidator.atleastOneLowercase(password);
        atleastOneNumber = PwdValidator.atleastOneNumber(password);
        atleastOneCharacter = PwdValidator.atleastOneCharacter(password);

        if (widget.confirmPasswordController != null) {
          passwordsMatch = widget.passwordController.text ==
              widget.confirmPasswordController!.text;
        }
      });
    });

    widget.confirmPasswordController?.addListener(() {
      setState(() {
        final password = widget.passwordController.text;

        atleastEightChars = PwdValidator.atleastEightChars(password);
        atleastOneUppercase = PwdValidator.atleastOneUppercase(password);
        atleastOneLowercase = PwdValidator.atleastOneLowercase(password);
        atleastOneNumber = PwdValidator.atleastOneNumber(password);
        atleastOneCharacter = PwdValidator.atleastOneCharacter(password);

        passwordsMatch = widget.passwordController.text ==
            widget.confirmPasswordController!.text;
      });
    });
  }

  @override
  void dispose() {
    widget.passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        PasswordChecklistItem(
          condition: "Atleast 8 characters",
          conditionMet: atleastEightChars,
        ),
        PasswordChecklistItem(
          condition: "Atleast 1 uppercase letter",
          conditionMet: atleastOneUppercase,
        ),
        PasswordChecklistItem(
          condition: "Atleast 1 lowercase letter",
          conditionMet: atleastOneLowercase,
        ),
        PasswordChecklistItem(
          condition: "Atleast 1 number",
          conditionMet: atleastOneNumber,
        ),
        PasswordChecklistItem(
          condition: "Atleast 1 special character",
          conditionMet: atleastOneCharacter,
        ),
        Offstage(
          offstage: !widget.passwordsMustMatch,
          child: PasswordChecklistItem(
            condition: "Passwords match",
            conditionMet: widget.passwordController.text.isNotEmpty
                ? passwordsMatch
                : false,
          ),
        ),
      ],
    );
  }
}
