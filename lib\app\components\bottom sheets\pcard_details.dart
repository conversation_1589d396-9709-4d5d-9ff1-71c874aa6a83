import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/locker_item_property_detail.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';

void paymentCardDetails({
  required BuildContext context,
  required Map paymentCard,
}) {
  showModalBottomSheet(
    context: context,
    elevation: 0,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder:
        (context) => Container(
          decoration: BoxDecoration(
            color: Get.isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(
                  alpha: Get.isDarkMode ? 0.5 : 0.1,
                ),
                blurRadius: 20,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: SingleChildScrollView(
              child: Safe<PERSON>rea(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle bar
                    Container(
                      margin: const EdgeInsets.only(top: 12),
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color:
                            Get.isDarkMode
                                ? Colors.grey[600]
                                : Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),

                    // Header
                    _buildHeader(),

                    // Content
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        children: [
                          _buildPropertyDetail(
                            icon: Icons.credit_card,
                            name: "Name",
                            value: paymentCard['name'],
                            color: IronLockerColors.blue02,
                          ),
                          if (paymentCard['cardHolderName'] != null &&
                              paymentCard['cardHolderName']
                                  .toString()
                                  .isNotEmpty)
                            _buildPropertyDetail(
                              icon: Icons.person_outline,
                              name: "Card Holder Name",
                              value: paymentCard['cardHolderName'],
                              color: IronLockerColors.blue02,
                            ),
                          if (paymentCard['cardNumber'] != null &&
                              paymentCard['cardNumber'].toString().isNotEmpty)
                            _buildPropertyDetail(
                              icon: Icons.numbers,
                              name: "Card Number",
                              value: paymentCard['cardNumber'],
                              color: IronLockerColors.blue02,
                              isSecret: true,
                            ),
                          if (paymentCard['expiryMonth'] != null &&
                              paymentCard['expiryMonth'].toString().isNotEmpty)
                            _buildPropertyDetail(
                              icon: Icons.calendar_month,
                              name: "Expiry Month",
                              value: paymentCard['expiryMonth'],
                              color: IronLockerColors.blue02,
                            ),
                          if (paymentCard['expiryYear'] != null &&
                              paymentCard['expiryYear'].toString().isNotEmpty)
                            _buildPropertyDetail(
                              icon: Icons.calendar_today,
                              name: "Expiry Year",
                              value: paymentCard['expiryYear'],
                              color: IronLockerColors.blue02,
                            ),
                          if (paymentCard['cvv'] != null &&
                              paymentCard['cvv'].toString().isNotEmpty)
                            _buildPropertyDetail(
                              icon: Icons.security,
                              name: "CVV",
                              value: paymentCard['cvv'],
                              color: IronLockerColors.blue02,
                              isSecret: true,
                            ),
                          if (paymentCard['notes'] != null &&
                              paymentCard['notes'].toString().isNotEmpty)
                            _buildPropertyDetail(
                              icon: Icons.note_outlined,
                              name: "Notes",
                              value: paymentCard['notes'],
                              color: IronLockerColors.blue02,
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
  );
}

Widget _buildHeader() {
  return Container(
    margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    padding: const EdgeInsets.all(24),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(20),
      gradient: LinearGradient(
        colors: [
          IronLockerColors.blue02.withValues(alpha: 0.1),
          IronLockerColors.blue02.withValues(alpha: 0.05),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      border: Border.all(
        color: IronLockerColors.blue02.withValues(alpha: 0.2),
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: IronLockerColors.blue02.withValues(alpha: 0.1),
          blurRadius: 12,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: IronLockerColors.blue02.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.credit_card,
            color: IronLockerColors.blue02,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Payment Card Details",
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w700,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                  letterSpacing: 0.5,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                "View your payment card information",
                style: TextStyle(
                  fontSize: 15,
                  fontFamily: 'Jura',
                  color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                  height: 1.3,
                ),
              ),
            ],
          ),
        ),
        Container(
          width: 4,
          height: 40,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                IronLockerColors.blue02,
                IronLockerColors.blue02.withValues(alpha: 0.3),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ],
    ),
  );
}

Widget _buildPropertyDetail({
  required IconData icon,
  required String name,
  required dynamic value,
  required Color color,
  bool isSecret = false,
}) {
  return Container(
    margin: const EdgeInsets.only(bottom: 16),
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: Get.isDarkMode ? Colors.grey[850] : Colors.grey[50],
      borderRadius: BorderRadius.circular(12),
      border: Border.all(
        color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
        width: 1,
      ),
    ),
    child: Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 18),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LockerItemPropertyDetail(
                name: name,
                value: value?.toString() ?? "",
              ),
            ],
          ),
        ),
      ],
    ),
  );
}
