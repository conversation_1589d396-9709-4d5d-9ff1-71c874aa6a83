import 'package:flutter/material.dart';

/// IronLocker Brand Colors
/// Based on the web design system color palette
class IronLockerColors {
  // Primary Brand Colors
  static const Color blue01 = Color(0xFF1F3B73); // rgba(31, 59, 115, 1)
  static const Color blue02 = Color(0xFF448BFF); // rgba(68, 139, 255, 1)
  static const Color yellow = Color(0xFFFFB400); // rgba(255, 180, 0, 1)
  static const Color yellow01 = Color(0xFFFF6B00); // rgba(255, 107, 0, 1)

  // Supporting Colors
  static const Color black = Color(0xFF262626); // rgba(38, 38, 38, 1)
  static const Color blueGray = Color(0xFF6C7A89); // rgba(108, 122, 137, 1)
  static const Color white01 = Color(0xFFF5F7FA); // rgba(245, 247, 250, 1)

  // Background Colors
  static const Color background = Color(0xFFFFFFFF); // --background
  static const Color foreground = Color(0xFF171717); // --foreground

  // Gradient Colors for Headers
  static const List<Color> blueGradient = [blue01, blue02];
  static const List<Color> yellowGradient = [yellow, yellow01];
  static const List<Color> primaryGradient = [blue01, blue02, yellow];

  // Theme-adaptive colors
  static Color backgroundDark = const Color(0xFF1E1E1E);
  static Color backgroundLight = background;

  static Color cardDark = const Color(0xFF2A2A2A);
  static Color cardLight = white01;

  static Color textPrimaryDark = Colors.white;
  static Color textPrimaryLight = foreground;

  static Color textSecondaryDark = Colors.grey[400]!;
  static Color textSecondaryLight = blueGray;

  static Color borderDark = Colors.grey[700]!;
  static Color borderLight = Colors.grey[200]!;

  // Form Section Colors (using brand palette)
  static const Color basicInfoColor = blue02;
  static const Color credentialsColor = blue01;
  static const Color secondaryColor = yellow;
  static const Color locationColor = yellow01;
  static const Color financialColor = blue01;
  static const Color organizationColor = yellow;

  // Item Type Theme Colors (matching add item sheet icons)
  static const Color contactTheme = Colors.blue;
  static const Color noteTheme = Colors.orange;
  static const Color addressTheme = Colors.green;
  static const Color accountTheme = Colors.purple;
  static const Color paymentCardTheme = Colors.indigo;
  static const Color bankAccountTheme = Colors.teal;
  static const Color folderTheme = Colors.amber;

  // Status Colors (keeping some standard colors for status)
  static const Color success = Color(0xFF10B981);
  static const Color error = Color(0xFFEF4444);
  static const Color warning = yellow;
  static const Color info = blue02;

  // Helper methods
  static Color getThemeBackground(bool isDark) {
    return isDark ? backgroundDark : backgroundLight;
  }

  static Color getThemeCard(bool isDark) {
    return isDark ? cardDark : cardLight;
  }

  static Color getThemeTextPrimary(bool isDark) {
    return isDark ? textPrimaryDark : textPrimaryLight;
  }

  static Color getThemeTextSecondary(bool isDark) {
    return isDark ? textSecondaryDark : textSecondaryLight;
  }

  static Color getThemeBorder(bool isDark) {
    return isDark ? borderDark : borderLight;
  }

  // Gradient helpers
  static LinearGradient getPrimaryGradient({
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
    double opacity = 0.1,
  }) {
    return LinearGradient(
      colors: [
        blue01.withValues(alpha: opacity),
        blue02.withValues(alpha: opacity),
      ],
      begin: begin,
      end: end,
    );
  }

  static LinearGradient getYellowGradient({
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
    double opacity = 0.1,
  }) {
    return LinearGradient(
      colors: [
        yellow.withValues(alpha: opacity),
        yellow01.withValues(alpha: opacity),
      ],
      begin: begin,
      end: end,
    );
  }

  static LinearGradient getBlueGradient({
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
    double opacity = 0.1,
  }) {
    return LinearGradient(
      colors: [
        blue01.withValues(alpha: opacity),
        blue02.withValues(alpha: opacity),
      ],
      begin: begin,
      end: end,
    );
  }
}
