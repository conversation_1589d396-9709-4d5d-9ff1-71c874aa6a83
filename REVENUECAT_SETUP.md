# RevenueCat Purchase Setup Guide for IronLocker

## 🎯 Overview

This guide will help you set up RevenueCat for purchase initiation only. Your backend will handle subscription status management.

## 📋 Prerequisites

- RevenueCat account (free tier available)
- Google Play Console account (for Android)
- Apple Developer account (for iOS)
- App published to stores (at least in internal testing)

## 🔧 Step 1: RevenueCat Dashboard Setup

### 1.1 Create RevenueCat Account

1. Go to [https://app.revenuecat.com](https://app.revenuecat.com)
2. Sign up for a free account
3. Create a new project for IronLocker

### 1.2 Configure App

1. In RevenueCat dashboard, go to **Projects** → **Apps**
2. Add your Android app:
   - **Bundle ID**: `app.ironlocker.ironlocker_dev`
   - **Platform**: Google Play Store
3. Add your iOS app:
   - **Bundle ID**: `app.ironlocker.ironlocker_dev`
   - **Platform**: Apple App Store

### 1.3 Get API Keys

1. Go to **Projects** → **API Keys**
2. Copy your **Google API Key** (starts with `goog_`)
3. Copy your **Apple API Key** (starts with `appl_`)

## 🏪 Step 2: Store Configuration

### 2.1 Google Play Store Setup

1. **Create Subscription Products**:

   - Go to Google Play Console → Your App → Monetization → Products → Subscriptions
   - Create products:
     - **Product ID**: `premium_monthly`
     - **Name**: "Premium Monthly"
     - **Price**: $2.99/month
     - **Product ID**: `premium_yearly`
     - **Name**: "Premium Yearly"
     - **Price**: $24.99/year

2. **Configure Service Account**:
   - Go to Google Cloud Console
   - Create service account for RevenueCat
   - Download JSON key file
   - Upload to RevenueCat dashboard

### 2.2 Apple App Store Setup

1. **Create Subscription Group**:

   - Go to App Store Connect → Your App → Features → In-App Purchases
   - Create Auto-Renewable Subscription Group: "Premium"

2. **Create Subscription Products**:

   - **Product ID**: `premium_monthly`
   - **Reference Name**: "Premium Monthly"
   - **Duration**: 1 Month
   - **Price**: $2.99
   - **Product ID**: `premium_yearly`
   - **Reference Name**: "Premium Yearly"
   - **Duration**: 1 Year
   - **Price**: $24.99

3. **Configure App Store Connect API**:
   - Generate API key in App Store Connect
   - Add to RevenueCat dashboard

## 🎛️ Step 3: RevenueCat Product Configuration

### 3.1 Import Products

1. In RevenueCat dashboard, go to **Products**
2. Click **Import from stores**
3. Import your subscription products from both stores

### 3.2 Create Entitlements

1. Go to **Entitlements**
2. Create entitlement:
   - **Identifier**: `premium`
   - **Display Name**: "Premium Features"

### 3.3 Create Offerings

1. Go to **Offerings**
2. Create default offering:
   - **Identifier**: `default`
   - **Display Name**: "Premium Subscription"
   - Add both monthly and yearly packages
   - Set yearly as "Popular" package

## 🔑 Step 4: Update App Configuration

### 4.1 Update API Keys

Edit `lib/app/constants/subscription_config.dart`:

```dart
class SubscriptionConfig {
  // Replace with your actual RevenueCat API keys
  static const String googleApiKey = "goog_YOUR_ACTUAL_GOOGLE_KEY_HERE";
  static const String appleApiKey = "appl_YOUR_ACTUAL_APPLE_KEY_HERE";

  // Keep these as they match your store products
  static const String premiumEntitlementId = "premium";
  static const String monthlyProductId = "premium_monthly";
  static const String yearlyProductId = "premium_yearly";
  static const String defaultOfferingId = "default";
}
```

## 🧪 Step 5: Testing

### 5.1 Sandbox Testing

1. **Android**: Use Google Play Console internal testing
2. **iOS**: Use TestFlight or Xcode simulator with StoreKit configuration

### 5.2 Test Scenarios

- [ ] Purchase monthly subscription
- [ ] Purchase yearly subscription
- [ ] Restore purchases
- [ ] Cross-device subscription sync
- [ ] Subscription expiration
- [ ] Upgrade/downgrade subscriptions

## 🔗 Step 6: Webhook Setup (Optional but Recommended)

### 6.1 Create Webhook Endpoint

Create an endpoint on your server: `https://your-api.com/webhooks/revenuecat`

### 6.2 Configure in RevenueCat

1. Go to **Projects** → **Webhooks**
2. Add webhook URL
3. Select events to receive:
   - `INITIAL_PURCHASE`
   - `RENEWAL`
   - `CANCELLATION`
   - `EXPIRATION`
   - `BILLING_ISSUE`

### 6.3 Handle Webhook Events

```javascript
app.post("/webhooks/revenuecat", (req, res) => {
  const event = req.body.event;
  const userId = event.app_user_id;

  switch (event.type) {
    case "INITIAL_PURCHASE":
      // Grant premium access in your database
      break;
    case "EXPIRATION":
      // Remove premium access, make shared lockers read-only
      break;
  }

  res.status(200).send("OK");
});
```

## 🚀 Step 7: Production Deployment

### 7.1 Pre-Launch Checklist

- [ ] API keys configured correctly
- [ ] Products created in both stores
- [ ] RevenueCat configuration complete
- [ ] Webhook endpoint tested
- [ ] Subscription flow tested end-to-end

### 7.2 Launch

1. Deploy app update with subscription functionality
2. Monitor RevenueCat dashboard for purchases
3. Check webhook logs for proper event handling

## 📊 Step 8: Monitoring & Analytics

### 8.1 RevenueCat Dashboard

- Monitor subscription metrics
- Track conversion rates
- Analyze churn patterns

### 8.2 Key Metrics to Watch

- **Trial-to-paid conversion**
- **Monthly recurring revenue (MRR)**
- **Churn rate**
- **Customer lifetime value (LTV)**

## 🆘 Troubleshooting

### Common Issues

1. **"No products found"**: Check store configuration and RevenueCat import
2. **"Purchase failed"**: Verify API keys and product IDs match
3. **"Restore failed"**: Ensure user is signed into correct store account
4. **Cross-device sync issues**: Verify user identification is working

### Debug Mode

The app uses test ads in debug mode. For subscription testing:

- Android: Use test accounts in Google Play Console
- iOS: Use sandbox environment automatically in debug builds

## 📞 Support

- RevenueCat Documentation: https://docs.revenuecat.com
- RevenueCat Support: <EMAIL>
- Community: https://community.revenuecat.com

---

## 🎉 You're Ready!

Once configured, users will see the subscription options in Settings → Premium Subscription. The app will automatically:

- Handle purchases and renewals
- Sync subscription status across devices
- Enable/disable premium features based on subscription status
- Make shared lockers read-only when subscription expires

Your premium shared locker feature is now monetized! 🚀
