import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/dialog.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/user.dart';
import 'package:ironlocker/app/stores/user.dart';

class SecurityController extends GetxController {
  final userService = Get.find<UserService>();

  enable2fa(String password) async {
    DialogHelper.loading();

    final resp = await userService.verifyPassword(password);

    Get.back();
    Get.back();
    if (resp.hasError) {
      ToastHelper.error(
        resp.body != null ? resp.body['error'] : "Something went wrong",
      );
      return;
    }

    Get.toNamed(Routes.TWO_FACTOR_METHODS, parameters: {"password": password});
  }

  viewRecoveryCodes(String password) async {
    DialogHelper.loading();

    final resp = await userService.getRecoveryCodes(password: password);

    Get.back();
    if (resp.hasError) {
      ToastHelper.error(
        resp.body != null ? resp.body['error'] : "Something went wrong",
      );
      return;
    }

    Get.back();

    Get.toNamed(Routes.RECOVERY_CODES,
        parameters: {"recoveryCodes": resp.body.join(" ")});
  }

  disable2fa(String password) async {
    DialogHelper.loading();

    final resp = await userService.disable2fa(password: password);

    Get.back();
    Get.back();
    if (resp.hasError) {
      ToastHelper.error(
        resp.body != null ? resp.body['error'] : "Something went wrong",
      );
      return;
    }

    UserStore.to.twoFactorEnabled(false);
  }
}
