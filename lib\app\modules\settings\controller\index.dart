import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import 'package:ironlocker/app/helpers/storage.dart';
import 'package:ironlocker/app/helpers/theme.dart';
import 'package:ironlocker/app/helpers/interstitial_ad_manager.dart';
import 'package:ironlocker/app/mixins/analytics_mixin.dart';
import 'package:ironlocker/app/services/local_auth.dart';

class SettingsController extends GetxController with AnalyticsMixin {
  final themeController = Get.find<ThemeController>();
  final storage = Storage();
  var biometricEnabled = false.obs;
  final biometricAvailable = false.obs;

  @override
  void onInit() {
    super.onInit();
    trackScreenView('SettingsScreen');
    checkBiometricSupport();
  }

  void toggleBiometric(bool enabled) async {
    debugPrint('🎯 toggleBiometric called with enabled: $enabled');

    if (await LocalAuthService.hasBiometricSensor()) {
      debugPrint('✅ Biometric sensor available, showing interstitial ad');

      // Show interstitial ad and wait for it to complete
      await _showInterstitialAdAndExecute(() async {
        await _performBiometricToggle(enabled);
      });
    } else {
      debugPrint('❌ No biometric sensor available');
    }
  }

  /// Helper method to show interstitial ad and execute callback ONLY after ad interaction
  Future<void> _showInterstitialAdAndExecute(
    Future<void> Function() callback,
  ) async {
    debugPrint(
      '🎯 Loading interstitial ad - action blocked until ad interaction complete',
    );

    try {
      debugPrint('🎯 Calling loadAndShowWithCallback...');
      await InterstitialAdManager.instance.loadAndShowWithCallback(
        useTestAds: kDebugMode,
        onAdDismissedOrFailed: () async {
          debugPrint(
            '🎯 CALLBACK TRIGGERED: Ad interaction complete, executing biometric action',
          );
          try {
            await callback();
            debugPrint('✅ Biometric callback executed successfully');
          } catch (e) {
            debugPrint('❌ Error in biometric callback: $e');
          }
        },
      );
      debugPrint('🎯 loadAndShowWithCallback completed');
    } catch (e) {
      debugPrint('❌ Error in _showInterstitialAdAndExecute: $e');
      // Fallback: execute callback anyway
      await callback();
    }
  }

  /// Performs the actual biometric toggle after ad interaction
  Future<void> _performBiometricToggle(bool enabled) async {
    debugPrint('🔐 _performBiometricToggle called, requesting authentication');

    var authenticated = await LocalAuthService.authenticate();
    debugPrint('🔐 Authentication result: $authenticated');

    if (authenticated) {
      debugPrint(
        '✅ Authentication successful, updating biometric setting to: $enabled',
      );
      biometricEnabled.value = enabled;
      await storage.setItem(key: "biometricEnabled", value: enabled);

      // Track biometric setting change
      trackSecurity(
        'biometric_toggle',
        success: true,
        method: 'settings',
        parameters: {'enabled': enabled},
      );
      debugPrint('✅ Biometric setting saved successfully');
    } else {
      debugPrint('❌ Authentication failed');
      // Track failed biometric authentication
      trackSecurity(
        'biometric_auth_failed',
        success: false,
        method: 'settings_toggle',
      );
    }
  }

  Future<void> checkBiometricSupport() async {
    biometricAvailable.value = await LocalAuthService.hasBiometricSensor();
    var biometric = await storage.getItem("biometricEnabled");

    if (biometric != null) {
      biometricEnabled.value = biometric;
    }
  }
}
