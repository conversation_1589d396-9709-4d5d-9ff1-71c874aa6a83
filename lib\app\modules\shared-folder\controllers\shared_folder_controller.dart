import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/bottom%20sheets/add_locker_item.dart';
import 'package:ironlocker/app/components/bottom%20sheets/address_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/contact_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/folder_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/account_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/note_form.dart';
import 'package:ironlocker/app/components/bottom%20sheets/pcard_form.dart';
import 'package:ironlocker/app/helpers/helpers.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/modules/shared-lockers/models/shared_locker.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:ironlocker/app/services/locker.dart';
import 'package:ironlocker/app/stores/shared_lockers.dart';
import 'package:ironlocker/app/stores/user.dart';

class SharedFolderController extends GetxController {
  final searchedItems = [].obs;
  final searching = false.obs;
  final searchController = TextEditingController();
  var sharedLockersStore = Get.find<SharedLockersStore>();
  var lockerService = Get.find<LockerService>();
  late Rx<SharedLockerData> sharedLockerData;
  var folderName = "".obs;
  var folderItems = RxList();
  var folders = RxList();
  final loading = true.obs;
  final sharedLocker = "".obs;
  var error = "".obs;
  var folderId = "".obs;

  @override
  void onInit() async {
    super.onInit();
    sharedLocker.value = Get.parameters["sharedLocker"]!;
    folderName.value = Get.parameters["folderName"]!;
    sharedLockerData =
        sharedLockersStore.getSharedLocker(Get.parameters["sharedLocker"]!).obs;

    ever(folderItems, (items) {
      folders =
          folderItems
              .where((item) => item["itemType"] == "Folder")
              .toList()
              .obs;
    });

    await fetchFolderItems();
    loading(false);

    searchController.addListener(() {
      if (searchController.text.isEmpty) {
        searching(false);
        return;
      }

      if (searching.isFalse) {
        searching(true);
      }

      searchedItems.value =
          folderItems.where((item) {
            String itemName = item['name'];

            return itemName.toLowerCase().contains(
              searchController.text.toLowerCase(),
            );
          }).toList();
    });
  }

  clearSearchText() {
    searchController.clear();
  }

  Future fetchFolderItems() async {
    if (error.isNotEmpty) {
      loading(true);
    }
    error("");
    var resp = await lockerService.getFolderItems(Get.parameters["id"]!);

    if (resp.hasError) {
      if (resp.body?["error"] == "SESSION_LOCKED") {
        Get.offAllNamed(Routes.lockedLocker);
      } else if (resp.body?["error"] == "SESSION_DESTROYED" ||
          resp.body?["error"] == "ACCOUNT_DELETION_IN_PROGRESS") {
        await Helpers.clearData();
        Get.offAllNamed(Routes.signin);
      } else {
        if (loading.isTrue) {
          error("Something went wrong");
        } else {
          ToastHelper.error("Something went wrong");
        }
      }
      loading(false);
      return;
    }

    folderItems(resp.body["items"]);
    // folderName(resp.body["name"]);
    loading(false);
  }

  addFolderItem(Map item) {
    folderItems.value = [item, ...folderItems];
  }

  bool get hasWritePermission {
    if (UserStore.to.id.value == sharedLockerData.value.creator) {
      return true;
    }

    var member = sharedLockerData.value.members.firstWhere(
      (element) => element.id == UserStore.to.id.value,
    );

    if (member.permission != "READ_WRITE") {
      return false;
    }

    return true;
  }

  addLockerItem(BuildContext context) {
    var folderId = Get.parameters["id"];
    addLockerItemSheet(
      context: context,
      onPress: (itemType) {
        Navigator.of(context).pop();

        switch (itemType) {
          case "Note":
            noteFormSheet(
              context: context,
              folderId: folderId,
              sharedLockerId: sharedLocker.value,
              onCreated: addFolderItem,
            );
            break;
          case "Contact":
            contactFormSheet(
              context: context,
              folderId: folderId,
              sharedLockerId: sharedLocker.value,
              onCreated: addFolderItem,
            );
            break;
          case "Address":
            addressFormSheet(
              context: context,
              folderId: folderId,
              sharedLockerId: sharedLocker.value,
              onCreated: addFolderItem,
            );
            break;
          case "Account":
            accountFormSheet(
              context: context,
              folders: folders,
              folderId: folderId,
              sharedLockerId: sharedLocker.value,
              onCreated: addFolderItem,
            );
            break;
          case "Folder":
            folderFormSheet(
              context: context,
              folderId: folderId,
              sharedLockerId: sharedLocker.value,
              onCreated: addFolderItem,
            );
            break;
          case "PaymentCard":
            paymentCardFormSheet(
              context: context,
              folderId: folderId,
              sharedLockerId: sharedLocker.value,
              onCreated: addFolderItem,
            );
            break;
          default:
            (
              context: context,
              folderId: folderId,
              sharedLockerId: sharedLocker.value,
              onCreated: addFolderItem,
            );
        }
      },
    );
  }
}
