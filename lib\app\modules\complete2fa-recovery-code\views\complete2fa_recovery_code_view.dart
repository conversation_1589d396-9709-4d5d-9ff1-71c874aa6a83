import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/components/button.dart';
import 'package:ironlocker/app/components/input_field.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';
import '../controllers/complete2fa_recovery_code_controller.dart';

class Complete2faRecoveryCodeView
    extends GetView<Complete2faRecoveryCodeController> {
  const Complete2faRecoveryCodeView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Get.isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.orange.withValues(alpha: 0.1),
              IronLockerColors.getThemeBackground(Get.isDarkMode),
            ],
            stops: const [0.0, 0.3],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                const SizedBox(height: 20),

                // Back Button
                Align(
                  alignment: Alignment.topLeft,
                  child: Container(
                    decoration: BoxDecoration(
                      color: IronLockerColors.getThemeCard(Get.isDarkMode),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: IronLockerColors.getThemeBorder(Get.isDarkMode),
                        width: 1,
                      ),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () => Get.back(),
                        borderRadius: BorderRadius.circular(12),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          child: Icon(
                            Icons.arrow_back,
                            color: IronLockerColors.getThemeTextPrimary(
                              Get.isDarkMode,
                            ),
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 40),

                // Header Section
                _buildHeader(),
                const SizedBox(height: 60),

                // Main Content Card
                _buildMainCard(),
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // Recovery Icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(40),
            border: Border.all(
              color: Colors.orange.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Icon(Icons.restore, size: 40, color: Colors.orange),
        ),
        const SizedBox(height: 24),

        // Title
        Text(
          "Recovery Code",
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.w700,
            fontFamily: 'Jura',
            color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),

        // Subtitle
        Text(
          "Use your backup authentication",
          style: TextStyle(
            fontSize: 16,
            fontFamily: 'Jura',
            color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMainCard() {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(maxWidth: 420),
      decoration: BoxDecoration(
        color: IronLockerColors.getThemeCard(Get.isDarkMode),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: IronLockerColors.getThemeBorder(Get.isDarkMode),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: Get.isDarkMode ? 0.3 : 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            // Key Icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(30),
              ),
              child: Icon(Icons.vpn_key, size: 30, color: Colors.orange),
            ),
            const SizedBox(height: 24),

            // Instructions
            Text(
              "Enter one of your 10-digit recovery codes",
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextPrimary(Get.isDarkMode),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),

            // Helper text
            Text(
              "These codes were provided when you first enabled 2FA",
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Jura',
                color: IronLockerColors.getThemeTextSecondary(Get.isDarkMode),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),

            // Recovery Code Input
            InputField(
              controller: controller.recoveryCodeController,
              hintText: "Enter recovery code",
              keyboardType: TextInputType.text,
            ),
            const SizedBox(height: 32),

            // Continue Button
            SizedBox(
              width: double.infinity,
              child: Obx(
                () => Button(
                  text: controller.loading.value ? "Verifying..." : "Continue",
                  loading: controller.loading.value,
                  disabled:
                      controller.loading.value ||
                      controller.recoveryCodeCompleted.isFalse,
                  onPress: controller.complete2faWithRecoveryCode,
                  bgColor: Colors.orange,
                  fgColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Important Notice
            _buildImportantNotice(),
          ],
        ),
      ),
    );
  }

  Widget _buildImportantNotice() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, size: 20, color: Colors.blue),
              const SizedBox(width: 8),
              Text(
                "Important",
                style: TextStyle(
                  fontSize: 14,
                  fontFamily: 'Jura',
                  fontWeight: FontWeight.w600,
                  color: Colors.blue[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          Text(
            "Each recovery code can only be used once. After using this code, it will no longer be valid.",
            style: TextStyle(
              fontSize: 13,
              fontFamily: 'Jura',
              color: Colors.blue[700],
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }
}
