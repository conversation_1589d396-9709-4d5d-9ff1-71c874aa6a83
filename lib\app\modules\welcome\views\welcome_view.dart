import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';

import 'package:get/get.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';

import '../controllers/welcome_controller.dart';

class WelcomeView extends GetView<WelcomeController> {
  const WelcomeView({super.key});
  @override
  Widget build(BuildContext context) {
    // Ensure controller is initialized
    Get.find<WelcomeController>();
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(color: IronLockerColors.blue02),
        child: Stack(
          children: [
            Center(
              child: Container(
                padding: EdgeInsets.all(50),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.025),
                  shape: BoxShape.circle,
                ),
                child: Image.asset(
                  "assets/icons/white_app_icon.png",
                  width: 100,
                ),
              ),
            ),
            Positioned(
              bottom: 60,
              left: 0,
              right: 0,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "Current Version",
                    style: TextStyle(
                      fontSize: 16,
                      fontFamily: 'Jura',
                      color: Colors.white.withValues(alpha: 0.6),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8),
                  FutureBuilder<PackageInfo>(
                    future: PackageInfo.fromPlatform(),
                    builder: (context, snapshot) {
                      String version = "Loading...";
                      if (snapshot.hasData) {
                        version = "v${snapshot.data!.version}";
                      }
                      return Text(
                        version,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Jura',
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                        textAlign: TextAlign.center,
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
