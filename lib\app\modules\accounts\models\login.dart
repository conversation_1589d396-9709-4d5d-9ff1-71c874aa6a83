class Account {
  final String id;
  final String name;
  final String? folder;
  final String itemType;
  final String? userName;
  final String? email;
  final String? password;
  final String? website;
  final String? notes;
  final bool favourite;

  Account({
    required this.id,
    required this.name,
    required this.favourite,
    required this.itemType,
    this.userName,
    this.folder,
    this.email,
    this.website,
    this.password,
    this.notes,
  });

  static Account fromJSON(data) {
    return Account(
      id: data['_id'],
      name: data['name'],
      folder: data['folder'],
      itemType: data['itemType'],
      userName: data['userName'],
      email: data['email'],
      password: data['password'],
      website: data['website'],
      notes: data['notes'],
      favourite: data['favourite'],
    );
  }
}
