import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:ironlocker/app/controllers/ad_free_controller.dart';
import 'package:ironlocker/app/helpers/ad_helper.dart';
import 'package:ironlocker/app/helpers/ad_manager.dart';
import 'package:ironlocker/app/stores/user.dart';

/// Large banner ad widget designed for empty state screens
/// Shows when lists are empty to fill space productively
class EmptyStateAd extends StatefulWidget {
  final String emptyStateType; // e.g., 'contacts', 'accounts', 'notes'
  final String emptyMessage;
  final IconData emptyIcon;
  final bool useTestAds;

  const EmptyStateAd({
    super.key,
    required this.emptyStateType,
    required this.emptyMessage,
    required this.emptyIcon,
    this.useTestAds = false,
  });

  String get adUnitId =>
      useTestAds ? AdHelper.testBannerAdUnitId : AdHelper.bannerAdUnitId;

  @override
  State<EmptyStateAd> createState() => _EmptyStateAdState();
}

class _EmptyStateAdState extends State<EmptyStateAd> {
  BannerAd? _bannerAd;
  bool _isLoading = true;
  bool _hasError = false;
  int _retryCount = 0;
  static const int _maxRetries = 4;
  bool _isDisposed = false;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Original empty state content
            Icon(widget.emptyIcon, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              widget.emptyMessage,
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),

            // Use GetX Obx for real-time ad-free status updates
            Obx(() {
              // Hide ad section when user has premium or lifetime plan
              if (UserStore.to.plan["name"] == "premium" ||
                  UserStore.to.plan["name"] == "lifetime") {
                return const SizedBox.shrink();
              }

              // Only show ad section if not ad-free
              if (!AdFreeController.to.isAdFree) {
                return Column(
                  children: [const SizedBox(height: 32), _buildAdSection()],
                );
              }
              return const SizedBox.shrink(); // Hide ad section when ad-free
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildAdSection() {
    if (_hasError) {
      // Attractive error placeholder with retry
      return Container(
        width: double.infinity,
        constraints: const BoxConstraints(maxWidth: 350),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[200]!, width: 1),
        ),
        child: Column(
          children: [
            Icon(Icons.wifi_off_outlined, size: 32, color: Colors.grey[400]),
            const SizedBox(height: 8),
            Text(
              'Content temporarily unavailable',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Tap to retry',
              style: TextStyle(fontSize: 12, color: Colors.grey[500]),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _retryLoadAd,
                icon: const Icon(Icons.refresh, size: 16),
                label: const Text('Retry'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[100],
                  foregroundColor: Colors.grey[700],
                  elevation: 0,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
              ),
            ),
          ],
        ),
      );
    }

    if (_isLoading) {
      // Attractive loading placeholder
      return Container(
        width: double.infinity,
        height: 120,
        constraints: const BoxConstraints(maxWidth: 350),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[200]!, width: 1),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[400]!),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Loading content...',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    if (_bannerAd == null) {
      return const SizedBox.shrink();
    }

    // Display the ad with attractive styling
    return Container(
      constraints: const BoxConstraints(maxWidth: 350),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: SizedBox(
          width: _bannerAd!.size.width.toDouble(),
          height: _bannerAd!.size.height.toDouble(),
          child: AdWidget(ad: _bannerAd!),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    // Ensure ads are muted by setting audio session
    _configureSilentAds();
    // Check ad-free status and load ad accordingly
    _checkAndLoadAd();
    // No need for periodic checking - GetX handles real-time updates
  }

  void _checkAndLoadAd() async {
    // Check if user has premium or lifetime plan
    if (UserStore.to.plan["name"] == "premium" ||
        UserStore.to.plan["name"] == "lifetime") {
      debugPrint('🚫 Empty state ad hidden - user has premium/lifetime plan');
      // No need to set state - GetX Obx handles the UI updates
      return;
    }

    // Check if user has ad-free experience active using GetX controller
    if (AdFreeController.to.isAdFree) {
      debugPrint('🚫 Empty state ad hidden - user has ad-free experience');
      // No need to set state - GetX Obx handles the UI updates
      return;
    }

    // Delay ad loading slightly to let empty state render first
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!_isDisposed) _loadAd();
    });
  }

  /// Configure ads to be silent
  void _configureSilentAds() {
    // Banner ads are typically silent by default, but we can ensure this
    // by configuring the audio session if needed
    debugPrint('Configuring empty state ads to be silent');
  }

  void _loadAd() {
    if (_isDisposed) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      debugPrint(
        'Loading empty state ad for ${widget.emptyStateType} (attempt ${_retryCount + 1}/$_maxRetries)',
      );

      // Create silent banner ad for empty state placement
      final bannerAd = BannerAd(
        size: AdSize.mediumRectangle, // Larger size for empty states
        adUnitId: widget.adUnitId,
        request:
            AdManager.instance
                .getOptimizedAdRequest(), // Banner ads are silent by default
        listener: BannerAdListener(
          onAdLoaded: (ad) {
            if (_isDisposed) {
              ad.dispose();
              return;
            }

            debugPrint(
              'Empty state ad loaded successfully for ${widget.emptyStateType}',
            );

            setState(() {
              _bannerAd = ad as BannerAd;
              _isLoading = false;
              _hasError = false;
              _retryCount = 0;
            });
          },
          onAdFailedToLoad: (ad, error) {
            ad.dispose();

            if (_isDisposed) return;

            debugPrint('Empty state ad failed to load: ${error.message}');

            _retryCount++;

            // Report error to Crashlytics
            AdManager.reportAdError(
              adType: 'empty_state',
              errorMessage: error.message,
              errorCode: error.code,
              retryAttempt: _retryCount,
              additionalData: {
                'ad_unit_id': widget.adUnitId,
                'empty_state_type': widget.emptyStateType,
                'placement': 'empty_state',
                'use_test_ads': widget.useTestAds,
              },
            );

            // Enhanced retry logic for empty state ads
            bool shouldRetry =
                _retryCount < _maxRetries &&
                (error.message.contains('JavascriptEngine') ||
                    error.message.contains('No ad to show') ||
                    error.message.contains('Network error') ||
                    error.code == 0 || // No fill
                    error.code == 2 || // Network error
                    error.code ==
                        3 // No fill
                        );

            if (shouldRetry) {
              int delaySeconds = _retryCount * 2; // Progressive delay
              debugPrint('Retrying empty state ad in $delaySeconds seconds...');
              Future.delayed(Duration(seconds: delaySeconds), () {
                if (!_isDisposed) _loadAd();
              });
            } else {
              setState(() {
                _isLoading = false;
                _hasError = true;
              });
            }
          },
        ),
      );

      bannerAd.load();
    } catch (e) {
      debugPrint('Error initializing empty state ad: $e');

      _retryCount++;

      // Report initialization error
      AdManager.reportAdError(
        adType: 'empty_state_init',
        errorMessage: e.toString(),
        errorCode: -1,
        retryAttempt: _retryCount,
        additionalData: {
          'ad_unit_id': widget.adUnitId,
          'empty_state_type': widget.emptyStateType,
          'error_type': 'initialization',
        },
      );

      if (_retryCount < _maxRetries) {
        Future.delayed(Duration(seconds: _retryCount * 2), () {
          if (!_isDisposed) _loadAd();
        });
      } else {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  void _retryLoadAd() {
    _retryCount = 0; // Reset retry count for manual retry
    _loadAd();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _bannerAd?.dispose();
    super.dispose();
  }
}
