import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/routes/app_pages.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:ironlocker/app/services/subscription_service.dart';
import 'package:ironlocker/app/helpers/toast.dart';
import 'package:ironlocker/app/stores/user.dart';

/// Simple upgrade button that shows subscription options
class UpgradeButton extends StatelessWidget {
  const UpgradeButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // Hide for premium/lifetime users
      if (UserStore.to.plan["name"] == "premium" ||
          UserStore.to.plan["name"] == "lifetime") {
        return const SizedBox.shrink();
      }

      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.blue.withValues(alpha: 0.1),
              Colors.purple.withValues(alpha: 0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.blue.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.amber,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.star,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Upgrade to Premium",
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color:
                                Get.isDarkMode ? Colors.white : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          "Unlock unlimited shared lockers",
                          style: TextStyle(
                            fontSize: 13,
                            color:
                                Get.isDarkMode
                                    ? Colors.grey[400]
                                    : Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => _showSubscriptionOptions(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    elevation: 2,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        "Choose Plan",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Icon(Icons.arrow_forward, size: 18),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildFeatureBadge(String text) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
        decoration: BoxDecoration(
          color: Colors.blue.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: Colors.blue.withValues(alpha: 0.3),
            width: 0.5,
          ),
        ),
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 11,
            fontWeight: FontWeight.w500,
            color: Colors.blue[700],
          ),
        ),
      ),
    );
  }

  void _showSubscriptionOptions(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const SubscriptionDialog(),
    );
  }
}

/// Dialog that shows subscription options
class SubscriptionDialog extends StatefulWidget {
  const SubscriptionDialog({super.key});

  @override
  State<SubscriptionDialog> createState() => _SubscriptionDialogState();
}

class _SubscriptionDialogState extends State<SubscriptionDialog> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Icon(Icons.star, color: Colors.amber, size: 28),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    "Choose Your Plan",
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Features
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Get.isDarkMode ? Colors.grey[800] : Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Premium Features:",
                    style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  _buildFeature("✓ Unlimited shared lockers"),
                  _buildFeature("✓ Ad-free experience"),
                  _buildFeature("✓ Priority sync"),
                  _buildFeature("✓ Premium support"),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Subscription options
            Obx(() {
              final offering = SubscriptionService.to.currentOffering;

              if (offering == null) {
                return _buildLoadingState();
              }

              if (offering.availablePackages.isEmpty) {
                return _buildNoProductsState();
              }

              // Debug: Print package information
              debugPrint(
                '📦 Available packages: ${offering.availablePackages.length}',
              );
              for (var package in offering.availablePackages) {
                debugPrint('📦 Package: ${package.identifier}');
                debugPrint('📦 Product ID: ${package.storeProduct.identifier}');
                debugPrint('📦 Product Title: ${package.storeProduct.title}');
                debugPrint(
                  '📦 Product Price: ${package.storeProduct.priceString}',
                );
                debugPrint('📦 Package Type: ${package.packageType}');
              }

              return Column(
                children:
                    offering.availablePackages.map((package) {
                      return _buildSubscriptionOption(package);
                    }).toList(),
              );
            }),

            if (_isLoading) ...[
              const SizedBox(height: 16),
              const CircularProgressIndicator(),
              const SizedBox(height: 8),
              const Text("Processing purchase..."),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFeature(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 14,
          color: Get.isDarkMode ? Colors.grey[300] : Colors.grey[700],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Column(
      children: [
        const CircularProgressIndicator(),
        const SizedBox(height: 16),
        Text(
          "Loading subscription options...",
          style: TextStyle(
            color: Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          "Make sure you have configured RevenueCat API keys",
          style: TextStyle(
            fontSize: 12,
            color: Get.isDarkMode ? Colors.grey[500] : Colors.grey[500],
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            TextButton(
              onPressed: () => SubscriptionService.to.loadOfferings(),
              child: const Text("Retry"),
            ),
            TextButton(
              onPressed: () => _showDebugInfo(),
              child: const Text("Debug Info"),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNoProductsState() {
    return Column(
      children: [
        Icon(Icons.error_outline, size: 48, color: Colors.orange),
        const SizedBox(height: 16),
        Text(
          "No subscription products available",
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Text(
          "Products are not configured in Google Play Console",
          style: TextStyle(
            fontSize: 14,
            color: Get.isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            TextButton(
              onPressed: () => SubscriptionService.to.loadOfferings(),
              child: const Text("Retry"),
            ),
            TextButton(
              onPressed: () => _showSetupGuide(),
              child: const Text("Setup Guide"),
            ),
          ],
        ),
      ],
    );
  }

  void _showDebugInfo() {
    final offering = SubscriptionService.to.currentOffering;

    Get.dialog(
      AlertDialog(
        title: const Text("RevenueCat Debug Info"),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Service Status: ${SubscriptionService.to.isLoading ? 'Loading' : 'Ready'}",
              ),
              const SizedBox(height: 8),
              Text(
                "Current Offering: ${offering != null ? 'Available' : 'Not loaded'}",
              ),
              if (offering != null) ...[
                const SizedBox(height: 8),
                Text("Offering ID: ${offering.identifier}"),
                const SizedBox(height: 8),
                Text(
                  "Available Packages: ${offering.availablePackages.length}",
                ),
                const SizedBox(height: 8),
                ...offering.availablePackages.map(
                  (package) => Padding(
                    padding: const EdgeInsets.only(left: 16, bottom: 4),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text("• Package: ${package.identifier}"),
                        Text(
                          "  Product ID: ${package.storeProduct.identifier}",
                        ),
                        Text("  Price: ${package.storeProduct.priceString}"),
                        const SizedBox(height: 4),
                      ],
                    ),
                  ),
                ),
              ],
              const SizedBox(height: 8),
              const Text("Check console logs for detailed error messages."),
              const SizedBox(height: 8),
              const Text(
                "Make sure your RevenueCat API keys are configured and Google Play products are active.",
              ),
            ],
          ),
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text("Close")),
        ],
      ),
    );
  }

  void _showSetupGuide() {
    Get.dialog(
      AlertDialog(
        title: const Text("Google Play Console Setup"),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                "To fix this error, you need to:",
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 12),
              const Text("1. Go to Google Play Console"),
              const Text(
                "2. Navigate to: Your App → Monetization → Products → In-app products",
              ),
              const Text("3. Create subscription products with IDs:"),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Get.isDarkMode ? Colors.grey[800] : Colors.grey[100],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "• monthly_premium",
                      style: TextStyle(fontFamily: 'monospace'),
                    ),
                    Text(
                      "• yearly_premium",
                      style: TextStyle(fontFamily: 'monospace'),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
              const Text("4. Set status to 'Active'"),
              const Text("5. Make sure product IDs match RevenueCat exactly"),
              const SizedBox(height: 12),
              const Text(
                "Note: Changes may take a few hours to propagate.",
                style: TextStyle(fontStyle: FontStyle.italic),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text("Close")),
        ],
      ),
    );
  }

  Widget _buildSubscriptionOption(Package package) {
    final isPopular = package.packageType == PackageType.annual;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _isLoading ? null : () => _purchasePackage(package),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(
                color:
                    isPopular
                        ? Colors.blue
                        : (Get.isDarkMode
                            ? Colors.grey[700]!
                            : Colors.grey[300]!),
                width: isPopular ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
              color:
                  isPopular
                      ? (Get.isDarkMode
                          ? Colors.blue.withValues(alpha: 0.1)
                          : Colors.blue.withValues(alpha: 0.05))
                      : null,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            _getSubscriptionPeriod(package),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: isPopular ? Colors.blue : null,
                            ),
                          ),
                          if (isPopular) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.blue,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                "POPULAR",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getPackageDescription(package),
                        style: TextStyle(
                          fontSize: 12,
                          color:
                              Get.isDarkMode
                                  ? Colors.grey[400]
                                  : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      package.storeProduct.priceString,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (package.packageType == PackageType.annual) ...[
                      Text(
                        "Save 30%",
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.green[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getSubscriptionPeriod(Package package) {
    switch (package.packageType) {
      case PackageType.monthly:
        return 'Monthly';
      case PackageType.annual:
        return 'Yearly';
      case PackageType.sixMonth:
        return '6 Months';
      case PackageType.threeMonth:
        return '3 Months';
      case PackageType.weekly:
        return 'Weekly';
      default:
        return 'Subscription';
    }
  }

  String _getPackageDescription(Package package) {
    switch (package.packageType) {
      case PackageType.monthly:
        return "Billed monthly";
      case PackageType.annual:
        return "Billed yearly • Best value";
      case PackageType.sixMonth:
        return "Billed every 6 months";
      case PackageType.threeMonth:
        return "Billed every 3 months";
      default:
        return "Subscription";
    }
  }

  Future<void> _purchasePackage(Package package) async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint('🛒 Starting purchase for package: ${package.identifier}');
      debugPrint('🛒 Package type: ${package.packageType}');
      debugPrint('🛒 Product ID: ${package.storeProduct.identifier}');
      debugPrint('🛒 Price: ${package.storeProduct.priceString}');

      final success = await SubscriptionService.to.purchasePackage(package);

      debugPrint('🛒 Purchase result: $success');

      if (success) {
        debugPrint('✅ Purchase successful, closing dialog');
        Future.delayed(Duration(seconds: 3), () {
          Get.offAllNamed(Routes.WELCOME);
        });
      } else {
        debugPrint('❌ Purchase failed');
        ToastHelper.error("Purchase failed. Please try again.");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Purchase error: $e');
      debugPrint('❌ Stack trace: $stackTrace');

      String errorMessage = "Something went wrong. Please try again.";

      if (e.toString().contains('ProductNotAvailableForPurchaseError')) {
        errorMessage =
            "Product not available. Check Google Play Console setup.";
      } else if (e.toString().contains('UserCancelledError')) {
        errorMessage = "Purchase cancelled by user.";
      } else if (e.toString().contains('PaymentPendingError')) {
        errorMessage = "Payment is pending. Please wait.";
      } else if (e.toString().contains('PurchaseNotAllowedError')) {
        errorMessage = "Purchase not allowed. Check your Google Play account.";
      }

      ToastHelper.error(errorMessage);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
