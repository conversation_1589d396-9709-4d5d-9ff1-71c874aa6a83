import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:ironlocker/app/constants/subscription_config.dart';

/// Simplified service to handle RevenueCat purchase initiation only
/// Backend handles subscription status management
class SubscriptionService extends GetxService {
  static SubscriptionService get to => Get.find<SubscriptionService>();

  // Only track offerings and loading state for purchase flow
  final _currentOffering = Rxn<Offering>();
  final _isLoading = false.obs;

  // Getters
  Offering? get currentOffering => _currentOffering.value;
  bool get isLoading => _isLoading.value;

  // Reactive getters for UI
  Rxn<Offering> get currentOfferingRx => _currentOffering;
  RxBool get isLoadingRx => _isLoading;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeRevenueCat();
  }

  /// Initialize RevenueCat SDK
  Future<void> _initializeRevenueCat() async {
    try {
      debugPrint('🔄 Initializing RevenueCat...');

      // Configure RevenueCat
      PurchasesConfiguration configuration;
      if (defaultTargetPlatform == TargetPlatform.android) {
        configuration = PurchasesConfiguration(SubscriptionConfig.googleApiKey);
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        configuration =
            PurchasesConfiguration(SubscriptionConfig.appleApiKey)
              ..appUserID = null
              ..purchasesAreCompletedBy =
                  const PurchasesAreCompletedByRevenueCat();
      } else {
        debugPrint('❌ Unsupported platform for RevenueCat');
        return;
      }

      await Purchases.configure(configuration);

      // Load initial offerings
      await loadOfferings();

      debugPrint('✅ RevenueCat initialized successfully');
    } catch (e) {
      debugPrint('❌ RevenueCat initialization failed: $e');
    }
  }

  /// Load available subscription offerings
  Future<void> loadOfferings() async {
    try {
      debugPrint('🔄 Loading subscription offerings...');

      final offerings = await Purchases.getOfferings();

      if (offerings.current != null) {
        _currentOffering.value = offerings.current;
        debugPrint(
          '✅ Loaded ${offerings.current!.availablePackages.length} subscription packages',
        );
      } else {
        debugPrint('⚠️ No current offering available');
      }
    } catch (e) {
      debugPrint('❌ Failed to load offerings: $e');
    }
  }

  /// Purchase a subscription package
  Future<bool> purchasePackage(Package package) async {
    try {
      _isLoading.value = true;
      debugPrint(
        '🛒 Attempting to purchase: ${package.storeProduct.identifier}',
      );

      final customerInfo = await Purchases.purchasePackage(package);

      // Check if purchase was successful
      final hasActivePremium = customerInfo.entitlements.active.containsKey(
        SubscriptionConfig.premiumEntitlementId,
      );

      if (hasActivePremium) {
        debugPrint('✅ Purchase successful!');
        return true;
      } else {
        debugPrint('❌ Purchase completed but premium not active');
        return false;
      }
    } on PurchasesErrorCode catch (e) {
      debugPrint('❌ Purchase failed with error: $e');
      _handlePurchaseError(e);
      return false;
    } catch (e) {
      debugPrint('❌ Unexpected purchase error: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Handle purchase errors
  void _handlePurchaseError(PurchasesErrorCode errorCode) {
    switch (errorCode) {
      case PurchasesErrorCode.purchaseCancelledError:
        debugPrint('ℹ️ User cancelled purchase');
        break;
      case PurchasesErrorCode.purchaseNotAllowedError:
        debugPrint('❌ Purchase not allowed');
        break;
      case PurchasesErrorCode.purchaseInvalidError:
        debugPrint('❌ Invalid purchase');
        break;
      case PurchasesErrorCode.productNotAvailableForPurchaseError:
        debugPrint('❌ Product not available');
        break;
      case PurchasesErrorCode.networkError:
        debugPrint('❌ Network error during purchase');
        break;
      default:
        debugPrint('❌ Unknown purchase error: $errorCode');
    }
  }

  /// Restore previous purchases
  Future<bool> restorePurchases() async {
    try {
      _isLoading.value = true;
      debugPrint('🔄 Restoring purchases...');

      final customerInfo = await Purchases.restorePurchases();

      final hasActivePremium = customerInfo.entitlements.active.containsKey(
        SubscriptionConfig.premiumEntitlementId,
      );

      if (hasActivePremium) {
        debugPrint('✅ Purchases restored successfully');
        return true;
      } else {
        debugPrint('ℹ️ No active purchases to restore');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Failed to restore purchases: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Identify user to RevenueCat (call after user signs in)
  Future<void> identifyUser(String userId) async {
    try {
      debugPrint('🔄 Identifying user to RevenueCat: $userId');

      final logInResult = await Purchases.logIn(userId);
      // Note: We don't track customer info since backend manages subscription status

      debugPrint('✅ User identified successfully');
    } catch (e) {
      debugPrint('❌ Failed to identify user: $e');
    }
  }

  /// Log out user from RevenueCat (call during logout)
  Future<void> logoutUser() async {
    try {
      debugPrint('🔄 Logging out user from RevenueCat');

      await Purchases.logOut();
      // Note: We don't track customer info since backend manages subscription status

      debugPrint('✅ User logged out successfully');
    } catch (e) {
      debugPrint('❌ Failed to logout user: $e');
    }
  }

  /// Get formatted price for a package
  String getFormattedPrice(Package package) {
    return package.storeProduct.priceString;
  }

  /// Debug method to check product availability
  Future<void> debugProductAvailability() async {
    try {
      debugPrint('🔍 Checking product availability...');

      // Check if RevenueCat is configured
      final customerInfo = await Purchases.getCustomerInfo();
      debugPrint(
        '✅ RevenueCat is configured, user ID: ${customerInfo.originalAppUserId}',
      );

      // Check offerings
      final offerings = await Purchases.getOfferings();
      debugPrint('📦 Total offerings: ${offerings.all.length}');
      debugPrint(
        '📦 Current offering: ${offerings.current?.identifier ?? "None"}',
      );

      if (offerings.current != null) {
        final packages = offerings.current!.availablePackages;
        debugPrint('📦 Available packages: ${packages.length}');

        for (var package in packages) {
          debugPrint('📦 Package: ${package.identifier}');
          debugPrint('   Product ID: ${package.storeProduct.identifier}');
          debugPrint('   Title: ${package.storeProduct.title}');
          debugPrint('   Price: ${package.storeProduct.priceString}');
          debugPrint('   Type: ${package.packageType}');
        }
      } else {
        debugPrint('❌ No current offering available');
      }
    } catch (e) {
      debugPrint('❌ Debug check failed: $e');
    }
  }

  /// Get subscription period for a package
  String getSubscriptionPeriod(Package package) {
    // Use package type to determine period
    switch (package.packageType) {
      case PackageType.monthly:
        return 'Monthly';
      case PackageType.annual:
        return 'Yearly';
      case PackageType.sixMonth:
        return '6 Months';
      case PackageType.threeMonth:
        return '3 Months';
      case PackageType.weekly:
        return 'Weekly';
      default:
        return 'Subscription';
    }
  }

  @override
  void onClose() {
    // Clean up if needed
    super.onClose();
  }
}
