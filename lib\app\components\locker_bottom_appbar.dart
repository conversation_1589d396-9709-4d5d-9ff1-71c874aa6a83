import 'package:flutter/material.dart';

class LockerBottomAppBar extends StatefulWidget {
  const LockerBottomAppBar({super.key, required this.onPageChange});
  final Function(int page) onPageChange;

  @override
  State<LockerBottomAppBar> createState() => _LockerBottomAppBarState();
}

class _LockerBottomAppBarState extends State<LockerBottomAppBar> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.home), label: "Locker items"),
        BottomNavigationBarItem(icon: Icon(Icons.settings), label: "Settings"),
      ],
      currentIndex: _currentIndex,
      onTap: (index) {
        setState(() {
          _currentIndex = index;
          widget.onPageChange(index);
        });
      },
    );
  }
}
