class Contact {
  final String id;
  final String? folder;
  final String name;
  final String? phoneNumber;
  final String itemType;
  final String? notes;
  final bool favourite;

  Contact({
    required this.id,
    required this.name,
    required this.favourite,
    required this.itemType,
    this.folder,
    this.phoneNumber,
    this.notes,
  });

  static Contact fromJSON(data) {
    return Contact(
      id: data['_id'],
      name: data['name'],
      folder: data['folder'],
      itemType: data['itemType'],
      phoneNumber: data['phoneNumber'],
      notes: data['notes'],
      favourite: data['favourite'],
    );
  }
}
