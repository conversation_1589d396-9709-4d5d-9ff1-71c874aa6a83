class AddressModel {
  final String id;
  final String name;
  final String? folder;
  final String? street;
  final String? city;
  final String? state;
  final String? country;
  final String? zipCode;
  final String itemType;
  final String? notes;
  final bool favourite;

  AddressModel({
    required this.id,
    required this.name,
    required this.favourite,
    required this.itemType,
    this.folder,
    this.street,
    this.state,
    this.city,
    this.country,
    this.zipCode,
    this.notes,
  });

  static AddressModel fromJSON(data) {
    return AddressModel(
      id: data['_id'],
      name: data['name'],
      folder: data['folder'],
      street: data['street'],
      state: data['state'],
      city: data['city'],
      country: data['country'],
      zipCode: data['zipCode'],
      itemType: data['itemType'],
      notes: data['notes'],
      favourite: data['favourite'],
    );
  }
}
