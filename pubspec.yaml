name: ironlocker
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.2.1+60

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  firebase_core: ^3.15.1
  firebase_crashlytics: ^4.3.9
  firebase_analytics: ^11.5.2
  get: ^4.7.2
  gap: ^3.0.1
  fluttertoast: ^8.2.12
  otp_text_field: ^1.1.3
  form_validator: ^2.1.1
  smooth_page_indicator: ^1.2.1
  flutter_spinkit: ^5.2.1
  google_fonts: ^6.2.1
  responsive_grid: ^2.4.4
  dropdown_search: ^6.0.2
  get_storage: ^2.1.1
  flutter_secure_storage: ^9.2.4
  path_provider: ^2.1.5
  device_info_plus: ^11.5.0
  package_info_plus: ^8.1.2
  intl: ^0.20.2
  pointycastle: ^4.0.0
  bson: ^5.0.7
  asn1lib: ^1.6.5
  basic_utils: ^5.8.2
  local_auth: ^2.3.0
  upgrader: ^11.4.0
  google_mobile_ads: ^6.0.0
  purchases_flutter: ^8.1.3
  flutter_svg: ^2.0.10+1
  url_launcher: ^6.3.1
  timeago: ^3.7.1
  uuid: ^4.5.1
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  socket_io_client: ^3.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true
  assets:
    - assets/icons/
    - assets/images/

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app-icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  remove_alpha_ios: true
  web:
    generate: true
    image_path: "assets/icons/app-icon.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/icons/app-icon.png"
    icon_size: 48 # min:48, max:256, default: 48
