import 'dart:io';
import 'package:path_provider/path_provider.dart';

Future<File?> writeRecoveryCodesToFile(List<String> recoveryCodes) async {
  var downloadDIr = await getDownloadsDirectory();

  var storageDir = await getExternalStorageDirectory();

  late String path;

  if (downloadDIr != null) {
    path = downloadDIr.path;
  } else if (storageDir != null) {
    path = storageDir.path;
  }

  final file = File('$path/ironlocker-recovery-codes.txt');
  await file.writeAsString(recoveryCodes.join('\n'));
  return file;
}
