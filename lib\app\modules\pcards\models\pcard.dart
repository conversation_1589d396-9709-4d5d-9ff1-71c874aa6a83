class Pcard {
  final String id;
  final String name;
  final String? folder;
  final String itemType;
  final String? cardHolderName;
  final String? cardNumber;
  final String? expiryMonth;
  final String? expiryYear;
  final String? cvv;
  final String? notes;
  final bool favourite;

  Pcard({
    required this.id,
    required this.name,
    required this.favourite,
    required this.itemType,
    this.cardHolderName,
    this.folder,
    this.cardNumber,
    this.expiryYear,
    this.expiryMonth,
    this.cvv,
    this.notes,
  });

  static Pcard fromJSON(data) {
    return Pcard(
      id: data['_id'],
      name: data['name'],
      folder: data['folder'],
      itemType: data['itemType'],
      cardHolderName: data['cardHolderName'],
      cardNumber: data['cardNumber'],
      expiryMonth: data['expiryMonth'],
      expiryYear: data['expiryYear'],
      cvv: data['expiryYear'],
      notes: data['notes'],
      favourite: data['favourite'],
    );
  }
}
