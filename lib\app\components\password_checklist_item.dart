import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class PasswordChecklistItem extends StatelessWidget {
  const PasswordChecklistItem({
    super.key,
    required this.condition,
    required this.conditionMet,
  });
  final String condition;
  final bool conditionMet;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            Icons.close,
            size: 14,
            color: conditionMet ? Colors.green[500] : Colors.red[500],
          ),
          const Gap(5),
          Text(
            condition,
            style: TextStyle(
              fontSize: 14,
              color: conditionMet ? Colors.green[500] : Colors.red[500],
            ),
          ),
        ],
      ),
    );
  }
}
