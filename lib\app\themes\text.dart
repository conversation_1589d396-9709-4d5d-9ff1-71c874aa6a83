import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTextTheme {
  static final lightTheme = const TextTheme().copyWith(
    titleLarge: GoogleFonts.jura(fontSize: 24, fontWeight: FontWeight.w600),
    titleMedium: GoogleFonts.jura(fontSize: 18, fontWeight: FontWeight.w600),
    titleSmall: GoogleFonts.jura(fontSize: 15, fontWeight: FontWeight.w600),
    labelLarge: GoogleFonts.jura(fontSize: 16, fontWeight: FontWeight.w600),
    labelMedium: GoogleFonts.jura(fontSize: 14, fontWeight: FontWeight.w600),
    labelSmall: GoogleFonts.jura(fontSize: 12, fontWeight: FontWeight.w600),
    bodyLarge: GoogleFonts.jura(fontSize: 18, fontWeight: FontWeight.w400),
    bodyMedium: GoogleFonts.jura(fontSize: 16, fontWeight: FontWeight.w400),
    bodySmall: GoogleFonts.jura(
      fontSize: 14,
      fontWeight: FontWeight.w400,
      height: 1.7,
    ),
  );

  static final darkTheme = const TextTheme().copyWith(
    titleLarge: GoogleFonts.jura(fontSize: 24, fontWeight: FontWeight.w600),
    titleMedium: GoogleFonts.jura(fontSize: 18, fontWeight: FontWeight.w600),
    titleSmall: GoogleFonts.jura(fontSize: 15, fontWeight: FontWeight.w600),
    labelLarge: GoogleFonts.jura(fontSize: 16, fontWeight: FontWeight.w600),
    labelMedium: GoogleFonts.jura(fontSize: 14, fontWeight: FontWeight.w600),
    labelSmall: GoogleFonts.jura(fontSize: 12, fontWeight: FontWeight.w600),
    bodyLarge: GoogleFonts.jura(fontSize: 18, fontWeight: FontWeight.w400),
    bodyMedium: GoogleFonts.jura(fontSize: 16, fontWeight: FontWeight.w400),
    bodySmall: GoogleFonts.jura(
      fontSize: 14,
      fontWeight: FontWeight.w400,
      height: 1.7,
    ),
  );
}
