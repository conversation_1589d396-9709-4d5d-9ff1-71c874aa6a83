import 'package:get/get.dart';
import 'package:ironlocker/app/helpers/storage.dart';

class UserStore extends GetxController {
  var name = "".obs;
  var id = "".obs;
  var email = "".obs;
  var sessionId = "".obs;
  var twoFactorEnabled = RxBool(false);

  var refreshToken = "".obs;
  var accessToken = "".obs;
  var plan = RxMap<String, dynamic>();

  static UserStore get to => Get.find();

  @override
  void onInit() {
    super.onInit();
    final storage = Storage();

    refreshToken(storage.getItem("refreshToken") ?? "");
    accessToken(storage.getItem("accessToken") ?? "");

    ever(refreshToken, (callback) => {});
  }
}
