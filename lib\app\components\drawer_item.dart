import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ironlocker/app/constants/ironlocker_colors.dart';

class DrawerItem extends StatelessWidget {
  const DrawerItem({
    super.key,
    required this.title,
    required this.icon,
    required this.onPress,
    this.padding,
    this.selected = false,
  });
  final String title;
  final IconData icon;
  final VoidCallback onPress;
  final bool selected;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPress,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding:
              padding ??
              const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color:
                selected
                    ? IronLockerColors.blue02.withValues(alpha: 0.15)
                    : Colors.transparent,
            borderRadius: BorderRadius.circular(12),
            border:
                selected
                    ? Border.all(
                      color: IronLockerColors.blue02.withValues(alpha: 0.3),
                      width: 1,
                    )
                    : null,
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color:
                      selected
                          ? IronLockerColors.blue02
                          : (Get.isDarkMode
                              ? Colors.grey[700]?.withValues(alpha: 0.5)
                              : Colors.grey[100]),
                  borderRadius: BorderRadius.circular(8),
                  boxShadow:
                      selected
                          ? [
                            BoxShadow(
                              color: IronLockerColors.blue02.withValues(
                                alpha: 0.3,
                              ),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ]
                          : null,
                ),
                child: Icon(
                  icon,
                  size: 18,
                  color:
                      selected
                          ? Colors.white
                          : (Get.isDarkMode
                              ? Colors.grey[300]
                              : Colors.grey[700]),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: selected ? FontWeight.w600 : FontWeight.w500,
                    color:
                        selected
                            ? IronLockerColors.blue01
                            : (Get.isDarkMode ? Colors.white : Colors.black87),
                    fontFamily: 'Jura',
                  ),
                ),
              ),
              if (selected)
                Container(
                  width: 4,
                  height: 20,
                  decoration: BoxDecoration(
                    color: IronLockerColors.blue02,
                    borderRadius: BorderRadius.circular(2),
                    boxShadow: [
                      BoxShadow(
                        color: IronLockerColors.blue02.withValues(alpha: 0.5),
                        blurRadius: 4,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                ),
              if (selected)
                Container(
                  width: 4,
                  height: 20,
                  decoration: BoxDecoration(
                    color: IronLockerColors.yellow,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
